/**
 * Service 客服组件
 * 
 * 漂浮在页面右下角的客服按钮，点击可跳转到微信客服
 */
import React from 'react'
import { View, Text, Image } from '@tarojs/components'
import { makePhoneCall } from '@/utils/helpers/wechat'
import CacheManager, { CACHE_KEYS } from '@/utils/cache'
// 导入客服图标
import headsetIcon from '@/assets/images/common-icon/headset_outline.png'
import { ServiceProps } from './types'
import './index.scss'

const Service: React.FC<ServiceProps> = ({
  className = '',
  visible = true
}) => {

  // 处理客服按钮点击
  const handleServiceClick = () => {
    try {
      const hotlinePhone = CacheManager.getCache<CommonAPI.MiniConfig>(CACHE_KEYS.MINI_CONFIG)?.hotlinePhone || ''
      makePhoneCall(hotlinePhone)
    } catch (error) {
      console.error('客服功能异常:', error)
    }
  }

  // 如果不可见，则不渲染
  if (!visible) {
    return null
  }

  return (
    <View
      className={`service-widget ${visible ? 'service-widget--visible' : 'service-widget--hidden'} ${className}`}
      onClick={handleServiceClick}
    >
      <Image
        className='service-widget__icon'
        src={headsetIcon}
        mode='aspectFit'
      />
      <Text className='service-widget__text'>客服</Text>
    </View>
  )
}

// 导出组件
export default Service

// 导出类型
export type { ServiceProps } from './types'
