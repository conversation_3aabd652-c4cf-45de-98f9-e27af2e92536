// 律师登录相关组件的公共样式

.account-login,
.phone-login,
.register-form,
.form-section {
  .form-header {
    text-align: center;
    margin-bottom: 60rpx;

    .form-title {
      display: block;
      font-size: 36rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 16rpx;
    }

    .form-subtitle {
      display: block;
      font-size: 26rpx;
      color: #666666;
    }
  }

  .form-group {
    margin-bottom: 40rpx;

    .input-label {
      font-size: 28rpx;
      color: #333333;
      margin-bottom: 16rpx;
      font-weight: 500;

      .required-mark {
        color: #ff4757;
        margin-left: 4rpx;
      }

      .optional-mark {
        color: #999999;
        font-size: 24rpx;
        font-weight: 400;
        margin-left: 8rpx;
      }
    }

    .form-input {
      width: 100%;
      height: 88rpx;
      background: #f8f9fa;
      border: 2rpx solid #e9ecef;
      border-radius: 12rpx;
      padding: 0 24rpx;
      font-size: 28rpx;
      color: #333333;
      box-sizing: border-box;

      &:focus {
        border-color: #BD8A4F;
        background: #ffffff;
      }
    }

    // 地区选择器样式
    .area-select-trigger {
      width: 100%;
      height: 88rpx;
      background: #f8f9fa;
      border: 2rpx solid #e9ecef;
      border-radius: 12rpx;
      padding: 0 24rpx;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .area-select-text {
        font-size: 28rpx;
        color: #333333;
        flex: 1;

        &.placeholder {
          color: #999999;
        }
      }

      .area-select-arrow {
        font-size: 32rpx;
        color: #999999;
        transform: rotate(90deg);
        margin-left: 16rpx;
      }

      &:active {
        border-color: #BD8A4F;
        background: #ffffff;
      }
    }

    .password-input-group {
      position: relative;
      display: flex;
      align-items: center;

      .password-input {
        flex: 1;
        padding-right: 80rpx; // 为眼睛图标留出空间
      }

      .password-toggle {
        position: absolute;
        right: 24rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;


        .eye-icon {
          width: 32rpx;
          height: 32rpx;
          opacity: 0.6;
          transition: opacity 0.3s ease;

          &--active {
            opacity: 1;
            filter: brightness(1.2);
          }
        }

        &:active .eye-icon {
          opacity: 1;
        }
      }
    }

    .code-input-group {
      display: flex;
      align-items: center;
      gap: 20rpx;

      .code-input {
        flex: 1;
      }

      .code-btn {
        width: 200rpx;
        height: 88rpx;
        background: #BD8A4F;
        color: #ffffff;
        font-size: 26rpx;
        border-radius: 12rpx;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        &:disabled {
          background: #cccccc;
        }

        &::after {
          border: none;
        }
      }
    }
  }

  // 协议确认区域
  .agreement-section {
    margin: 40rpx 0 20rpx;

    .agreement-item {
      display: flex;
      align-items: flex-start;
      gap: 16rpx;

      .agreement-checkbox {
        margin-top: 4rpx;
        transform: scale(0.9);
      }

      .agreement-text {
        flex: 1;
        line-height: 1.6;
        font-size: 26rpx;
        color: #666666;

        .agreement-prefix,
        .agreement-separator {
          color: #666666;
        }

        .agreement-link {
          color: #BD8A4F;
          text-decoration: underline;
          margin: 0 4rpx;

          &:active {
            color: #a67a42;
          }
        }
      }
    }
  }

  .login-btn,
  .register-btn {
    width: 100%;
    height: 88rpx;
    background: #BD8A4F;
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 500;
    border-radius: 44rpx;
    border: none;
    margin: 60rpx 0 40rpx 0;

    &:disabled {
      background: #cccccc;
    }

    &::after {
      border: none;
    }
  }

  .register-tip,
  .login-tip {
    text-align: center;

    .tip-text {
      font-size: 26rpx;
      color: #666666;
    }

    .register-link,
    .login-link {
      font-size: 26rpx;
      color: #BD8A4F;
      margin-left: 10rpx;
      text-decoration: underline;
    }
  }
}
