/**
 * 手机号验证码输入组件
 */
import React, { useState, useEffect } from 'react'
import { View, Text, Input, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { commonApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
import type { PhoneCodeInputProps } from '../../types'
import './index.scss'

const PhoneCodeInput: React.FC<PhoneCodeInputProps> = ({
  phoneValue,
  codeValue,
  onPhoneChange,
  onCodeChange,
  className = ''
}) => {
  // 验证码发送状态
  const [codeLoading, setCodeLoading] = useState(false)
  // 倒计时状态
  const [countdown, setCountdown] = useState(0)

  // 倒计时效果
  useEffect(() => {
    let timer: number
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1)
      }, 1000)
    }
    return () => {
      if (timer) {
        clearTimeout(timer)
      }
    }
  }, [countdown])

  // 手机号格式验证
  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }

  // 发送验证码
  const handleSendCode = async () => {
    if (!validatePhone(phoneValue)) {
      Taro.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return
    }

    setCodeLoading(true)
    try {
      const response = await commonApi.getPhoneVerificationCode({
        phone: phoneValue,
        bizCode: 'miniOrder', 
        bizType: 'mini'
      })

      if (response.code === STATUS_CODE.SUCCESS) {
        Taro.showToast({
          title: '验证码已发送',
          icon: 'success'
        })
        setCountdown(60) // 60秒倒计时
      } else {
        throw new Error(response.message || '发送失败')
      }
    } catch (error) {
      console.error('发送验证码失败:', error)
      Taro.showToast({
        title: error.message || '发送验证码失败',
        icon: 'none'
      })
    } finally {
      setCodeLoading(false)
    }
  }

  // 处理手机号输入
  const handlePhoneInput = (e: any) => {
    onPhoneChange(e.detail.value)
  }

  // 处理验证码输入
  const handleCodeInput = (e: any) => {
    onCodeChange(e.detail.value)
  }

  return (
    <View className={`phone-code-input ${className}`}>
      {/* 手机号输入 */}
      <View className='input-field'>
        <View className='field-label'>
          <Text className='required-mark'>*</Text>
          <Text className='label-text'>手机号</Text>
        </View>
        <Input
          className='form-input'
          placeholder='请输入手机号'
          type='number'
          value={phoneValue}
          onInput={handlePhoneInput}
          maxlength={11}
        />
      </View>

      {/* 验证码输入 */}
      <View className='input-field'>
        <View className='field-label'>
          <Text className='required-mark'>*</Text>
          <Text className='label-text'>验证码</Text>
        </View>
        <View className='code-input-group'>
          <Input
            className='form-input code-input'
            placeholder='请输入验证码'
            type='number'
            value={codeValue}
            onInput={handleCodeInput}
            maxlength={6}
          />
          <Button
            className='code-btn'
            onClick={handleSendCode}
            loading={codeLoading}
            disabled={codeLoading || countdown > 0}
          >
            {countdown > 0 ? `${countdown}s` : '获取验证码'}
          </Button>
        </View>
      </View>
    </View>
  )
}

export default PhoneCodeInput

