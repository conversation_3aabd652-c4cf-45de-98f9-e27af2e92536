/**
 * 案例分类选择组件
 */
import React from 'react'
import { View, Text, Image } from '@tarojs/components'
import clickMoreIcon from '@/assets/images/common-icon/click_more.png'

// 组件属性
export interface CasesCategoriesProps {
  // 分类列表
  categories: CaseAPI.CaseCategoryInfo[]
  // 选中的分类ID
  selectedCategoryId: number | null
  // 是否展开
  isExpanded: boolean
  // 分类选择回调
  onCategorySelect: (categoryId: number) => void
  // 切换展开状态回调
  onToggleExpanded: () => void
  // 自定义样式类名
  className?: string
}

const CasesCategories: React.FC<CasesCategoriesProps> = ({
  categories,
  selectedCategoryId,
  isExpanded,
  onCategorySelect,
  onToggleExpanded,
  className = ''
}) => {
  // 显示的分类列表（展开时显示全部，收起时显示前6个）
  const displayCategories = isExpanded ? categories : categories.slice(0, 4)

  return (
    <View className={`cases-categories ${className}`}>
      <View className='cases-categories__list'>
        {displayCategories.map((category) => (
          <View
            key={category.id}
            className={`cases-categories__item ${
              selectedCategoryId === category.id ? 'cases-categories__item--active' : ''
            }`}
            onClick={() => onCategorySelect(category.id)}
          >
            <Text className='cases-categories__item-text'>{category.name}</Text>
          </View>
        ))}
      </View>

      {/* 展开/收起按钮 */}
      {categories.length > 6 && (
        <View className='cases-categories__toggle' onClick={onToggleExpanded}>
          <Image
            className={`cases-categories__toggle-icon ${
              isExpanded ? 'cases-categories__toggle-icon--expanded' : ''
            }`}
            src={clickMoreIcon}
            mode='aspectFit'
          />
        </View>
      )}
    </View>
  )
}

export default CasesCategories
