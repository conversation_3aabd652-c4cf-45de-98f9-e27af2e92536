/**
 * 进度编辑弹窗组件
 */
import React, { useState } from 'react'
import { View, Text, Textarea, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import ImageUploadField from '@/components/ImageUploadField'
import { removeImagesPrefix } from '@/utils/helpers/utils'
import './index.scss'

interface ProgressEditModalProps {
  progress: LawyerAPI.ProgressData
  onSubmit: (data: LawyerAPI.FollowCaseOrderProgressRequest) => void
  onCancel: () => void
}

const ProgressEditModal: React.FC<ProgressEditModalProps> = ({
  progress,
  onSubmit,
  onCancel
}) => {
  const [content, setContent] = useState('')
  const [imageUrls, setImageUrls] = useState<string[]>([])
  const [uploading, setUploading] = useState(false)

  // 处理内容输入
  const handleContentChange = (e: any) => {
    setContent(e.detail.value)
  }

  // 提交表单
  const handleSubmit = () => {
    if (!content.trim()) {
      Taro.showToast({
        title: '请输入跟进内容',
        icon: 'none'
      })
      return
    }

    const data: LawyerAPI.FollowCaseOrderProgressRequest = {
      orderStageId: progress.orderStageId,
      caseProgressId: progress.caseProgressId,
      content: content.trim(),
      imageUrls: imageUrls.length > 0 ? removeImagesPrefix(imageUrls) : undefined
    }

    // 提示不可修改
    Taro.showModal({
      title: '提示',
      content: '提交后将不可修改，是否确认提交？',
      success: (res) => {
        if (res.confirm) {
          onSubmit(data)
        }
      }
    })
  }

  return (
    <View className='progress-edit-modal'>
      <View className='progress-edit-modal__header'>
        <Text className='progress-edit-modal__title'>更新进度记录</Text>
        <Text className='progress-edit-modal__subtitle'>{progress.orderProgressName}</Text>
      </View>

      <View className='progress-edit-modal__content'>
        {/* 内容输入 */}
        <View className='progress-edit-modal__field'>
          <Text className='progress-edit-modal__label'>跟进内容</Text>
          <Textarea
            className='progress-edit-modal__textarea'
            placeholder='请输入本次跟进的具体内容...'
            value={content}
            onInput={handleContentChange}
            maxlength={500}
            showConfirmBar={false}
            adjustPosition={false}
          />
          <Text className='progress-edit-modal__count'>{content.length}/500</Text>
        </View>

        {/* 图片上传 */}
        <View className='progress-edit-modal__field'>
          <ImageUploadField
            imageUrls={imageUrls || []}
            onChange={setImageUrls}
            uploading={uploading}
            onUploadingChange={setUploading}
          />
        </View>
      </View>

      <View className='progress-edit-modal__actions'>
        <Button
          className='progress-edit-modal__button progress-edit-modal__button--cancel'
          onClick={onCancel}
        >
          取消
        </Button>
        <Button
          className='progress-edit-modal__button progress-edit-modal__button--submit'
          onClick={handleSubmit}
          disabled={uploading}
        >
          提交
        </Button>
      </View>
    </View>
  )
}

export default ProgressEditModal

