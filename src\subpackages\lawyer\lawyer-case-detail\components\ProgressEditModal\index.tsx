/**
 * 进度编辑弹窗组件
 */
import React, { useState } from 'react'
import { View, Text, Textarea, Button, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { uploadApi } from '@/utils/request/apis'
import { STATUS_CODE } from '@/utils/request/config'
import './index.scss'

interface ProgressEditModalProps {
  progress: LawyerAPI.ProgressData
  onSubmit: (data: LawyerAPI.FollowCaseOrderProgressRequest) => void
  onCancel: () => void
}

const ProgressEditModal: React.FC<ProgressEditModalProps> = ({
  progress,
  onSubmit,
  onCancel
}) => {
  const [content, setContent] = useState('')
  const [imageUrls, setImageUrls] = useState<string[]>([])
  const [uploading, setUploading] = useState(false)

  // 处理内容输入
  const handleContentChange = (e: any) => {
    setContent(e.detail.value)
  }

  // 选择图片
  const handleChooseImage = () => {
    Taro.chooseImage({
      count: 9 - imageUrls.length, // 最多9张图片
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        const tempFilePaths = res.tempFilePaths

        setUploading(true)
        try {
          const uploadPromises = tempFilePaths.map(async (filePath) => {
            const uploadRes = await uploadApi.uploadImage(filePath)
            if (uploadRes.code === STATUS_CODE.SUCCESS && uploadRes.data) {
              return uploadRes.data.url
            }
            throw new Error('上传失败')
          })

          const uploadedUrls = await Promise.all(uploadPromises)
          setImageUrls(prev => [...prev, ...uploadedUrls])

          Taro.showToast({
            title: '上传成功',
            icon: 'success'
          })
        } catch (error) {
          console.error('图片上传失败:', error)
          Taro.showToast({
            title: '上传失败',
            icon: 'none'
          })
        } finally {
          setUploading(false)
        }
      }
    })
  }

  // 删除图片
  const handleDeleteImage = (index: number) => {
    setImageUrls(prev => prev.filter((_, i) => i !== index))
  }

  // 预览图片
  const handlePreviewImage = (current: string) => {
    Taro.previewImage({
      current,
      urls: imageUrls
    })
  }

  // 提交表单
  const handleSubmit = () => {
    if (!content.trim()) {
      Taro.showToast({
        title: '请输入跟进内容',
        icon: 'none'
      })
      return
    }

    const data: LawyerAPI.FollowCaseOrderProgressRequest = {
      orderStageId: progress.orderStageId,
      caseProgressId: progress.caseProgressId,
      content: content.trim(),
      imageUrls: imageUrls.length > 0 ? JSON.stringify(imageUrls) : undefined
    }

    // 提示不可修改
    Taro.showModal({
      title: '提示',
      content: '提交后将不可修改，是否确认提交？',
      success: (res) => {
        if (res.confirm) {
          onSubmit(data)
        }
      }
    })
  }

  return (
    <View className='progress-edit-modal'>
      <View className='progress-edit-modal__header'>
        <Text className='progress-edit-modal__title'>更新进度记录</Text>
        <Text className='progress-edit-modal__subtitle'>{progress.orderProgressName}</Text>
      </View>

      <View className='progress-edit-modal__content'>
        {/* 内容输入 */}
        <View className='progress-edit-modal__field'>
          <Text className='progress-edit-modal__label'>跟进内容</Text>
          <Textarea
            className='progress-edit-modal__textarea'
            placeholder='请输入本次跟进的具体内容...'
            value={content}
            onInput={handleContentChange}
            maxlength={500}
            showConfirmBar={false}
            adjustPosition={false}
          />
          <Text className='progress-edit-modal__count'>{content.length}/500</Text>
        </View>

        {/* 图片上传 */}
        <View className='progress-edit-modal__field'>
          <Text className='progress-edit-modal__label'>相关图片（可选）</Text>
          <View className='progress-edit-modal__images'>
            {imageUrls.map((url, index) => (
              <View key={index} className='progress-edit-modal__image-item'>
                <Image
                  className='progress-edit-modal__image'
                  src={url}
                  mode='aspectFill'
                  onClick={() => handlePreviewImage(url)}
                />
                <View
                  className='progress-edit-modal__image-delete'
                  onClick={() => handleDeleteImage(index)}
                >
                  <Text className='progress-edit-modal__image-delete-text'>×</Text>
                </View>
              </View>
            ))}

            {imageUrls.length < 9 && (
              <View
                className='progress-edit-modal__image-add'
                onClick={handleChooseImage}
              >
                <Text className='progress-edit-modal__image-add-text' style={{ fontSize: uploading ? '24rpx' : '48rpx' }}>
                  {uploading ? '上传中...' : '+'}
                </Text>
              </View>
            )}
          </View>
          <Text className='progress-edit-modal__tip'>最多可上传9张图片</Text>
        </View>
      </View>

      <View className='progress-edit-modal__actions'>
        <Button
          className='progress-edit-modal__button progress-edit-modal__button--cancel'
          onClick={onCancel}
        >
          取消
        </Button>
        <Button
          className='progress-edit-modal__button progress-edit-modal__button--submit'
          onClick={handleSubmit}
          disabled={uploading}
        >
          提交
        </Button>
      </View>
    </View>
  )
}

export default ProgressEditModal
