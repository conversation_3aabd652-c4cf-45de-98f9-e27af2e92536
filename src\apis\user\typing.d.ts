/**
 * 用户模块类型声明
 */

// 引用通用类型
/// <reference path="../common-types.d.ts" />

declare namespace UserAPI {
  // 会员信息类型（基于 OwnerMemberInfoRes）
  interface MemberInfo {
    id: number
    userName: string
    phone: string
    type: number  // 用户类型，1普通用户，2律师
    avatarUrl?: string  // 头像URL
    nickname?: string   // 昵称
    email?: string      // 邮箱
    // 根据实际API文档补充更多字段
    [key: string]: any
  }

  // 获取会员信息响应
  interface GetMemberInfoResponse extends MemberInfo { }

  // 更新头像URL请求参数
  interface UpdateAvatarUrlRequest {
    avatarUrl: string
  }

  // 更新头像URL响应
  interface UpdateAvatarUrlResponse extends CommonTypes.BaseApiResponse {
    data?: {
      avatarUrl: string
    }
  }

  // 会员案件订单信息
  interface MemberCaseOrderInfo {
    id: number
    orderNo: string
    caseTypeName: string
    caseStage: string
    clientName: string
    handlingAgency: string
    province: string
    city: string
    district: string
    orderStatus: number
    amountInvolvedOfCase: number
    paymentStatus: number
    paymentAmount: number
    lawyerId: number
    lawyerName: string
    lawyerRequirements: string
    createdAt: string
  }

  // 获取会员案件订单列表请求参数（基于 OwnerMemberOrderListReq）
  interface GetMemberCaseOrderListRequest extends CommonTypes.BasePaginationRequest {
    caseStageId?: number  // 案件阶段Id
    caseTypeId?: number   // 案件类型ID
  }

  // 获取会员案件订单列表响应（基于 OwnerMemberOrderListRes）
  interface GetMemberCaseOrderListResponse extends CommonTypes.PaginationResponse<MemberCaseOrderInfo> { }

  // 案源订单创建请求参数
  interface CreateLawEntrustOrderRequest {
    caseTypeId: number
    caseStageId: number
    handlingAgency: string
    province: string
    clientName: string
    mobile: string
    code: string
    city: string
    district: string
    source: string // 订单来源：rescue=>黄金救援,optimal=>优配律师,customer=>客服
    amountInvolvedOfCase?: number
    lawyerRequirements?: string
    lawyerId?: number
  }

  // 案源订单创建响应
  interface CreateLawEntrustOrderResponse {
    id: number
    orderNo: string
    clientName: string
    clientId: number
    caseTypeName: string
    caseStage: string
    handlingAgency: string
    province: string
    city: string
    district: string
    orderStatus: number
    source: string
    amountInvolvedOfCase: number
  }

  interface GetMemberCaseOrderDetailResponse extends MemberCaseOrderDetailInfo { }
  interface GetMemberCaseOrderStageRecordsResponse extends CommonAPI.GetCaseOrderStageRecordsResponse { }

  // 会员案件订单详情信息
  interface MemberCaseOrderDetailInfo {
    id: number
    orderNo: string
    clientName: string
    clientId: number
    clientMobile: string
    caseTypeId: number
    caseTypeName: string
    caseStage: string
    caseStageId: number
    handlingAgency: string
    province: string
    city: string
    district: string
    orderStatus: number  // 订单状态：0-删除，1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废
    source: string     // 订单来源：rescue=>黄金救援,optimal=>优配律师,customer=>客服
    amountInvolvedOfCase: number
    paymentStatus: number  // 支付状态：1-未支付，2-支付完成，3-支付失败
    paymentAmount: number
    lawyerId: number
    lawyerName: string
    lawyerRequirements: string
    creator: string
    creatorId: number
    reviewTime: string
    createdAt: string
  }

  // 会员案件订单阶段记录信息（使用通用类型）
  interface MemberCaseOrderStageRecordsInfo extends CommonAPI.CaseOrderStageRecordsInfo { }

  // 会员案件订单阶段进度数据（使用通用类型）
  interface MemberCaseOrderStageProgressData extends CommonAPI.CaseOrderStageProgressData { }

  // 会员案件订单阶段记录数据（使用通用类型）
  interface MemberCaseOrderStageRecordData extends CommonAPI.CaseOrderStageRecordData { }
}
