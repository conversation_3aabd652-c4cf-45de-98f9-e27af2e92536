/**
 * 律师动态列表组件样式
 */

.dynamics-list {
  width: 100%;

  &__loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200rpx;
    color: #999;
    font-size: 28rpx;
  }

  &__empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 60rpx 40rpx;
    text-align: center;
  }

  &__empty-icon {
    font-size: 60rpx;
    margin-bottom: 16rpx;
    opacity: 0.6;
  }

  &__empty-title {
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
    margin-bottom: 12rpx;
  }

  &__empty-desc {
    font-size: 22rpx;
    color: #999;
    line-height: 1.4;
    max-width: 400rpx;
  }

  &__scroll-view {
    width: 100%;
    white-space: nowrap;
  }

  &__container {
    display: flex;
    flex-direction: row;
    padding: 0 30rpx;
  }
}

// 动态卡片样式
.dynamics-card {
  display: inline-block;
  width: 520rpx;
  height: 243rpx;
  background: #E8F4FD;
  box-shadow: 0rpx 2rpx 48rpx 0rpx rgba(0, 0, 0, 0.04);
  border-radius: 14rpx;
  padding: 30rpx;
  margin-right: 30rpx;
  vertical-align: top;
  white-space: normal;
  box-sizing: border-box;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }

  &:last-child {
    margin-right: 0;
  }

  &__title {
    font-weight: 600;
    font-size: 28rpx;
    color: #000000;
    line-height: 36rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    margin-bottom: 20rpx;
  }

  &__meta {
    display: flex;
    flex-direction: column;
    gap: 10rpx;
    margin-top: auto;
  }

  &__category {
    font-size: 24rpx;
    color: #666;
    line-height: 28rpx;
  }

  &__info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 22rpx;
    color: #999;
    line-height: 26rpx;
  }

  &__time {
    flex: 1;
  }

  &__view-count {
    flex-shrink: 0;
  }
}