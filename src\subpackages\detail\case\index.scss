/**
 * 案例详情页面样式
 */
// 案例信息卡片
.case-info-card {
  background: #F8F8F8;
  padding: 55rpx 30rpx;
  margin-bottom: 30rpx;

  // 标题
  &__title {
    display: block;
    font-size: 40rpx;
    font-weight: 600;
    color: #333;
    line-height: 48rpx;
    margin-bottom: 20rpx;
    word-break: break-word;
  }

  // 信息模块
  &__meta {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  // 分类名称
  &__category {
    color: #BD8A4F;
    font-size: 20rpx;
    font-weight: 600;
    margin-right: 20rpx;
  }

  // 时间容器
  &__time {
    display: flex;
    align-items: center;
    margin-right: 20rpx;
  }

  &__time-icon {
    width: 25rpx;
    height: 25rpx;
    margin-right: 5rpx;
  }

  &__time-text {
    color: #9C9AA0;
    font-size: 20rpx;
    font-weight: 600;
  }

  // 浏览量容器
  &__view {
    display: flex;
    align-items: center;
  }

  &__view-icon {
    width: 25rpx;
    height: 25rpx;
    margin-right: 5rpx;
  }

  &__view-text {
    color: #9C9AA0;
    font-size: 20rpx;
    font-weight: 600;
  }
}

// 案例内容
.case-content {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 40rpx;

  &__title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
    line-height: 36rpx;
  }

  &__text {
    font-size: 30rpx;
    color: #333;
    line-height: 44rpx;
    word-break: break-word;
    white-space: pre-wrap;
  }

  &__images {
    margin-top: 30rpx;
    display: flex;
    flex-wrap: wrap;
    gap: 8rpx;
  }

  &__image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 8rpx;
    background-color: #f0f0f0;
  }
}

.case-detail-list {
  padding-top: 30rpx;
  display: flex;
  flex-direction: column;
  background: #F8F8F8;

  &__header {
    padding: 0 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__title {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    &-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 12rpx;
    }

    &-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #000000;
    }
  }

  &__more {
    text-align: right;
    margin-bottom: 24rpx;

    text {
      font-size: 28rpx;
      color: #828D99;
    }
  }
}