/**
 * 横向文章列表组件
 * 
 * 展示最多5个文章卡片，支持横向滑动，支持外部传入筛选条件
 */
import React, { useState, useEffect } from 'react'
import { View, ScrollView, Text, Image } from '@tarojs/components'
import { articleApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
import { navigateToPage } from '@/utils'
import { formatTime } from '@/utils'
import ClockIcon from '@/assets/images/common-icon/clock.png'
import EyeIcon from '@/assets/images/common-icon/eye.png'
import StarIcon from '@/assets/images/common-icon/star_fill.png'
import LikeIcon from '@/assets/images/common-icon/like_outlone_g.png'
import { HorizontalArticleListProps } from './types'
import './index.scss'

const HorizontalArticleList: React.FC<HorizontalArticleListProps> = ({
  filterParams,
  list,
  className = ''
}) => {
  // 文章列表数据
  const [articles, setArticles] = useState<ArticleAPI.ArticleListInfo[]>([])
  // 加载状态
  const [isLoading, setIsLoading] = useState(false)



  // 加载文章列表数据
  const loadArticles = async (val?: ArticleAPI.GetArticleListRequest) => {
    try {
      setIsLoading(true)
      const params = {
        page: 1,
        pageSize: 5,
        ...val
      }
      const response = await articleApi.getArticleList(params)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setArticles(response.data.list || [])
      }
    } catch (error) {
      console.error('加载文章列表失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理文章卡片点击 - 跳转到文章详情页
  const handleArticleClick = (articleInfo: ArticleAPI.ArticleListInfo) => {
    navigateToPage(`/subpackages/detail/article/index?id=${articleInfo.id}`)
  }

  // 使用外部传入的数据
  useEffect(() => {
    if (list) {
      setArticles(list)
      setIsLoading(false)
    }
  }, [list])

  // 根据筛选条件加载数据
  useEffect(() => {
    if (!list && filterParams) {
      loadArticles(filterParams)
    }
  }, [filterParams, list])

  // 初始化加载数据
  useEffect(() => {
    if (!list && !filterParams) {
      loadArticles()
    }
  }, [list, filterParams])

  // 是否显示加载状态
  const showLoading = isLoading && articles.length === 0

  return (
    <View className={`horizontal-article-list ${className}`}>
      {showLoading ? (
        <View className='horizontal-article-list__loading'>
          <Text>加载中...</Text>
        </View>
      ) : (
        <>
          {articles.length > 0 ? (
            <ScrollView
              className='horizontal-article-list__scroll-view'
              scrollX
              showScrollbar={false}
              enhanced
              bounces={false}
            >
              <View className='horizontal-article-list__container'>
                {articles.map((articleInfo) => (
                  <View
                    key={articleInfo.id}
                    className='horizontal-article-card'
                    onClick={() => handleArticleClick(articleInfo)}
                  >
                    <Text className='horizontal-article-card__title'>{articleInfo.title}</Text>
                    <View className='horizontal-article-card__meta'>
                      <Image src={ClockIcon} mode='aspectFit' className='horizontal-article-card__info-icon' />
                      <Text className='horizontal-article-card__info-text'>{formatTime(articleInfo.createdAt)}</Text>
                      <Image src={EyeIcon} mode='aspectFit' className='horizontal-article-card__info-icon' />
                      <Text className='horizontal-article-card__info-text'>{articleInfo.viewCount}</Text>
                      <Image src={StarIcon} mode='aspectFit' className='horizontal-article-card__info-icon' />
                      <Text className='horizontal-article-card__info-text'>{articleInfo.favoriteCount}</Text>
                      <Image src={LikeIcon} mode='aspectFit' className='horizontal-article-card__info-icon' />
                      <Text className='horizontal-article-card__info-text'>{articleInfo.likeCount}</Text>
                    </View>
                  </View>
                ))}
              </View>
            </ScrollView>
          ) : (
            <View className='horizontal-article-list__empty'>
              <Text className='horizontal-article-list__empty-title'>暂无文章信息</Text>
              <Text className='horizontal-article-list__empty-desc'>当前没有相关文章，请稍后再试</Text>
            </View>
          )}
        </>
      )}
    </View>
  )
}

// 导出组件
export default HorizontalArticleList

export type { HorizontalArticleListProps } from './types'
