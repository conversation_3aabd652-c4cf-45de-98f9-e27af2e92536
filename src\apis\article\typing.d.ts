/**
 * 文章模块类型声明
 * 基于 api.json 接口文档定义
 */

// 引用通用类型
/// <reference path="../common-types.d.ts" />

declare namespace ArticleAPI {
  // 文章详情信息类型（基于 LawArticleDetailDto）
  interface ArticleDetailInfo {
    id: number
    title: string
    content: string  // HTML/富文本
    imagesContexts: string // 图片内容字符串(支持json)
    categoryId: number
    categoryName: string
    likeCount: number
    viewCount: number
    favoriteCount: number
    createdAt: string
    creator: string
    creatorId: number
  }

  // 文章列表信息类型（基于 LawArticleLstDto）
  interface ArticleListInfo {
    id: number
    title: string
    categoryId: number
    categoryName: string
    likeCount: number
    viewCount: number
    favoriteCount: number
    createdAt: string
  }

  // 获取文章列表请求参数
  interface GetArticleListRequest extends CommonTypes.BasePaginationRequest {
    title?: string  // 文章标题模糊搜索
    lawyerId?: number  // 律师id获取该律师的文章列表
  }

  // 获取文章列表响应
  interface GetArticleListResponse extends CommonTypes.PaginationResponse<ArticleListInfo> { }

  // 获取文章详情响应
  interface GetArticleDetailResponse {
    detail: ArticleDetailInfo
    lawyerInfo: LawyerAPI.LawyerInfo
  }

  // 获取律师文章数据统计响应
  interface GetLawyerArticleCountResponse {
    articleNum: number  // 文章数
    viewNum: number     // 阅读数
    likeNum: number     // 点赞数
  }

  // 收藏文章响应
  interface CollectArticleResponse { }

  // 点赞文章响应
  interface LikeArticleResponse { }

  // 取消收藏文章响应
  interface CancelCollectArticleResponse { }

  // 取消点赞文章响应
  interface CancelLikeArticleResponse { }

  // 我的收藏文章信息
  interface MyCollectedArticleInfo {
    id: number
    articleTitle: string
    articleId: number
    collectorName: string
    collectorId: number
    createdAt: string
    // 文章统计数据
    favoriteCount: number
    viewCount: number
    likeCount: number
  }

  // 获取我的收藏文章列表请求参数
  interface GetMyCollectedArticlesRequest extends CommonTypes.BasePaginationRequest { }

  // 获取我的收藏文章列表响应
  interface GetMyCollectedArticlesResponse extends CommonTypes.PaginationResponse<MyCollectedArticleInfo> { }

  // 我的文章信息
  interface MyArticleInfo extends CommonTypes.BaseEntity {
    title: string
    categoryId: number
    categoryName: string
    content: string
    likeCount: number
    viewCount: number
    favoriteCount: number
    status: number  // 0删除,1待审核,2审核通过,3审核不通过
    rejectReason?: string
    imagesContexts: string // 图片内容字符串(支持json)
    creator: string
    creatorId: number
    reviewer?: string
    reviewerId?: number
    modifier?: string
    reviewTime?: string
    updatedAt: string
  }

  // 获取我的文章列表请求参数
  interface GetMyArticleListRequest extends CommonTypes.BasePaginationRequest {
    title?: string  // 文章标题模糊搜索
  }

  // 获取我的文章列表响应
  interface GetMyArticleListResponse extends CommonTypes.PaginationResponse<MyArticleInfo> { }

  // 获取我的文章详情请求参数
  interface GetMyArticleDetailRequest {
    title: string
    content: string
  }

  // 获取我的文章详情响应
  interface GetMyArticleDetailResponse extends MyArticleInfo { }

  // 发布文章请求参数
  interface PublishArticleRequest {
    title: string
    content: string
    imagesContexts?: string // 图片内容字符串(支持json)，非必传
  }

  // 发布文章响应
  interface PublishArticleResponse { }

  // 修改文章请求参数
  interface UpdateMyArticleRequest extends PublishArticleRequest {
    articleId: number
  }

  // 修改文章响应
  interface UpdateMyArticleResponse { }
}
