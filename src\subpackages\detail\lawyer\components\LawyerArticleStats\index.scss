/**
 * 律师文章数据统计组件样式
 */

.lawyer-article-stats {
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

  &__title {
    font-size: 32rpx;
    font-weight: 600;
    color: #000000;
    margin-bottom: 30rpx;
    display: block;
  }

  // 数据列表
  &__list {
    display: flex;
    justify-content: space-around;
  }

  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
  }

  &__number {
    position: relative;
    margin-bottom: 10rpx;
    display: flex;
    align-items: flex-start;
    justify-content: center;
  }

  &__number-text {
    font-size: 48rpx;
    font-weight: 600;
    line-height: 56rpx;
    display: block;
  }

  &__number-unit {
    font-size: 24rpx;
    font-weight: 600;
    color: #BD8A4F;
    line-height: 1;
    margin-left: 4rpx;
    margin-top: 8rpx;
    display: block;
  }

  &__label {
    font-size: 26rpx;
    color: #666;
    line-height: 30rpx;
    text-align: center;
    display: block;
  }

  // 加载状态
  &__loading {
    display: flex;
    justify-content: space-around;
  }

  &__loading-item {
    width: 120rpx;
    height: 80rpx;
    background: #F5F5F5;
    border-radius: 10rpx;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }
}

// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.6;
  }

  100% {
    opacity: 1;
  }
}