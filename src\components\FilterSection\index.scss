.filter-section {
  flex-shrink: 0;
  background: #fff;
  border-bottom: 2rpx solid #f0f0f0;

  .filter-section__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 30rpx;

    .filter-section__left {
      display: flex;
      align-items: center;
      gap: 15rpx;
      flex: 1;

      .filter-section__title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
        flex-shrink: 0;
      }

      .filter-section__selected {
        font-size: 26rpx;
        color: #BD8A4F;
        background: rgba(189, 138, 79, 0.1);
        padding: 4rpx 12rpx;
        border-radius: 16rpx;
        white-space: nowrap;
        max-width: 200rpx;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .filter-section__arrow {
      width: 24rpx;
      height: 24rpx;
      transition: transform 0.3s ease;
      flex-shrink: 0;

      &.expanded {
        transform: rotate(180deg);
      }
    }
  }

  .filter-section__content {
    overflow: hidden;
    transition: max-height 0.3s ease;

    &.collapsed {
      max-height: 0;
    }

    &.expanded {
      max-height: 800rpx;
    }

    .filter-section__options {
      display: flex;
      flex-wrap: wrap;
      padding: 20rpx 30rpx 30rpx;
      gap: 20rpx;

      .filter-section__option {
        padding: 10rpx 30rpx;
        background: #f7f7f7;
        border-radius: 40rpx;

        &.active {
          background: #BD8A4F;

          .filter-section__option-text {
            color: #fff;
          }
        }

        .filter-section__option-text {
          font-size: 28rpx;
          color: #666;
          white-space: nowrap;
        }
      }
    }
  }
}
