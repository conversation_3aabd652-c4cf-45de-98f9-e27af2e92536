/**
 * 律师动态相关接口
 * 基于 api.json 接口文档定义
 */
import request from '../../index'

// 律师动态相关接口
export const dynamicsApi = {
  // 获取动态列表（基于 /mini/find-law-dynamics/list）
  getDynamicsList: (params?: DynamicsAPI.GetDynamicsListRequest) =>
    request.get<DynamicsAPI.GetDynamicsListResponse>('/mini/find-law-dynamics/list', params),

  // 获取动态详情（基于 /mini/find-law-dynamics/:dynamicsId/detail）
  getDynamicsDetail: (dynamicsId: number) =>
    request.get<DynamicsAPI.GetDynamicsDetailResponse>(`/mini/find-law-dynamics/${dynamicsId}/detail`),

  // 获取推荐动态列表（基于 /mini/find-law-dynamics/:dynamicsId/recommend-list）
  getRecommendDynamicsList: (dynamicsId: number, params?: DynamicsAPI.GetRecommendDynamicsListRequest) =>
    request.get<DynamicsAPI.GetRecommendDynamicsListResponse>(`/mini/find-law-dynamics/${dynamicsId}/recommend-list`, { ...params, dynamicsId }),

  // 获取我的动态列表（律师个人中心）
  getMyDynamicsList: (params?: DynamicsAPI.GetMyDynamicsListRequest) =>
    request.get<DynamicsAPI.GetMyDynamicsListResponse>('/mini/owner-center-lawyer/law-dynamics/list', params),

  // 获取我的动态详情（律师个人中心）
  getMyDynamicsDetail: (dynamicsId: number) =>
    request.post<DynamicsAPI.GetMyDynamicsDetailResponse>(`/mini/owner-center-lawyer/issue-dynamics/${dynamicsId}/detail`, { dynamicsId }),

  // 发布动态（律师个人中心）
  publishDynamics: (data: DynamicsAPI.PublishDynamicsRequest) =>
    request.post<DynamicsAPI.PublishDynamicsResponse>('/mini/owner-center-lawyer/issue-dynamics/add', data),

  // 修改动态（律师个人中心）
  updateMyDynamics: (dynamicsId: number, data: DynamicsAPI.UpdateMyDynamicsRequest) =>
    request.post<DynamicsAPI.UpdateMyDynamicsResponse>(`/mini/owner-center-lawyer/issue-dynamics/${dynamicsId}/save`, data),
}
