/**
 * 黄金救援页面样式
 */

.golden-rescue {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0;

  .golden-rescue-form {
    padding: 0;
  }

  // 表单区块
  .form-section {
    background-color: #ffffff;
    margin-bottom: 20rpx;
    padding: 30rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 30rpx;
      display: block;
    }
  }

  // 提交区域
  .submit-section {
    background-color: #ffffff;
    padding: 30rpx;
    margin-top: 20rpx;

    .submit-btn {
      width: 100%;
      height: 88rpx;
      background-color: #BD8A4F;
      color: #ffffff;
      font-size: 32rpx;
      font-weight: 600;
      border-radius: 44rpx;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        background-color: #a67a42;
      }

      &.button-disabled {
        background-color: #cccccc;
        color: #999999;
      }

      &.button-loading {
        background-color: #cccccc;
      }
    }
  }

  // 通用表单输入框样式
  .form-input {
    height: 88rpx;
    background-color: #F7F7F7;
    border-radius: 8rpx;
    padding: 0 24rpx;
    font-size: 28rpx;
    color: #333333;
    border: none;

    &::placeholder {
      color: #999999;
    }

    &:focus {
      background-color: #f0f0f0;
    }
  }

  // 文本域样式
  .form-textarea {
    width: 100%;
    min-height: 160rpx;
    background-color: #F7F7F7;
    border-radius: 8rpx;
    padding: 24rpx;
    font-size: 28rpx;
    color: #333333;
    border: none;
    box-sizing: border-box;

    &::placeholder {
      color: #999999;
    }

    &:focus {
      background-color: #f0f0f0;
    }
  }
}
