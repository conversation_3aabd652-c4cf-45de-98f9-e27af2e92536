/**
 * 委托人案件详情页面
 */
import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { View } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'
import { CaseBasicInfo, CaseStageTimeline } from '@/components'
import { userApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
import { debounceForMiniProgram } from '@/utils/helpers/common'

import { PaymentButton } from './components'
import './index.scss'

const MyCaseDetail: React.FC = () => {
  const router = useRouter()
  const { id } = router.params
  const orderId = Number(id)

  // 数据状态
  const [caseDetail, setCaseDetail] = useState<UserAPI.MemberCaseOrderDetailInfo | null>(null)
  const [stageRecords, setStageRecords] = useState<UserAPI.MemberCaseOrderStageRecordsInfo | null>(null)

  // 加载案件详情
  const loadCaseDetail = async () => {
    try {
      const response = await userApi.getMemberCaseOrderDetail(orderId)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setCaseDetail(response.data)
      } else {
        throw new Error(response.message || '获取案件详情失败')
      }
    } catch (e) {
      console.error('加载案件详情失败:', e)
      throw e
    }
  }

  // 加载案件阶段记录
  const loadStageRecords = async () => {
    try {
      const response = await userApi.getMemberCaseOrderStageRecords(orderId)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setStageRecords(response.data)
      } else {
        throw new Error(response.message || '获取阶段记录失败')
      }
    } catch (e) {
      console.error('加载阶段记录失败:', e)
      throw e
    }
  }

  // 加载页面数据
  const loadPageData = async () => {
    try {
      await Promise.all([
        loadCaseDetail(),
        loadStageRecords()
      ])
    } catch (e) {
      console.error('加载页面数据失败:', e)
    }
  }

  // 判断是否需要显示支付按钮
  const shouldShowPaymentButton = (): boolean => {
    if (!caseDetail) return false
    // 支付状态为未支付(1)时显示支付按钮
    return caseDetail.paymentStatus === 1 && caseDetail.paymentAmount > 0
  }

  // 处理支付按钮点击的原始函数
  const handlePaymentOriginal = useCallback(() => {
    if (!caseDetail) return

    // TODO: 实现支付逻辑
    Taro.showToast({
      title: '支付功能开发中',
      icon: 'none'
    })
  }, [caseDetail])

  // 使用防抖函数包装支付处理函数
  const handlePayment = useMemo(
    () => debounceForMiniProgram(handlePaymentOriginal, 500),
    [handlePaymentOriginal]
  )

  // 页面初始化
  useEffect(() => {
    if (orderId) {
      loadPageData()
    }
  }, [orderId])

  // 下拉刷新
  const handleRefresh = async () => {
    await loadPageData()
  }

  // 判断是否显示支付按钮
  const showPaymentButton = shouldShowPaymentButton()

  return (
    <>
      <PageLayout
        title='案件详情'
        showBack
        backgroundColor='#f5f5f5'
        enablePullRefresh
        onPullRefresh={handleRefresh}
        showFloatingMenu={false}
      >
        <PageContent>
          {
            (caseDetail && stageRecords) ?
              <View
                className='case-detail'
                style={{
                  paddingBottom: showPaymentButton ? '350rpx' : '20rpx'
                }}
              >
                {/* 案件基本信息 */}
                <CaseBasicInfo caseDetail={caseDetail} viewType='member' />
                {/* 案件阶段时间线 */}
                <CaseStageTimeline stageRecords={stageRecords} />
              </View> : null}
        </PageContent>
      </PageLayout>
      {/* 支付按钮 - 固定在底部 */}
      {showPaymentButton && caseDetail && (
        <PaymentButton
          caseDetail={caseDetail}
          onPayment={handlePayment}
        />
      )}
    </>
  )
}

export default MyCaseDetail
