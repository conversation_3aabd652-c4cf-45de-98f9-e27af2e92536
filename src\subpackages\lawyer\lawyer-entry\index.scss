.auth-form-container {
    height: calc(100vh - 120rpx); // 减去底部按钮高度
    background: #f8f9fa;
    padding-bottom: 120rpx; // 防止内容被底部按钮遮挡
    box-sizing: border-box;
}

.auth-form {
    padding: 30rpx;

    .form-section {
        background: #fff;
        border-radius: 16rpx;
        padding: 30rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

        .section-title {
            display: inline-block;
            font-size: 32rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 30rpx;
            padding-bottom: 20rpx;
            border-bottom: 2rpx solid #f0f0f0;
        }

        .form-item {
            margin-bottom: 30rpx;

            &:last-child {
                margin-bottom: 0;
            }

            .form-label {
                display: inline-block;
                font-size: 28rpx;
                color: #333;
                margin-bottom: 16rpx;
                font-weight: 500;
                width: 100%;

                .required {
                    color: #ff4757;
                    margin-right: 8rpx;
                }
            }

            .form-input {
                width: 100%;
                height: 88rpx;
                background: #f7f7f7;
                border-radius: 12rpx;
                padding: 0 24rpx;
                font-size: 28rpx;
                color: #333;
                box-sizing: border-box;
                border: 2rpx solid transparent;
                transition: all 0.3s ease;

                &:focus {
                    background: #fff;
                    border-color: #BD8A4F;
                }

                &::placeholder {
                    color: #999;
                }
            }

            // 地址选择器样式
            .area-select-trigger {
                width: 100%;
                height: 88rpx;
                background: #f7f7f7;
                border-radius: 12rpx;
                padding: 0 24rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;
                box-sizing: border-box;
                border: 2rpx solid transparent;
                transition: all 0.3s ease;

                &:active {
                    background: #fff;
                    border-color: #BD8A4F;
                }

                .area-select-text {
                    font-size: 28rpx;
                    color: #333;

                    &.placeholder {
                        color: #999;
                    }
                }

                .area-select-arrow {
                    width: 0;
                    height: 0;
                    border-left: 10rpx solid transparent;
                    border-right: 10rpx solid transparent;
                    border-top: 12rpx solid #999;
                }
            }

            // 复选框样式
            .checkbox-container {
                display: flex;
                align-items: center;
                margin-top: 16rpx;

                .checkbox {
                    width: 40rpx;
                    height: 40rpx;
                    border: 2rpx solid #ddd;
                    border-radius: 8rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 16rpx;
                    transition: all 0.3s ease;

                    &.checked {
                        background-color: #BD8A4F;
                        border-color: #BD8A4F;

                        .checkbox-icon {
                            color: #fff;
                            font-size: 24rpx;
                            font-weight: bold;
                        }
                    }
                }

                .checkbox-label {
                    font-size: 28rpx;
                    color: #666;
                }
            }

            // 图片上传样式
            .upload-container {
                display: flex;
                justify-content: center;
                align-items: center;

                .upload-item {
                    position: relative;
                    width: 542rpx;
                    height: 300rpx;
                    border-radius: 12rpx;
                    overflow: hidden;

                    .upload-preview {
                        width: 100%;
                        height: 100%;
                        border-radius: 12rpx;
                    }

                    .upload-placeholder {
                        width: 100%;
                        height: 100%;
                        background: #f7f7f7;
                        border: 2rpx dashed #ddd;
                        border-radius: 12rpx;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        box-sizing: border-box;

                        .placeholder-icon {
                            width: 100%;
                            height: 100%;
                        }

                        .upload-button {
                            width: 60rpx;
                            height: 60rpx;
                            background: #BD8A4F;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-bottom: 16rpx;

                            .upload-button-text {
                                font-size: 40rpx;
                                color: #fff;
                                font-weight: 300;
                            }
                        }
                    }

                    .upload-loading {
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: rgba(0, 0, 0, 0.6);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 12rpx;

                        text {
                            color: #fff;
                            font-size: 24rpx;
                        }
                    }
                }
            }

            // 律师执业证上传样式
            .certificate-upload-section {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                gap: 16rpx;

                .certificate-upload-button {
                    width: 200rpx;
                    height: 80rpx;
                    background: #BD8A4F;
                    color: #fff;
                    border-radius: 12rpx;
                    font-size: 28rpx;
                    border: none;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.3s ease;

                    &:active {
                        background: #a67a42;
                        transform: scale(0.98);
                    }

                    &:disabled {
                        background: #ccc;
                        transform: none;
                    }

                    &::after {
                        border: none;
                    }
                }

                .certificate-upload-text {
                    font-size: 24rpx;
                    color: #666;
                    display: inline-block;
                }
            }
        }
    }
}

// 底部提交按钮
.submit-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 30rpx;
    border-top: 2rpx solid #f0f0f0;
    box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);

    .submit-button {
        width: 100%;
        height: 88rpx;
        background: #BD8A4F;
        color: #fff;
        border-radius: 12rpx;
        font-size: 32rpx;
        font-weight: 600;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &:active {
            background: #a67a42;
            transform: scale(0.98);
        }

        &:disabled {
            background: #ccc;
            transform: none;
        }

        &::after {
            border: none;
        }
    }
}