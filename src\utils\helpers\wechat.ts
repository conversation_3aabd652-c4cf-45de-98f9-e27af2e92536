/**
 * 微信小程序特有功能工具函数
 */
import Taro from '@tarojs/taro'

/**
 * 获取用户信息
 * @param withCredentials 是否带上登录态信息
 */
export const getUserInfo = (withCredentials: boolean = false): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.getUserInfo({
      withCredentials,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 获取用户授权设置
 */
export const getSetting = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.getSetting({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 打开设置页面
 */
export const openSetting = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.openSetting({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 获取位置信息
 * @param type 位置类型
 */
export const getLocation = (type: 'wgs84' | 'gcj02' = 'wgs84'): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.getLocation({
      type,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 选择位置
 */
export const chooseLocation = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.chooseLocation({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 打开位置
 * @param latitude 纬度
 * @param longitude 经度
 * @param name 位置名称
 * @param address 地址
 */
export const openLocation = (options: {
  latitude: number
  longitude: number
  name?: string
  address?: string
  scale?: number
}): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.openLocation({
      ...options,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 扫码
 * @param onlyFromCamera 是否只能从相机扫码
 * @param scanType 扫码类型
 */
export const scanCode = (options: {
  onlyFromCamera?: boolean
  scanType?: ('barCode' | 'qrCode' | 'datamatrix' | 'pdf417')[]
} = {}): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.scanCode({
      ...options,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 拨打电话
 * @param phoneNumber 电话号码
 */
export const makePhoneCall = (phoneNumber: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.makePhoneCall({
      phoneNumber,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 保存图片到相册
 * @param filePath 图片文件路径
 */
export const saveImageToPhotosAlbum = (filePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.saveImageToPhotosAlbum({
      filePath,
      success: resolve,
      fail: reject
    })
  })
}
