import React, { useState, useEffect } from 'react'
import { View, Text, ScrollView } from '@tarojs/components'
import Taro from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'
import { CaseOrderCard } from '@/components'
import type { FilterOption } from '@/components/FilterSection/types'
import { userApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
import CategoryCacheManager from '@/utils/cache/categoryCache'
import { sortArray } from '@/utils'
import CaseStagesTab from './components/CaseStagesTab'
import './index.scss'

const MyCases: React.FC = () => {
  // 状态管理
  const [caseOrderList, setCaseOrderList] = useState<UserAPI.MemberCaseOrderInfo[]>([])
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)
  const pageSize = 20

  // 案件阶段相关
  const [caseStages, setCaseStages] = useState<FilterOption[]>([])

  // 过滤参数统一管理
  const [filterParams, setFilterParams] = useState<UserAPI.GetMemberCaseOrderListRequest>({
    page: 1,
    pageSize: 20
  })

  // 加载案件阶段
  const loadCaseStages = async () => {
    try {
      const stageList = await CategoryCacheManager.getCaseStageList()
      const sortedStageList = sortArray(stageList, 'id', 'asc')
      setCaseStages(sortedStageList.map(item => ({ id: item.id, name: item.name })))
    } catch (error) {
      console.error('加载案件阶段失败:', error)
      setCaseStages([])
    }
  }

  // 统一的数据加载函数
  const loadData = async (val: UserAPI.GetMemberCaseOrderListRequest) => {
    if (loading) return

    try {
      setLoading(true)

      const response = await userApi.getMemberCaseOrderList({ ...val })
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        const newList = response.data.list || []

        if (val.page === 1) {
          setCaseOrderList(newList)
        } else {
          setCaseOrderList(prev => [...prev, ...newList])
        }

        // 判断是否还有更多数据
        setHasMore(newList.length === pageSize)
        setPage(val.page!)
      }
    } catch (error) {
      console.error('加载案件订单列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 更新过滤参数并重新加载数据
  const updateFilterAndReload = (newParams: Partial<UserAPI.GetMemberCaseOrderListRequest>) => {
    const updatedParams = {
      ...filterParams,
      ...newParams,
      page: 1,
      pageSize
    }
    setFilterParams(updatedParams)
    loadData(updatedParams)
  }

  // 案件阶段选择处理
  const handleCaseStageSelect = (stageId?: number) => {
    updateFilterAndReload({
      caseStageId: stageId ? stageId : undefined
    })
  }

  // 加载更多
  const handleLoadMore = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1
      loadData({ page: nextPage })
    }
  }

  // 跳转到案件详情页面
  const handleCaseClick = (caseOrder: UserAPI.MemberCaseOrderInfo) => {
    // 这里可以跳转到案件详情页面，暂时使用案例详情页面
    Taro.navigateTo({
      url: `/subpackages/my/my-case-detail/index?id=${caseOrder.id}`
    })
  }

  // 页面初始化
  useEffect(() => {
    loadCaseStages()
  }, [])

  // 监听filterParams变化，重新加载数据
  useEffect(() => {
    loadData(filterParams)
  }, [filterParams])

  return (
    <PageLayout
      title='我的案件'
      showBack
      scrollable={false}
      showFloatingMenu={false}
    >
      <PageContent>
        <View className='my-cases'>
          {/* 案件阶段标签 */}
          <CaseStagesTab
            caseStages={caseStages}
            selectedStageId={filterParams.caseStageId}
            onStageSelect={handleCaseStageSelect}
          />

          {/* 案件列表 */}
          <View className='my-cases-list-container'>
            <ScrollView
              className='my-cases-list'
              scrollY
              onScrollToLower={handleLoadMore}
              lowerThreshold={100}
            >
              {caseOrderList.map((caseOrder) => (
                <CaseOrderCard
                  key={caseOrder.id}
                  caseOrder={caseOrder}
                  viewType='member'
                  onClick={handleCaseClick}
                />
              ))}

              {/* 加载状态 */}
              {loading && (
                <View className='my-cases-loading-more'>
                  <Text>加载中...</Text>
                </View>
              )}

              {/* 没有更多数据 */}
              {!hasMore && caseOrderList.length > 0 && (
                <View className='my-cases-no-more'>
                  <Text>没有更多案件了</Text>
                </View>
              )}

              {/* 空状态 */}
              {!loading && caseOrderList.length === 0 && (
                <View className='my-cases-empty-state'>
                  <Text>暂无案件订单</Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </PageContent>
    </PageLayout>
  )
}

export default MyCases
