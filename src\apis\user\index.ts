/**
 * 用户相关接口
 */
import request from '@/utils/request/index'
import { RequestConfig } from '../types'

// 用户相关接口
export const userApi = {
  // 获取会员信息（基于 /mini/owner-center-member/member-info）
  getMemberInfo: (config?: Omit<RequestConfig, 'url'>) =>
    request.get<UserAPI.GetMemberInfoResponse>('/mini/owner-center-member/member-info', {}, config),

  // 获取会员案件订单列表（基于 /mini/owner-center-member/my-case-order/list）
  getMemberCaseOrderList: (params?: UserAPI.GetMemberCaseOrderListRequest) =>
    request.get<UserAPI.GetMemberCaseOrderListResponse>('/mini/owner-center-member/my-case-order/list', params),

  // 案源订单创建
  createLawEntrustOrder: (data: UserAPI.CreateLawEntrustOrderRequest) =>
    request.post<UserAPI.CreateLawEntrustOrderResponse>('/mini/owner-center-lawyer/law-entrust-order/create', data),

  // 案源订单详情(会员用)
  getMemberCaseOrderDetail: (orderId: number) =>
    request.get<UserAPI.GetMemberCaseOrderDetailResponse>(`/mini/owner-center-member/my-case-order/${orderId}/detail`),

  // 案源订单阶段记录(会员用)
  getMemberCaseOrderStageRecords: (orderId: number) =>
    request.get<UserAPI.GetMemberCaseOrderStageRecordsResponse>(`/mini/owner-center-member/my-case-order/${orderId}/stage-records`),

  // 头像地址更新
  updateAvatarUrl: (avatarUrl: string) =>
    request.post<UserAPI.UpdateAvatarUrlResponse>('/mini/owner-center-member/avatar-url/update', { avatarUrl })
}
