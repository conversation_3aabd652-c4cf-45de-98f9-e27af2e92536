/**
 * 公共接口
 */
import request from '@/utils/request/index'

// 公共接口
export const commonApi = {
  // 获取案例分类列表
  getCaseCategoryList: () =>
    request.get<CommonAPI.CaseCategoryListResponse>('/mini/common/law-case/category-list'),

  // 获取动态分类列表
  getDynamicsCategoryList: () =>
    request.get<CommonAPI.DynamicsCategoryListResponse>('/mini/common/law-dynamics/category-list'),

  // 获取案件阶段列表
  getCaseStageList: () =>
    request.get<CommonAPI.CaseStageListResponse>('/mini/common/law-order/case-stage-list'),

  // 账号登录
  accountLogin: (data: CommonAPI.AccountLoginRequest) =>
    request.post<CommonAPI.AccountLoginResponse>('/mini/common/site/account-login', data),

  // 手机号登录
  phoneLogin: (data: CommonAPI.PhoneLoginRequest) =>
    request.post<CommonAPI.PhoneLoginResponse>('/mini/common/site/phone-login', data),

  // 用户注册
  userRegister: (data: CommonAPI.UserRegisterRequest) =>
    request.post<CommonAPI.UserRegisterResponse>('/mini/common/site/user-register', data),

  // 获取手机验证码
  getPhoneVerificationCode: (data: CommonAPI.PhoneVerificationCodeRequest) =>
    request.get<CommonAPI.PhoneVerificationCodeResponse>('/mini/common/site/phone-verification-code', data),

  // 微信一键登录
  wechatLogin: (data: CommonAPI.WechatLoginRequest) =>
    request.post<CommonAPI.WechatLoginResponse>('/mini/common/wechat/one-click-login', data),

  // 验证用户业务状态是否(点赞、收藏、关注)
  checkUserBusinessStatus: (data: CommonAPI.CheckUserBusinessStatusRequest) =>
    request.post<CommonAPI.CheckUserBusinessStatusResponse>(`/mini/user-business/${data.bizId}/${data.userId}/check`, data),

  // 小程序配置信息
  getMiniConfig: () =>
    request.get<CommonAPI.GetMiniConfigResponse>('/mini/mini-config/sys-config'),

  // 退出登录
  logout: () =>
    request.post('/mini/common/logout')
}
