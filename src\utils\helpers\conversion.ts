/**
 * 数据转换相关工具函数
 */
import Taro from '@tarojs/taro'

/**
 * rpx 转 px
 * @param rpx rpx值
 */
export const rpxToPx = (rpx: number): number => {
  const windowInfo = Taro.getWindowInfo()
  return (rpx * windowInfo.windowWidth) / 750
}

/**
 * px 转 rpx
 * @param px px值
 */
export const pxToRpx = (px: number): number => {
  const windowInfo = Taro.getWindowInfo()
  return (px * 750) / windowInfo.windowWidth
}

/**
 * 获取图片信息
 * @param src 图片路径
 */
export const getImageInfo = (src: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.getImageInfo({
      src,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 下载文件
 * @param url 文件URL
 * @param filePath 保存路径
 */
export const downloadFile = (url: string, filePath?: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.downloadFile({
      url,
      filePath,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 上传文件
 * @param url 上传地址
 * @param filePath 文件路径
 * @param name 文件对应的 key
 * @param formData 额外的表单数据
 */
export const uploadFile = (options: {
  url: string
  filePath: string
  name: string
  formData?: Record<string, any>
  header?: Record<string, any>
}): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.uploadFile({
      ...options,
      success: resolve,
      fail: reject
    })
  })
}
