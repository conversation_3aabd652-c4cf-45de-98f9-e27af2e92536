/**
 * 请求相关类型定义
 * 基于 api.json 接口文档定义
 */

// 导入通用类型
/// <reference path="./common-types.d.ts" />

declare namespace RequestTypes {
  // HTTP方法类型
  type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'HEAD' | 'CONNECT' | 'OPTIONS' | 'TRACE' | 'PATCH'

  // 基础响应结构
  interface BaseResponse<T = any> {
    code: number
    message: string
    data: T
  }

  // 请求配置
  interface RequestConfig {
    url: string
    method?: RequestMethod
    data?: any
    header?: Record<string, string>
    timeout?: number
    showLoading?: boolean
    loadingText?: string
    showError?: boolean
    skipAuth?: boolean
  }

  // 请求拦截器
  interface RequestInterceptor {
    request?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>
    response?: <T>(response: BaseResponse<T>) => BaseResponse<T> | Promise<BaseResponse<T>>
    error?: (error: any) => any
  }

  // 上传文件配置
  interface UploadConfig {
    url: string
    filePath: string
    name: string
    formData?: Record<string, any>
    header?: Record<string, string>
    showLoading?: boolean
    loadingText?: string
  }

  // 下载文件配置
  interface DownloadConfig {
    url: string
    header?: Record<string, string>
    showLoading?: boolean
    loadingText?: string
  }

  // 错误类型
  interface RequestError {
    code: number
    message: string
    data?: any
    statusCode?: number
  }
}

// 导出命名空间中的类型以保持向后兼容
export type RequestMethod = RequestTypes.RequestMethod
export type BaseResponse<T = any> = RequestTypes.BaseResponse<T>
export type RequestConfig = RequestTypes.RequestConfig
export type RequestInterceptor = RequestTypes.RequestInterceptor
export type UploadConfig = RequestTypes.UploadConfig
export type DownloadConfig = RequestTypes.DownloadConfig
export type RequestError = RequestTypes.RequestError
