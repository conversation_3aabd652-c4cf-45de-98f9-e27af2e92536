/**
 * 音频相关工具函数
 */
import Taro from '@tarojs/taro'

/**
 * 播放音频
 * @param filePath 音频文件路径
 */
export const playVoice = (filePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.playVoice({
      filePath,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 暂停播放音频
 */
export const pauseVoice = (): void => {
  Taro.pauseVoice()
}

/**
 * 停止播放音频
 */
export const stopVoice = (): void => {
  Taro.stopVoice()
}
