/**
 * 律师模块类型声明
 * 基于 api.json 接口文档定义
 */

// 引用通用类型
/// <reference path="../common-types.d.ts" />

declare namespace LawyerAPI {
  // 律师等级类型
  type LawyerLevel = 0 | 1 | 2 | 3 | 4 | 5  //0无数据 1积分律师，2协办律师，3品牌律师，4金牌律师，5金牌大律师



  // 律师信息类型（基于 FindLawyerListDto）
  interface LawyerInfo extends CommonTypes.BaseEntity {
    userId: number
    province: string
    city: string
    district: string
    name: string
    personalProfile: string  // 个人简介
    signature?: string       // 个性化签名
    figurePhotoUrl: string   // 形象照地址
    lawFirm: string         // 律所名称
    lawFirmAddress: string  // 律所地址
    lawyerLevel: LawyerLevel
    lawyerField: string[]   // 律师擅长领域（字符串数组）
    isFollowed?: boolean    // 是否已关注（可选字段，用于详情页面）
  }

  // 律师统计数据类型
  interface LawyerCountDataDto extends CommonTypes.LawyerCountDataDto { }

  // 内容统计类型
  interface ContentNumDto extends CommonTypes.ContentNumDto { }

  // 获取律师列表请求参数
  interface GetLawyerListRequest extends CommonTypes.BaseSearchRequest {
    lawyerName?: string    // 律师姓名模糊搜索
    lawyerLevel?: LawyerLevel[]  // 律师等级
    province?: string      // 所在省份
    city?: string         // 所在城市
    district?: string     // 所在地区
    categoryId?: number   // 案件罪名分类ID
  }

  // 获取律师列表响应
  interface GetLawyerListResponse extends CommonTypes.PaginationResponse<LawyerInfo> { }

  // 获取律师详情请求参数
  interface GetLawyerDetailRequest {
    lawyerId: number  // 律师ID，取值于列表中的user_id
  }

  // 获取律师详情响应
  interface GetLawyerDetailResponse {
    lawyerInfo: LawyerInfo
    lawCase: CaseAPI.CaseListInfo[]      // 个人代表案例
    lawArticle: ArticleAPI.ArticleListInfo[]  // 个人代表著作
    lawyerCountData: LawyerCountDataDto   // 律师统计数据
    contentNum: ContentNumDto             // 个人内容统计
  }

  // 获取律师详情信息请求参数（基于 DetailInfoReq）
  interface GetLawyerDetailInfoRequest {
    lawyerId: number
  }

  // 获取律师详情信息响应（基于 DetailInfoRes）
  interface GetLawyerDetailInfoResponse {
    lawyerInfo: LawyerInfo
  }

  // 获取律师统计数据请求参数（基于 DetailLawyerCountReq）
  interface GetLawyerCountDataRequest {
    lawyerId: number
  }

  // 获取律师统计数据响应（基于 DetailLawyerCountRes）
  interface GetLawyerCountDataResponse {
    lawyerCountData: LawyerCountDataDto
  }

  // 获取律师内容统计请求参数（基于 DetailContentNumReq）
  interface GetLawyerContentNumRequest {
    lawyerId: number
  }

  // 获取律师内容统计响应（基于 DetailContentNumRes）
  interface GetLawyerContentNumResponse {
    contentNum: ContentNumDto
  }



  // 关注律师请求参数（基于 FollowLawyerReq）
  interface FollowLawyerRequest {
    lawyerId: number
  }

  // 关注律师响应（基于 FollowLawyerRes）
  interface FollowLawyerResponse { }

  // 取消关注律师请求
  interface CancelFollowLawyerRequest {
    lawyerId: number
  }

  // 取消关注律师响应
  interface CancelFollowLawyerResponse { }

  // 获取律师信息响应（律师个人中心）
  interface GetLawyerInfoResponse {
    lawyerInfo: LawyerInfo
  }

  // 更新律师名片响应
  interface UpdateBusinessCardResponse { }

  // 提交律师认证请求
  interface CommitLawyerAuthenticationRequest {
    name: string
    idCard: string
    lawyerCertificate: string
    practiceNumber: string
    lawFirm: string
  }

  // 提交律师认证响应
  interface CommitLawyerAuthenticationResponse { }

  // 获取律师关注列表请求参数
  interface GetLawyerFollowListRequest extends CommonTypes.BasePaginationRequest { }

  // 获取律师关注列表响应
  interface GetLawyerFollowListResponse extends CommonTypes.PaginationResponse<OwnerLawyerFollowListInfo> { }

  // 案件订单信息
  interface CaseOrderInfo {
    id: number
    orderNo: string
    caseTitle: string
    status: number
    amount: number
    createdAt: string
  }

  // 律师关注列表信息类型（基于 OwnerLawyerFollowListDto）
  interface OwnerLawyerFollowListInfo extends CommonTypes.BaseEntity {
    lawyerName: string
    lawyerId: number
    lawyerPhotoUrl: string
    followerName: string
    followerId: number
    createdAt: string
  }

  // 获取律师关注列表请求参数（基于 OwnerLawyerFollowListReq）
  interface GetOwnerLawyerFollowListRequest extends CommonTypes.BaseSearchRequest { }

  // 获取律师关注列表响应（基于 OwnerLawyerFollowListRes）
  interface GetOwnerLawyerFollowListResponse extends CommonTypes.PaginationResponse<OwnerLawyerFollowListInfo> { }

  // 获取律师个人信息响应（基于 OwnerLawyerInfoListRes）
  interface GetOwnerLawyerInfoResponse {
    userId: number
    province: string
    city: string
    district: string
    name: string
    personalProfile: string
    figurePhotoUrl: string
    lawFirm: string
    lawFirmAddress: string
    authStatus: number
    lawyerLevel: LawyerLevel
    fieldIdStr: string
  }

  // 更新律师名片请求参数（基于 OwnerBusinessCardReq）
  interface UpdateBusinessCardRequest {
    personalProfile?: string  // 个人简介
    figurePhotoUrl?: string   // 形象照地址
    lawFirm?: string         // 律所名称
    lawFirmAddress?: string  // 律所地址
    fieldIdArr?: number[]    // 律师擅长的案件领域（案件分类）
  }

  // 更新律师名片响应（基于 OwnerBusinessCardRes）
  interface UpdateBusinessCardResponse { }

  // 律师认证提交请求参数（基于 OwnerLawyerAuthReq）
  interface SubmitAuthenticationRequest {
    province: string
    city: string
    district: string
    phone: string
    lawFirm: string
    lawFirmAddress: string
    name: string
    idCard: string
    idCardFrontUrl: string
    idCardBackUrl: string
    isGoldenRescue: number
    licenseUrl: string
  }

  // 律师认证提交响应（基于 OwnerLawyerAuthRes）
  interface SubmitAuthenticationResponse { }

  // 我的案件订单信息
  interface MyCaseOrderInfo {
    id: number
    clientName: string      // 委托人姓名
    caseStage: string      // 案件阶段
    caseTypeId: number     // 案件类型ID
    createdAt: string
    // 根据实际API文档补充更多字段
    [key: string]: any
  }

  // 获取我的案件订单列表请求参数（基于 OwnerOrderListReq）
  interface GetMyCaseOrderListRequest extends CommonTypes.BasePaginationRequest {
    clientName?: string    // 委托人姓名（模糊搜索）
    caseStageId?: number    // 案件阶段ID
    caseTypeId?: number   // 案件类型ID
    orderStatus?: number // 订单状态：1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废
  }

  // 获取我的案件订单列表响应（基于 OwnerOrderListRes）
  interface GetMyCaseOrderListResponse extends CommonTypes.PaginationResponse<MyCaseOrderInfo> { }

  // 使用通用类型别名
  type PaginationResponse<T> = CommonTypes.PaginationResponse<T>
  type ListResponse<T> = CommonTypes.ListResponse<T>

  interface GetMyCaseOrderDetailResponse {
    id: number
    orderNo: string
    clientName: string
    clientId: number
    clientMobile: string
    caseTypeId: number
    caseTypeName: string
    caseStage: string
    caseStageId: number
    handlingAgency: string
    province: string
    city: string
    district: string
    orderStatus: number  // 订单状态：0-删除，1-已创建，2-待签约，3-跟进中，4-已完结，5-已作废
    source: string
    amountInvolvedOfCase: number
    paymentStatus: number  // 支付状态：1-未支付，2-支付完成，3-支付失败
    paymentAmount: number
    lawyerId: number
    lawyerName: string
    lawyerRequirements: string
    creator: string
    creatorId: number
    reviewTime: string
    createdAt: string
  }

  interface GetMyCaseOrderStageRecordsResponse extends CommonAPI.GetCaseOrderStageRecordsResponse { }

  // 律师案件订单阶段进度数据（使用通用类型）
  interface ProgressData extends CommonAPI.CaseOrderStageProgressData {
    stageFollowRecords: StageFollowRecordData[]
  }

  // 律师案件订单阶段记录数据（使用通用类型）
  interface StageFollowRecordData extends CommonAPI.CaseOrderStageRecordData { }

  interface FollowCaseOrderProgressRequest {
    orderStageId: number
    caseProgressId: number
    content: string
    imageUrls?: string
  }
}
