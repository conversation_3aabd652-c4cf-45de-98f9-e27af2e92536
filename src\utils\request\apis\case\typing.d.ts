/**
 * 案例模块类型声明
 * 基于 api.json 接口文档定义
 */

// 引用通用类型
/// <reference path="../../common-types.d.ts" />

declare namespace CaseAPI {
  // 案例详情信息类型（基于 FindLawCaseDetailDto）
  interface CaseDetailInfo extends CommonTypes.BaseEntity {
    title: string
    categoryId: number
    categoryName: string
    content: string  // HTML/富文本
    viewCount: number
    creator: string
    creatorId: number
    createdAt: string
  }

  // 案例列表信息类型（基于 FindLawCaseListDto）
  interface CaseListInfo extends CommonTypes.BaseEntity {
    title: string
    categoryId: number
    categoryName: string
    viewCount: number
    creator: string
    createdAt: string
  }

  // 案例分类类型（基于 LawCaseCategoryDto）
  interface CaseCategoryInfo extends CommonTypes.CategoryDto {}

  // 律师案例分类统计类型
  interface LawyerCaseCategoryCountDto extends CommonTypes.CategoryCountDto {}

  // 获取案例详情请求参数
  interface GetCaseDetailRequest {
    caseId: number
  }

  // 获取案例详情响应（基于 FindCaseDetailRes）
  interface GetCaseDetailResponse {
    detail: CaseDetailInfo
    lawyerInfo: LawyerAPI.LawyerInfo
  }

  // 获取案例列表请求参数（基于 FindCaseListReq）
  interface GetCaseListRequest extends CommonTypes.BasePaginationRequest {
    title?: string  // 案例标题模糊搜索
    categoryId?: number  // 分类ID
    lawyerId?: number  // 律师id，有值就是获取当前律师的案例
    isExcellent?: 1 | 2  // 是否优秀案例，1否，2是
  }

  // 获取案例列表响应（基于 FindCaseListRes）
  interface GetCaseListResponse extends CommonTypes.PaginationResponse<CaseListInfo> {}

  // 获取律师案例列表请求参数
  interface GetLawyerCaseListRequest extends CommonTypes.BasePaginationRequest {
    title?: string  // 案例标题模糊搜索
    categoryId?: number  // 分类ID
  }

  // 获取律师案例列表响应
  interface GetLawyerCaseListResponse extends CommonTypes.PaginationResponse<CaseListInfo> {}

  // 获取案例分类列表响应（基于 CaseCategoryListRes）
  interface GetCaseCategoryListResponse {
    list: CaseCategoryInfo[]
  }

  // 获取推荐案例列表请求参数（基于 RecommendCaseListReq）
  interface GetRecommendCaseListRequest extends CommonTypes.BaseSearchRequest {
    caseId: number
  }

  // 获取推荐案例列表响应（基于 RecommendCaseListRes）
  interface GetRecommendCaseListResponse extends CommonTypes.PaginationResponse<CaseListInfo> {}

  // 律师个人中心案例列表信息类型（基于 OwnerCenterLawCaseListDto）
  interface OwnerCenterCaseListInfo extends CommonTypes.BaseEntity {
    title: string
    categoryId: number
    categoryName: string
    viewCount: number
    creator: string
    createdAt: string
    status: number  // 状态：1待审核，2已发布，3已拒绝
  }

  // 获取律师个人中心案例列表请求参数（基于 OwnerCaseListReq）
  interface GetOwnerCenterCaseListRequest extends CommonTypes.BaseSearchRequest {
    title?: string  // 案例标题模糊搜索
    categoryId?: number  // 分类ID
  }

  // 获取律师个人中心案例列表响应（基于 OwnerCaseListRes）
  interface GetOwnerCenterCaseListResponse extends CommonTypes.PaginationResponse<OwnerCenterCaseListInfo> {
    lawyerCaseCategoryNum?: LawyerCaseCategoryCountDto
  }

  // 案件阶段类型（基于 CaseStageDto）
  interface CaseStageInfo extends CommonTypes.BaseEntity {
    name: string
  }

  // 获取案件阶段列表响应（基于 CaseStageListRes）
  interface GetCaseStageListResponse {
    list: CaseStageInfo[]
  }

  // 获取我的案例列表请求参数
  interface GetMyCaseListRequest extends CommonTypes.BasePaginationRequest {
    title?: string
    categoryId?: number
    status?: number
  }

  // 获取我的案例列表响应
  interface GetMyCaseListResponse extends CommonTypes.PaginationResponse<MyCaseInfo> {}

  // 获取我的案例详情响应
  interface GetMyCaseDetailResponse extends MyCaseInfo {}

  // 我的案例信息
  interface MyCaseInfo {
    id: number
    title: string
    categoryId: number
    categoryName: string
    content: string
    viewCount: number
    status: number  // 状态：0删除,1待审核,2审核通过,3审核不通过
    rejectReason?: string
    creator: string
    creatorId: number
    reviewer?: string
    reviewerId?: number
    modifier?: string
    reviewTime?: string
    createdAt: string
    updatedAt: string
  }



  // 发布案例请求参数（基于 OwnerLawCaseAddReq）
  interface PublishCaseRequest {
    title: string
    categoryId: number
    content: string
  }

  // 发布案例响应（基于 OwnerLawCaseAddRes）
  interface PublishCaseResponse {}

  // 修改案例请求参数（基于 OwnerLawCaseSaveReq）
  interface UpdateMyCaseRequest {
    title: string
    categoryId: number
    content: string
  }

  // 修改案例响应（基于 OwnerLawCaseSaveRes）
  interface UpdateMyCaseResponse {}

  // 使用通用类型别名
  type PaginationResponse<T> = CommonTypes.PaginationResponse<T>
  type ListResponse<T> = CommonTypes.ListResponse<T>
}
