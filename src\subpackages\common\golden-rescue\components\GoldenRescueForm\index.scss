/**
 * 黄金救援表单组件样式
 */

.golden-rescue-form {
  // 介绍区域
  .intro-section {
    background-color: #ffffff;
    margin-bottom: 20rpx;
    padding: 30rpx;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);

    .intro-content {
      .intro-desc {
        font-size: 26rpx;
        color: #333333;
        line-height: 1.8;
        margin-bottom: 24rpx;
        display: block;
        text-align: justify;
      }

      .intro-points {
        margin: 24rpx 0;

        .point-item {
          margin-bottom: 20rpx;
          padding: 20rpx;
          background-color: #f8f9fa;
          border-radius: 8rpx;
          border-left: 4rpx solid #BD8A4F;

          &:last-child {
            margin-bottom: 0;
          }

          .point-title {
            font-size: 24rpx;
            font-weight: 600;
            color: #BD8A4F;
            margin-bottom: 8rpx;
            display: block;
          }

          .point-desc {
            font-size: 22rpx;
            color: #555555;
            line-height: 1.8;
            display: block;
            text-align: justify;
          }
        }
      }

      .intro-note {
        font-size: 24rpx;
        color: #BD8A4F;
        font-weight: 600;
        margin-top: 20rpx;
        padding: 16rpx 20rpx;
        background: linear-gradient(135deg, rgba(189, 138, 79, 0.1), rgba(212, 165, 116, 0.1));
        border-radius: 8rpx;
        border: 1rpx solid rgba(189, 138, 79, 0.2);
        text-align: center;
        display: block;
      }
    }
  }

  .form-section {
    background-color: #ffffff;
    margin-bottom: 20rpx;
    padding: 30rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 30rpx;
      display: block;
    }
  }

  .submit-section {
    background-color: #ffffff;
    padding: 30rpx;
    margin-top: 20rpx;

    .submit-btn {
      width: 100%;
      height: 88rpx;
      background-color: #BD8A4F;
      color: #ffffff;
      font-size: 32rpx;
      font-weight: 600;
      border-radius: 44rpx;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        background-color: #a67a42;
      }

      &.button-disabled {
        background-color: #cccccc;
        color: #999999;
      }

      &.button-loading {
        background-color: #cccccc;
      }
    }
  }

  .form-input {
    height: 88rpx;
    background-color: #F7F7F7;
    border-radius: 8rpx;
    padding: 0 24rpx;
    font-size: 28rpx;
    color: #333333;
    border: none;

    &::placeholder {
      color: #999999;
    }

    &:focus {
      background-color: #f0f0f0;
    }
  }

  .form-textarea {
    width: 100%;
    min-height: 160rpx;
    background-color: #F7F7F7;
    border-radius: 8rpx;
    padding: 24rpx;
    font-size: 28rpx;
    color: #333333;
    border: none;
    box-sizing: border-box;

    &::placeholder {
      color: #999999;
    }

    &:focus {
      background-color: #f0f0f0;
    }
  }
}
