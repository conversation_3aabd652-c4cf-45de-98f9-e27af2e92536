/**
 * 请求相关的React Hooks
 */
import { useState, useEffect, useCallback } from 'react'
import { BaseResponse } from './types'

// 请求状态
interface RequestState<T> {
  data: T | null
  loading: boolean
  error: string | null
}

// useRequest Hook
export function useRequest<T = any>(
  requestFn: () => Promise<BaseResponse<T>>,
  options: {
    immediate?: boolean
    onSuccess?: (data: T) => void
    onError?: (error: string) => void
  } = {}
) {
  const { immediate = true, onSuccess, onError } = options
  
  const [state, setState] = useState<RequestState<T>>({
    data: null,
    loading: false,
    error: null
  })

  const run = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    try {
      const response = await requestFn()
      setState({
        data: response.data,
        loading: false,
        error: null
      })
      onSuccess?.(response.data)
    } catch (error: any) {
      const errorMessage = error.message || '请求失败'
      setState({
        data: null,
        loading: false,
        error: errorMessage
      })
      onError?.(errorMessage)
    }
  }, [requestFn, onSuccess, onError])

  useEffect(() => {
    if (immediate) {
      run()
    }
  }, [immediate, run])

  return {
    ...state,
    run,
    refresh: run
  }
}

// 分页请求Hook
export function usePagination<T = any>(
  requestFn: (params: { page: number; pageSize: number; [key: string]: any }) => Promise<BaseResponse<{
    list: T[]
    total: number
    page: number
    pageSize: number
  }>>,
  options: {
    pageSize?: number
    immediate?: boolean
    onSuccess?: (data: { list: T[]; total: number }) => void
    onError?: (error: string) => void
  } = {}
) {
  const { pageSize = 10, immediate = true, onSuccess, onError } = options
  
  const [state, setState] = useState({
    list: [] as T[],
    total: 0,
    page: 1,
    pageSize,
    loading: false,
    error: null as string | null,
    hasMore: true
  })

  const [params, setParams] = useState<Record<string, any>>({})

  const loadData = useCallback(async (page: number, isRefresh = false) => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    try {
      const response = await requestFn({
        page,
        pageSize,
        ...params
      })
      
      const { list = [], total = 0 } = response.data || {}
      const hasMore = page * pageSize < total

      setState(prev => ({
        ...prev,
        list: isRefresh ? list : [...prev.list, ...list],
        total,
        page,
        loading: false,
        hasMore
      }))
      
      onSuccess?.(response.data)
    } catch (error: any) {
      const errorMessage = error.message || '请求失败'
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }))
      onError?.(errorMessage)
    }
  }, [requestFn, pageSize, params, onSuccess, onError])

  // 刷新数据
  const refresh = useCallback(() => {
    setState(prev => ({ ...prev, page: 1, list: [], hasMore: true }))
    loadData(1, true)
  }, [loadData])

  // 加载更多
  const loadMore = useCallback(() => {
    if (!state.loading && state.hasMore) {
      const nextPage = state.page + 1
      setState(prev => ({ ...prev, page: nextPage }))
      loadData(nextPage)
    }
  }, [state.loading, state.hasMore, state.page, loadData])

  // 搜索
  const search = useCallback((searchParams: Record<string, any>) => {
    setParams(searchParams)
    setState(prev => ({ ...prev, page: 1, list: [], hasMore: true }))
    loadData(1, true)
  }, [loadData])

  useEffect(() => {
    if (immediate) {
      loadData(1, true)
    }
  }, [immediate, loadData])

  return {
    ...state,
    refresh,
    loadMore,
    search
  }
}

// 上传Hook
export function useUpload(
  uploadFn: (filePath: string) => Promise<BaseResponse<{ url: string }>>,
  options: {
    onSuccess?: (url: string) => void
    onError?: (error: string) => void
  } = {}
) {
  const { onSuccess, onError } = options
  
  const [state, setState] = useState({
    uploading: false,
    progress: 0,
    error: null as string | null
  })

  const upload = useCallback(async (filePath: string) => {
    setState({ uploading: true, progress: 0, error: null })
    
    try {
      const response = await uploadFn(filePath)
      setState({ uploading: false, progress: 100, error: null })
      onSuccess?.(response.data.url)
      return response.data.url
    } catch (error: any) {
      const errorMessage = error.message || '上传失败'
      setState({ uploading: false, progress: 0, error: errorMessage })
      onError?.(errorMessage)
      throw error
    }
  }, [uploadFn, onSuccess, onError])

  return {
    ...state,
    upload
  }
}
