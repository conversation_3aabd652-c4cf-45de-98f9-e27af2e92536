/**
 * 案件阶段时间线组件样式
 */

.case-stage-timeline {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  margin-bottom: 24rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);

  // 添加悬浮动画效果
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
  }

  &__header {
    padding: 32rpx 30rpx;
    background: linear-gradient(90deg, rgba(189, 138, 79, 0.05) 0%, rgba(189, 138, 79, 0.02) 100%);
    border-bottom: 1rpx solid rgba(240, 240, 240, 0.6);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 30rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 6rpx;
      height: 32rpx;
      background: linear-gradient(180deg, #BD8A4F 0%, #d4a574 100%);
      border-radius: 3rpx;
    }
  }

  &__title {
    font-size: 34rpx;
    font-weight: 700;
    color: #2c3e50;
    margin-left: 20rpx;
    letter-spacing: 0.5rpx;
  }

  &__content {
    padding: 32rpx 30rpx;
    background: rgba(255, 255, 255, 0.5);
  }
}

// 时间线阶段
.timeline-stage {
  position: relative;

  &__header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24rpx;
    position: relative;
  }

  &__dot-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 8rpx 24rpx 0 0;
    flex-shrink: 0;
    position: relative;
  }

  &__dot {
    width: 20rpx;
    height: 20rpx;
    border-radius: 50%;
    border: 3rpx solid;
    background-color: #ffffff;
    flex-shrink: 0;
    position: relative;
    z-index: 2;

    &.stage-processing {
      border-color: #3498db;
      background-color: #3498db;
      animation: pulse 2s infinite;
    }

    &.stage-completed {
      border-color: #27ae60;
      background-color: #27ae60;
    }

    &.stage-deleted {
      border-color: #e74c3c;
      background-color: #e74c3c;
    }

    &.stage-unknown {
      border-color: #95a5a6;
      background-color: #95a5a6;
    }
  }

  &__line {
    position: absolute;
    left: 50%;
    top: 26rpx;
    transform: translateX(-50%);
    width: 2rpx;
    height: calc(100% - 26rpx);
    background-color: #e5e5e5;
    z-index: 1;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 60%;
      background: linear-gradient(180deg, #BD8A4F 0%, transparent 100%);
    }
  }

  &__info {
    flex: 1;
    padding-top: 0;
  }

  &__name-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8rpx;
  }

  &__name {
    font-size: 30rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 8rpx;
    display: block;
  }

  &__status {
    font-size: 24rpx;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
    font-weight: 500;
    margin-bottom: 8rpx;
    display: inline-block;

    &.stage-processing {
      color: #3498db;
      background: rgba(52, 152, 219, 0.1);
    }

    &.stage-completed {
      color: #27ae60;
      background: rgba(39, 174, 96, 0.1);
    }

    &.stage-deleted {
      color: #e74c3c;
      background: rgba(231, 76, 60, 0.1);
    }

    &.stage-unknown {
      color: #95a5a6;
      background: rgba(149, 165, 166, 0.1);
    }
  }

  &__time {
    font-size: 24rpx;
    color: #999999;
    display: block;
  }
}

// 脉搏动画
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);
  }

  70% {
    box-shadow: 0 0 0 20rpx rgba(52, 152, 219, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
  }
}

// 进度数据
.timeline-progress {
  margin-left: 44rpx;
  position: relative;

  &__item {
    position: relative;
    margin-bottom: 40rpx;
    padding-left: 32rpx;

    &:last-child {
      margin-bottom: 0;

      &::before {
        display: none;
      }
    }

    // 时间线连接线
    &::before {
      content: '';
      position: absolute;
      left: 8rpx;
      top: 24rpx;
      width: 2rpx;
      height: calc(100% + 16rpx);
      background-color: #e5e5e5;
    }

    // 时间线节点
    &::after {
      content: '';
      position: absolute;
      left: 4rpx;
      top: 8rpx;
      width: 10rpx;
      height: 10rpx;
      background-color: #BD8A4F;
      border-radius: 50%;
      border: 2rpx solid #ffffff;
      box-shadow: 0 0 0 2rpx #BD8A4F;
    }

    // 已完成状态的节点
    &.done::after {
      background-color: #27ae60;
      box-shadow: 0 0 0 2rpx #27ae60;
    }
  }

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
  }

  &__name {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
  }

  &__status {
    margin-left: auto;
    font-size: 22rpx;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
    font-weight: 500;

    &.done {
      color: #27ae60;
      background: rgba(39, 174, 96, 0.1);
    }

    &.pending {
      color: #f39c12;
      background: rgba(243, 156, 18, 0.1);
    }
  }

  &__edit-icon {
    margin-left: 20rpx;
    width: 32rpx;
    height: 32rpx;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.9);
      opacity: 0.7;
    }
  }

  &__records {
    margin-top: 16rpx;
  }

  &__empty-records {
    margin-top: 16rpx;
  }
}

// 添加跟踪记录入口
.add-record-entry {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  background: linear-gradient(135deg, rgba(189, 138, 79, 0.05) 0%, rgba(189, 138, 79, 0.02) 100%);
  border: 2rpx dashed rgba(189, 138, 79, 0.3);
  border-radius: 12rpx;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, rgba(189, 138, 79, 0.08) 0%, rgba(189, 138, 79, 0.04) 100%);
    border-color: rgba(189, 138, 79, 0.5);
    transform: translateY(-1rpx);
  }

  &:active {
    transform: translateY(0);
    background: linear-gradient(135deg, rgba(189, 138, 79, 0.1) 0%, rgba(189, 138, 79, 0.06) 100%);
  }

  .add-record-icon {
    width: 28rpx;
    height: 28rpx;
    margin-right: 12rpx;
    opacity: 0.7;
    transition: all 0.3s ease;
  }

  .add-record-text {
    font-size: 26rpx;
    color: #BD8A4F;
    font-weight: 500;
    letter-spacing: 0.5rpx;
  }

  &:hover .add-record-icon {
    opacity: 1;
    transform: scale(1.1);
  }
}

// 跟进记录
.timeline-record {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 12rpx;

  &:last-child {
    margin-bottom: 0;
  }

  &__header {
    margin-bottom: 8rpx;
  }

  &__time {
    font-size: 22rpx;
    color: #999999;
  }

  &__content {
    font-size: 26rpx;
    color: #333333;
    line-height: 36rpx;
    margin-bottom: 12rpx;
    word-break: break-all;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__images {
    display: flex;
    flex-wrap: wrap;
    gap: 8rpx;
  }

  &__image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 8rpx;
    background-color: #f0f0f0;
  }
}