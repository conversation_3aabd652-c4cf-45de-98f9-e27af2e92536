/**
 * 律师列表组件
 * 
 * 展示最多3个律师卡片，支持外部传入筛选条件
 */
import React, { useState, useEffect } from 'react'
import { View, Image, Text } from '@tarojs/components'
import { lawyer<PERSON><PERSON> } from '@/utils/request/apis/lawyer'
import { STATUS_CODE } from '@/utils/request/config'
import { navigateToPage, showLoading, hideLoading } from '@/utils'


// 导入图片资源
import { LawyerCardBg, LawyerLevelIcon1, LawyerLevelIcon2, LawyerLevelIcon3, LawyerLevelIcon4, LawyerLevelIcon5, DefaultAvatar } from '@/constant/image'

import { LawyerListProps } from './types'
import './index.scss'


// 律师等级图标映射（已移到函数内部）

const LawyerList: React.FC<LawyerListProps> = ({
  filterParams,
  className = ''
}) => {
  // 律师列表数据
  const [lawyers, setLawyers] = useState<LawyerAPI.LawyerInfo[]>([])
  // 加载状态
  const [isLoading, setIsLoading] = useState(false)

  // 获取律师等级图标
  const getLevelIcon = (lawyerLevel: number): string => {
    const levelIconMap = {
      1: LawyerLevelIcon1,
      2: LawyerLevelIcon2,
      3: LawyerLevelIcon3,
      4: LawyerLevelIcon4,
      5: LawyerLevelIcon5
    }
    return levelIconMap[lawyerLevel] || LawyerLevelIcon1
  }

  // 加载律师列表数据
  const loadLawyers = async (val?: LawyerAPI.GetLawyerListRequest) => {
    try {
      setIsLoading(true)
      // 使用toast阻止用户操作
      showLoading()
      const params = {
        page: 1,
        pageSize: 3,
        ...val
      }
      const response = await lawyerApi.getLawyerList(params)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setLawyers(response.data.list || [])
      }
      hideLoading()
    } catch (error) {
      console.error('加载律师列表失败:', error)
    } finally {
      setIsLoading(false)
    }
  }



  // 处理律师卡片点击 - 跳转到律师详情页
  const handleLawyerClick = (lawyer: LawyerAPI.LawyerInfo) => {
    navigateToPage(`/subpackages/detail/lawyer/index?lawyerId=${lawyer.userId}`)
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadLawyers(filterParams)
  }, [filterParams])

  return (
    <View className={`lawyer-list ${className}`}>
      {isLoading ? (
        <View className='lawyer-list__loading'>
          <Text>加载中...</Text>
        </View>
      ) : (
        <>
          {lawyers.length > 0 ? (
            <View className='lawyer-list__container'>
              {lawyers.map((lawyer, index) => (
                <View
                  key={lawyer.userId}
                  className='lawyer-card'
                  style={{
                    // backgroundImage: `url(${layerCardBg})`,
                    marginBottom: index < lawyers.length - 1 ? '20rpx' : '0'
                  }}
                  onClick={() => handleLawyerClick(lawyer)}
                >
                  <Image
                    className='lawyer-card__bg'
                    src={LawyerCardBg}
                    mode='aspectFill'
                  />
                  <View className='lawyer-card__avatar'>
                    <Image
                      className='lawyer-card__avatar-img'
                      src={lawyer.figurePhotoUrl || DefaultAvatar}
                      mode='aspectFill'
                    />
                  </View>
                  <View className='lawyer-card__info'>
                    <View className='lawyer-card__top'>
                      <View className='lawyer-card__name-row'>
                        <Text className='lawyer-card__name'>{lawyer.name}</Text>
                        <Image
                          className='lawyer-card__level-icon'
                          src={getLevelIcon(lawyer.lawyerLevel)}
                          mode='aspectFit'
                        />
                      </View>
                      <Text className='lawyer-card__description'>
                        {lawyer.personalProfile}
                      </Text>
                    </View>
                    <View className='lawyer-card__bottom'>
                      <Text className='lawyer-card__city'>
                        {lawyer.province}{lawyer.city}{lawyer.district}
                      </Text>
                      <Text className='lawyer-card__office'>
                        {lawyer.lawFirm}
                      </Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View className='lawyer-list__empty'>
              <Text className='lawyer-list__empty-title'>暂无律师信息</Text>
              <Text className='lawyer-list__empty-desc'>当前筛选条件下没有找到相关律师，请尝试调整搜索条件</Text>
            </View>
          )}
        </>
      )}
    </View>
  )
}

// 导出组件
export default LawyerList

// 导出类型
export type { LawyerListProps } from './types'
