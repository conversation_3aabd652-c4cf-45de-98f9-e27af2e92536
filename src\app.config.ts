export default defineAppConfig({
  pages: [
    'pages/index/index',
    'pages/lawyer/index',
    'pages/case/index',
    'pages/mine/index'
  ],
  window: {
    navigationStyle: "custom",
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '律师咨询平台',
    navigationBarTextStyle: 'black'
  },
  tabBar: {
    color: '#999999',
    selectedColor: '#BD8A4F',
    backgroundColor: '#000000',
    borderStyle: 'white',
    list: [
      {
        pagePath: 'pages/index/index',
        text: '首页',
        iconPath: 'assets/images/tabbar-icon/Home.png',
        selectedIconPath: 'assets/images/tabbar-icon/Home_active.png'
      },
      {
        pagePath: 'pages/lawyer/index',
        text: '找律师',
        iconPath: 'assets/images/tabbar-icon/Search.png',
        selectedIconPath: 'assets/images/tabbar-icon/Search_active.png'
      },
      {
        pagePath: 'pages/case/index',
        text: '案例',
        iconPath: 'assets/images/tabbar-icon/Bookmark.png',
        selectedIconPath: 'assets/images/tabbar-icon/Bookmark_active.png'
      },
      {
        pagePath: 'pages/mine/index',
        text: '我的',
        iconPath: 'assets/images/tabbar-icon/User.png',
        selectedIconPath: 'assets/images/tabbar-icon/User_active.png'
      }
    ]
  },
  subPackages: [
    {
      root: 'subpackages/detail',
      pages: [
        'lawyer/index',
        'article/index',
        'case/index',
        'dynamics/index'
      ]
    },
    {
      root: 'subpackages/agreement',
      pages: [
        'privacy-policy/index',
        'user-agreement/index'
      ]
    },
    {
      root: 'subpackages/common',
      pages: [
        'about-us/index',
        'company-profile/index',
        'contract-management/index',
        'excellent-cases/index',
        'golden-rescue/index'
      ]
    },
    {
      root: 'subpackages/lawyer',
      pages: [
        'edit-business-card/index',
        'lawyer-article-manage/index',
        'lawyer-case-detail/index',
        'lawyer-case-manage/index',
        'lawyer-case-orders/index',
        'lawyer-dynamics-manage/index',
        'lawyer-entry/index',
        'lawyer-list/index'
      ]
    },
    {
      root: 'subpackages/login',
      pages: [
        'index'
      ]
    },
    {
      root: 'subpackages/my',
      pages: [
        'article-collection/index',
        'my-case-detail/index',
        'my-cases/index',
        'my-follow/index'
      ]
    },
    {
      root: 'subpackages/publish',
      pages: [
        'publish-article/index',
        'publish-case/index',
        'publish-dynamic/index'
      ]
    }
  ],
  lazyCodeLoading: 'requiredComponents',
  preloadRule: {
    "pages/index/index": {
      "network": "all",
      "packages": ["subpackages/detail", "subpackages/common", "subpackages/my"]
    },
    "pages/mine/index": {
      "network": "all",
      "packages": ["subpackages/login", "subpackages/publish", "subpackages/agreement", "subpackages/lawyer"]
    }
  },
  permission: {
    "scope.userInfo": {
      "desc": "用于完善会员资料"
    },
    "scope.phoneNumber": {
      "desc": "用于关联手机号"
    }
  }
})
