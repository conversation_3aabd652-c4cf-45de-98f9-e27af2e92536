/**
 * 存储相关工具函数
 */
import Taro from '@tarojs/taro'

/**
 * 设置本地存储
 * @param key 存储键
 * @param data 存储数据
 * @param sync 是否同步存储
 */
export const setStorage = async (
  key: string,
  data: any,
  sync: boolean = false
): Promise<void> => {
  try {
    if (sync) {
      Taro.setStorageSync(key, data)
    } else {
      await Taro.setStorage({ key, data })
    }
  } catch (error) {
    console.error('存储数据失败:', error)
    throw error
  }
}

/**
 * 获取本地存储（有默认值）
 * @param key 存储键
 * @param defaultValue 默认值
 * @param sync 是否同步获取
 */
export function getStorage<T,>(
  key: string,
  defaultValue: T,
  sync?: boolean
): Promise<T>

/**
 * 获取本地存储（无默认值）
 * @param key 存储键
 * @param sync 是否同步获取
 */
export function getStorage<T = any>(
  key: string,
  sync?: boolean
): Promise<T | null>

/**
 * 获取本地存储实现
 */
export async function getStorage<T = any>(
  key: string,
  defaultValueOrSync?: T | boolean,
  sync: boolean = false
): Promise<T | null> {
  // 参数解析
  let defaultValue: T | null = null
  let isSync = sync

  if (typeof defaultValueOrSync === 'boolean') {
    // 第二个参数是 sync
    isSync = defaultValueOrSync
  } else {
    // 第二个参数是 defaultValue
    defaultValue = defaultValueOrSync as T
  }

  try {
    if (isSync) {
      const result = Taro.getStorageSync(key)
      if (result !== undefined && result !== null) {
        return result
      }
      return defaultValue
    } else {
      const result = await Taro.getStorage({ key })
      if (result.data !== undefined && result.data !== null) {
        return result.data
      }
      return defaultValue
    }
  } catch (error) {
    console.warn('获取存储数据失败:', error)
    return defaultValue
  }
}

/**
 * 移除本地存储
 * @param key 存储键
 * @param sync 是否同步移除
 */
export const removeStorage = async (
  key: string,
  sync: boolean = false
): Promise<void> => {
  try {
    if (sync) {
      Taro.removeStorageSync(key)
    } else {
      await Taro.removeStorage({ key })
    }
  } catch (error) {
    console.error('移除存储数据失败:', error)
    throw error
  }
}

/**
 * 清空本地存储
 * @param sync 是否同步清空
 */
export const clearStorage = async (sync: boolean = false): Promise<void> => {
  try {
    if (sync) {
      Taro.clearStorageSync()
    } else {
      await Taro.clearStorage()
    }
  } catch (error) {
    console.error('清空存储失败:', error)
    throw error
  }
}
