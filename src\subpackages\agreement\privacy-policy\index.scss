.privacy-policy-page {
  .policy-content {
    padding: 30rpx;

    .policy-section {
      margin-bottom: 50rpx;
      background: #fafafa;
      border-radius: 16rpx;
      padding: 30rpx;
      border-left: 8rpx solid #BD8A4F;

      .section-title {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
        margin-bottom: 24rpx;
        padding-bottom: 16rpx;
        border-bottom: 2rpx solid #e9ecef;
      }

      .section-content {
        display: block;
        font-size: 28rpx;
        color: #666666;
        line-height: 44rpx;
        text-align: justify;
      }

      .content-item {
        display: block;
        margin-bottom: 20rpx;
        padding-left: 20rpx;
        position: relative;

        &:last-child {
          margin-bottom: 0;
        }

        &::before {
          content: "•";
          position: absolute;
          left: 0;
          top: 0;
          color: #BD8A4F;
          font-weight: bold;
          font-size: 32rpx;
        }
      }

      .highlight-text {
        color: #BD8A4F;
        font-weight: 500;
      }

      .important-note {
        background: #fff3e0;
        border: 2rpx solid #ffcc80;
        border-radius: 12rpx;
        padding: 20rpx;
        margin-top: 20rpx;

        .note-text {
          font-size: 26rpx;
          color: #e65100;
          line-height: 40rpx;
        }
      }
    }

    .update-time {
      margin-top: 60rpx;
      padding: 30rpx;
      background: #f8f9fa;
      border-radius: 16rpx;
      text-align: center;

      .update-text {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
}
