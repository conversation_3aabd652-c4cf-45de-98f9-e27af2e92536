/**
 * 小程序页面相关工具函数
 */
import Taro from '@tarojs/taro'

/**
 * 获取当前页面栈
 */
export const getCurrentPages = (): any[] => {
  return Taro.getCurrentPages()
}

/**
 * 获取当前页面路径
 */
export const getCurrentPagePath = (): string => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  return currentPage ? `/${currentPage.route}` : ''
}

/**
 * 获取当前页面参数
 */
export const getCurrentPageOptions = (): Record<string, any> => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  return currentPage ? currentPage.options || {} : {}
}

/**
 * 设置页面标题
 * @param title 页面标题
 */
export const setNavigationBarTitle = (title: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.setNavigationBarTitle({
      title,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 设置导航栏颜色
 * @param frontColor 前景颜色
 * @param backgroundColor 背景颜色
 */
export const setNavigationBarColor = (options: {
  frontColor: '#000000' | '#ffffff'
  backgroundColor: string
  animation?: {
    duration?: number
    timingFunc?: 'linear' | 'easeIn' | 'easeOut' | 'easeInOut'
  }
}): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.setNavigationBarColor({
      ...options,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 显示导航栏加载动画
 */
export const showNavigationBarLoading = (): void => {
  Taro.showNavigationBarLoading()
}

/**
 * 隐藏导航栏加载动画
 */
export const hideNavigationBarLoading = (): void => {
  Taro.hideNavigationBarLoading()
}
