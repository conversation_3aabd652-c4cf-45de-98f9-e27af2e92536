/**
 * 关于我们页面样式
 */

.about-us-page {
  height: 100vh;
  background-color: #f8f9fa;
}

.about-us-content {
  padding: 0 30rpx 40rpx;

  // 头部标语区域
  .hero-section {
    background: linear-gradient(135deg, #BD8A4F 0%, #D4A574 100%);
    margin: 0 -30rpx 40rpx;
    padding: 60rpx 40rpx;
    text-align: center;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
    }

    .hero-slogan {
      position: relative;
      z-index: 1;

      .slogan-main {
        display: block;
        font-size: 40rpx;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 20rpx;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
        letter-spacing: 2rpx;
      }

      .slogan-sub {
        display: block;
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.5;
        font-weight: 400;
      }
    }
  }

  // 企业介绍区域
  .company-intro-section {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .section-header {
      text-align: left;
      margin-bottom: 40rpx;

      .section-title {
        display: block;
        font-size: 32rpx;
        font-weight: 700;
        color: #BD8A4F;
        margin-bottom: 16rpx;
      }

      .section-subtitle {
        display: block;
        font-size: 27rpx;
        font-weight: 600;
        color: #333333;
        line-height: 1.6;
      }
    }

    .intro-content {
      .intro-paragraph {
        margin-bottom: 30rpx;

        .intro-text {
          font-size: 28rpx;
          color: #333333;
          line-height: 1.8;
          text-align: justify;
        }
      }

      .intro-highlight-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12rpx;
        padding: 30rpx;
        margin: 30rpx 0;
        border-left: 6rpx solid #BD8A4F;

        .highlight-title {
          display: block;
          font-size: 30rpx;
          font-weight: 700;
          color: #BD8A4F;
          margin-bottom: 20rpx;
        }

        .highlight-text {
          font-size: 28rpx;
          color: #333333;
          line-height: 1.8;
          text-align: justify;
        }
      }

      .beliefs-section {
        margin: 40rpx 0;

        .beliefs-title {
          display: block;
          font-size: 30rpx;
          font-weight: 700;
          color: #333333;
          margin-bottom: 30rpx;
          text-align: center;
        }

        .beliefs-list {
          .belief-item {
            display: flex;
            align-items: flex-start;
            background: #ffffff;
            border-radius: 12rpx;
            padding: 30rpx;
            margin-bottom: 20rpx;
            box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
            border: 1rpx solid #f0f0f0;

            .belief-icon {
              width: 60rpx;
              height: 60rpx;
              background: linear-gradient(135deg, #BD8A4F, #D4A574);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 24rpx;
              font-weight: 700;
              color: #ffffff;
              margin-right: 20rpx;
              flex-shrink: 0;
            }

            .belief-content {
              flex: 1;

              .belief-label {
                display: block;
                font-size: 28rpx;
                font-weight: 600;
                color: #333333;
                margin-bottom: 12rpx;
              }

              .belief-desc {
                font-size: 26rpx;
                color: #666666;
                line-height: 1.7;
                text-align: justify;
              }
            }
          }
        }
      }

      .intro-conclusion {
        background: linear-gradient(135deg, #BD8A4F 0%, #D4A574 100%);
        border-radius: 16rpx;
        padding: 40rpx;
        margin-top: 40rpx;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
          opacity: 0.3;
        }

        .conclusion-highlight {
          font-size: 30rpx;
          font-weight: 600;
          color: #ffffff;
          line-height: 1.8;
          text-align: center;
          position: relative;
          z-index: 1;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
        }
      }
    }
  }

  // 公司介绍区域
  .intro-section {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .intro-text {
      font-size: 28rpx;
      color: #333333;
      line-height: 1.8;
      text-align: justify;
    }
  }

  // 章节标题
  .section-header {
    margin-bottom: 30rpx;

    .section-title {
      display: block;
      font-size: 32rpx;
      font-weight: 700;
      color: #BD8A4F;
      margin-bottom: 12rpx;
      position: relative;
      padding-left: 20rpx;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 8rpx;
        height: 32rpx;
        background: linear-gradient(135deg, #BD8A4F, #D4A574);
        border-radius: 4rpx;
      }
    }

    .section-subtitle {
      display: block;
      font-size: 24rpx;
      color: #666666;
      line-height: 1.5;
      padding-left: 20rpx;
    }
  }

  // 三重革新区域
  .innovation-section {
    margin-bottom: 40rpx;

    .innovation-list {
      .innovation-item {
        background: #ffffff;
        border-radius: 16rpx;
        padding: 30rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
        border-left: 6rpx solid #BD8A4F;

        .innovation-header {
          display: flex;
          align-items: flex-start;
          margin-bottom: 20rpx;

          .innovation-icon {
            font-size: 32rpx;
            margin-right: 16rpx;
            margin-top: 4rpx;
            flex-shrink: 0;
          }

          .innovation-title {
            flex: 1;
            font-size: 28rpx;
            font-weight: 600;
            color: #333333;
            line-height: 1.4;
          }
        }

        .innovation-desc {
          font-size: 26rpx;
          color: #666666;
          line-height: 1.7;
          text-align: justify;
          padding-left: 48rpx;
        }
      }
    }
  }

  // 平台优势区域
  .advantage-section {
    margin-bottom: 40rpx;

    .advantage-list {
      .advantage-item {
        background: #ffffff;
        border-radius: 16rpx;
        padding: 30rpx;
        margin-bottom: 16rpx;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: flex-start;

        .advantage-label {
          background: linear-gradient(135deg, #BD8A4F, #D4A574);
          color: #ffffff;
          font-size: 24rpx;
          font-weight: 600;
          padding: 12rpx 20rpx;
          border-radius: 20rpx;
          margin-right: 20rpx;
          flex-shrink: 0;
          white-space: nowrap;
        }

        .advantage-desc {
          flex: 1;
          font-size: 26rpx;
          color: #666666;
          line-height: 1.6;
          margin-top: 8rpx;
        }
      }
    }
  }

  // 结语区域
  .conclusion-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
    border: 2rpx solid #BD8A4F;
    position: relative;

    &::before {
      content: '"';
      position: absolute;
      top: 20rpx;
      left: 20rpx;
      font-size: 60rpx;
      color: #BD8A4F;
      opacity: 0.3;
      font-family: serif;
      line-height: 1;
    }

    &::after {
      content: '"';
      position: absolute;
      bottom: 20rpx;
      right: 20rpx;
      font-size: 60rpx;
      color: #BD8A4F;
      opacity: 0.3;
      font-family: serif;
      line-height: 1;
    }

    .conclusion-text {
      font-size: 28rpx;
      color: #333333;
      line-height: 1.8;
      text-align: justify;
      font-style: italic;
      position: relative;
      z-index: 1;
    }
  }

  // 公司信息区域
  .company-info-section {
    margin-bottom: 40rpx;

    .company-info-card {
      background: #ffffff;
      border-radius: 16rpx;
      padding: 30rpx;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
      border: 1rpx solid #f0f0f0;

      .company-info-item {
        display: flex;
        align-items: flex-start;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f5f5f5;
        position: relative;

        &:last-child {
          border-bottom: none;
          padding-bottom: 0;
        }

        &:first-child {
          padding-top: 0;
        }

        // 电话项可点击样式
        &.phone-item {
          transition: all 0.2s ease;
          border-radius: 12rpx;
          margin: 0 -16rpx;
          padding: 20rpx 16rpx;

          &:hover {
            background-color: #f8f9fa;
          }

          &:active {
            background-color: #e9ecef;
            transform: scale(0.98);
          }
        }

        .info-icon {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          margin-right: 20rpx;
          margin-top: 4rpx;
          flex-shrink: 0;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 20rpx;
            color: #ffffff;
            font-weight: 600;
          }

          &.company-icon {
            background: linear-gradient(135deg, #BD8A4F, #D4A574);

            &::before {
              content: '企';
            }
          }

          &.address-icon {
            background: linear-gradient(135deg, #4CAF50, #66BB6A);

            &::before {
              content: '址';
            }
          }

          &.phone-icon {
            background: linear-gradient(135deg, #2196F3, #42A5F5);

            &::before {
              content: '电';
            }
          }
        }

        .info-content {
          flex: 1;
          display: flex;
          flex-direction: column;

          .info-label {
            font-size: 24rpx;
            color: #999999;
            margin-bottom: 8rpx;
            font-weight: 500;
          }

          .info-value {
            font-size: 28rpx;
            color: #333333;
            line-height: 1.6;
            word-break: break-all;
          }
        }

        .copy-hint {
          display: flex;
          align-items: center;
          margin-left: 16rpx;
          opacity: 0.7;
          transition: opacity 0.2s ease;

          .copy-text {
            font-size: 22rpx;
            color: #BD8A4F;
            background: rgba(189, 138, 79, 0.1);
            padding: 6rpx 12rpx;
            border-radius: 12rpx;
            border: 1rpx solid rgba(189, 138, 79, 0.2);
            font-weight: 500;
          }
        }


      }
    }
  }
}