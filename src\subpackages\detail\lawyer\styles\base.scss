/**
 * 律师详情页面基础样式
 */

// 导航栏左侧样式
.nav-left {
  display: flex;
  align-items: center;

  &__icon {
    width: 44rpx;
    height: 44rpx;
    padding-right: 20rpx;
  }

  &__text {
    font-weight: 800;
    font-size: 36rpx;
    color: #000000;
  }
}

.lawyer-detail {
  position: relative;
}

.lawyer-detail-nav-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--taro-nav-height, 420rpx) + 280rpx); // 导航栏高度 + 280rpx
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
}

.lawyer-detail {
  &-bg {
    background-repeat: no-repeat;
    z-index: -1;
  }

  &__content {
    padding: 30rpx 30rpx 0;
  }

  // Tab内容
  &__tab-content {
    // 内容区域样式
  }
}
