/**
 * 图片上传字段组件
 */
import React, { useState } from 'react'
import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { uploadApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
import './index.scss'

interface ImageUploadFieldProps {
  label?: string
  tip?: string
  maxCount?: number
  imageUrls: string[]
  onChange: (urls: string[]) => void
  uploading?: boolean
  onUploadingChange?: (uploading: boolean) => void
  className?: string
}

const ImageUploadField: React.FC<ImageUploadFieldProps> = ({
  label = '相关图片（可选）',
  tip = '最多可上传15张图片',
  maxCount = 15,
  imageUrls,
  onChange,
  uploading = false,
  onUploadingChange,
  className = ''
}) => {
  const [internalUploading, setInternalUploading] = useState(false)
  
  const isUploading = uploading || internalUploading
  
  // 选择图片
  const handleChooseImage = () => {
    Taro.chooseImage({
      count: maxCount - imageUrls.length,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        const tempFilePaths = res.tempFilePaths

        const setUploadingState = (state: boolean) => {
          setInternalUploading(state)
          onUploadingChange?.(state)
        }

        setUploadingState(true)
        
        // 显示上传中提示
        Taro.showToast({
          title: '图片上传中，请稍候...',
          icon: 'loading',
          duration: 0,
          mask: true
        })

        try {
          const uploadPromises = tempFilePaths.map(async (filePath) => {
            const uploadRes = await uploadApi.uploadImage(filePath)
            if (uploadRes.code === STATUS_CODE.SUCCESS && uploadRes.data) {
              return uploadRes.data.url
            }
            throw new Error('上传失败')
          })

          const uploadedUrls = await Promise.all(uploadPromises)
          onChange([...imageUrls, ...uploadedUrls])

          // 隐藏上传中提示
          Taro.hideToast()
          
          Taro.showToast({
            title: '上传成功',
            icon: 'success'
          })
        } catch (error) {
          console.error('图片上传失败:', error)
          
          // 隐藏上传中提示
          Taro.hideToast()
          
          Taro.showToast({
            title: '上传失败',
            icon: 'none'
          })
        } finally {
          setUploadingState(false)
        }
      }
    })
  }

  // 删除图片
  const handleDeleteImage = (index: number) => {
    onChange(imageUrls.filter((_, i) => i !== index))
  }

  // 预览图片
  const handlePreviewImage = (current: string) => {
    Taro.previewImage({
      current,
      urls: imageUrls
    })
  }

  return (
    <View className={`image-upload-field ${className}`}>
      <Text className='image-upload-field__label'>{label}</Text>
      <View className='image-upload-field__images'>
        {imageUrls.map((url, index) => (
          <View key={index} className='image-upload-field__image-item'>
            <Image
              className='image-upload-field__image'
              src={url}
              mode='aspectFill'
              onClick={() => handlePreviewImage(url)}
            />
            <View
              className='image-upload-field__image-delete'
              onClick={() => handleDeleteImage(index)}
            >
              <Text className='image-upload-field__image-delete-text'>×</Text>
            </View>
          </View>
        ))}

        {imageUrls.length < maxCount && (
          <View
            className='image-upload-field__image-add'
            onClick={handleChooseImage}
          >
            <Text className='image-upload-field__image-add-text' style={{ fontSize: isUploading ? '24rpx' : '48rpx' }}>
              {isUploading ? '上传中...' : '+'}
            </Text>
          </View>
        )}
      </View>
      <Text className='image-upload-field__tip'>{tip}</Text>
    </View>
  )
}

export default ImageUploadField
