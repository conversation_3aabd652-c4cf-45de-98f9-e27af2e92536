/**
 * 动态详情页面样式
 */
// 动态信息卡片
.dynamics-info-card {
  background: #F8F8F8;
  padding: 55rpx 30rpx;
  margin-bottom: 30rpx;

  // 标题
  &__title {
    display: block;
    font-size: 40rpx;
    font-weight: 600;
    color: #333;
    line-height: 48rpx;
    margin-bottom: 20rpx;
    word-break: break-word;
  }

  // 信息模块
  &__meta {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  // 分类名称
  &__category {
    color: #BD8A4F;
    font-size: 20rpx;
    font-weight: 600;
    margin-right: 20rpx;
  }

  // 时间容器
  &__time {
    display: flex;
    align-items: center;
    margin-right: 20rpx;
  }

  &__time-icon {
    width: 25rpx;
    height: 25rpx;
    margin-right: 5rpx;
  }

  &__time-text {
    color: #9C9AA0;
    font-size: 20rpx;
    font-weight: 600;
  }

  // 浏览量容器
  &__view {
    display: flex;
    align-items: center;
  }

  &__view-icon {
    width: 25rpx;
    height: 25rpx;
    margin-right: 5rpx;
  }

  &__view-text {
    color: #9C9AA0;
    font-size: 20rpx;
    font-weight: 600;
  }
}

// 动态内容
.dynamics-content {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 40rpx;

  &__title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
    line-height: 36rpx;
  }

  &__text {
    font-size: 30rpx;
    color: #333;
    line-height: 44rpx;
    word-break: break-word;
    white-space: pre-wrap;
  }
}

.dynamics-detail-list {
  padding-top: 30rpx;
  display: flex;
  flex-direction: column;
  background: #F8F8F8;

  &__header {
    padding: 0 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__title {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    &-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 12rpx;
    }

    &-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #000000;
    }
  }

  &__more {
    text-align: right;
    margin-bottom: 24rpx;

    text {
      font-size: 28rpx;
      color: #828D99;
    }
  }
}
.dynamics-actions {
  display: flex;
  gap: 30rpx;
  padding: 20rpx 0;

  &__btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 600;

    &::after {
      border: none;
    }

    // 主要按钮样式
    &[type="primary"] {
      background: #BD8A4F;
      border: none;
    }

    // 次要按钮样式
    &[plain] {
      background: transparent;
      color: #BD8A4F;
      border: 2rpx solid #BD8A4F;
    }
  }
}
