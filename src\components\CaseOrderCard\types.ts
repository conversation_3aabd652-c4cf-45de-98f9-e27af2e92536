/**
 * CaseOrderCard 组件类型定义
 */

// 通用案件订单信息接口
export interface CaseOrderInfo {
  id: number
  orderNo?: string
  clientName?: string
  caseTypeName?: string
  caseStage?: string
  handlingAgency?: string
  province?: string
  city?: string
  district?: string
  orderStatus?: number
  amountInvolvedOfCase?: number
  paymentStatus?: number
  paymentAmount?: number
  lawyerId?: number
  lawyerName?: string
  lawyerRequirements?: string
  createdAt: string
  [key: string]: any
}

// 组件属性
export interface CaseOrderCardProps {
  // 案件订单信息
  caseOrder: CaseOrderInfo
  // 视角类型：member(委托人) 或 lawyer(律师)
  viewType?: 'member' | 'lawyer'
  // 点击回调
  onClick?: (caseOrder: CaseOrderInfo) => void
  // 自定义样式类名
  className?: string
}
