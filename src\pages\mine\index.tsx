import React, { useMemo, useState, useCallback, useEffect } from 'react'

import { View, Image, Button } from '@tarojs/components'
import Taro, { useShareAppMessage, useShareTimeline } from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'
import { lawyer<PERSON><PERSON>, userApi, commonApi } from '@/utils/request/apis'
import { uploadApi } from '@/utils/request/apis/upload'
import { STATUS_CODE } from '@/utils/request/config'
import UserCacheManager from '@/utils/cache/userCache'
import { debounceForMiniProgram } from '@/utils/helpers/common'
import { makePhoneCall } from '@/utils/helpers/wechat'
import CacheManager, { CACHE_KEYS } from '@/utils/cache'

// 导入律师等级图标
import { LawyerLevelIcon1, LawyerLevelIcon2, LawyerLevelIcon3, LawyerLevelIcon4, LawyerLevelIcon5, HomeBg } from '@/constant/image'

// 导入功能图标
import idCardIcon from '@/assets/images/common-icon/id_card.png'
import verifyIcon from '@/assets/images/common-icon/verify.png'
import bookIcon from '@/assets/images/common-icon/book.png'
import starFillIcon from '@/assets/images/common-icon/star_fill.png'
import bagIcon from '@/assets/images/common-icon/bag.png'
import heartFillIcon from '@/assets/images/common-icon/heart_fill.png'
import headsetIcon from '@/assets/images/common-icon/headset.png'
import DefaultAvatar from '@/assets/images/common-icon/avatar.png'
import SendIcon from '@/assets/images/common-icon/send.png'
import BookMarkIcon from '@/assets/images/common-icon/book_mark_color.png'
import BookMarkFillIcon from '@/assets/images/common-icon/book_mark_fill.png'
import TelegramIcon from '@/assets/images/common-icon/telegram.png'
import bookOpenIcon from '@/assets/images/common-icon/book_open.png'
import LogoutIcon from '@/assets/images/common-icon/logout.png'

import './index.scss'

interface MenuModules {
  title: string
  items: MenuModulesItem[]
}

interface MenuModulesItem {
  key: string
  icon: string
  text: string
}

// 菜单配置接口定义

// 菜单项配置常量
const MENU_ITEMS = {
  // 便捷功能菜单项
  CONVENIENT: {
    ELECTRONIC_CARD: {
      key: 'electronic-card',
      icon: idCardIcon,
      text: '电子名片'
    },
    LAWYER_AUTH: {
      key: 'lawyer-auth',
      icon: verifyIcon,
      text: '律师认证'
    },
    PUBLISH_DYNAMIC: {
      key: 'publish-dynamic',
      icon: SendIcon,
      text: '发布动态'
    },
    PUBLISH_ARTICLE: {
      key: 'publish-article',
      icon: bookOpenIcon,
      text: '发布文章'
    },
    PUBLISH_CASE: {
      key: 'publish-case',
      icon: BookMarkIcon,
      text: '上传案例'
    }
  },
  // 内容管理菜单项
  CONTENT: {
    PERSONAL_COLLECTION: {
      key: 'personal-collection',
      icon: starFillIcon,
      text: '个人收藏'
    },
    DYNAMIC_MANAGE: {
      key: 'dynamic-manage',
      icon: TelegramIcon,
      text: '动态管理'
    },
    ARTICLE_MANAGE: {
      key: 'article-manage',
      icon: bookIcon,
      text: '文章管理'
    },
    CASE_MANAGE: {
      key: 'case-manage',
      icon: BookMarkFillIcon,
      text: '案例管理'
    }
  },
  // 服务管理菜单项
  SERVICE: {
    MY_CASES: {
      key: 'my-cases',
      icon: bagIcon,
      text: '我的案件'
    },
    MY_FOLLOW: {
      key: 'my-follow',
      icon: heartFillIcon,
      text: '我的关注'
    },
    CONTACT_SERVICE: {
      key: 'contact-service',
      icon: headsetIcon,
      text: '联系客服'
    },
    PLACEHOLDER: {
      key: '-',
      icon: '',
      text: ''
    }
  }
}

// 菜单模块配置生成器
const createMenuModules = {
  // 创建便捷功能模块
  convenient: (isAuthenticated: boolean): MenuModules => ({
    title: '便捷功能',
    items: [
      isAuthenticated ? MENU_ITEMS.CONVENIENT.ELECTRONIC_CARD : MENU_ITEMS.CONVENIENT.LAWYER_AUTH,
      MENU_ITEMS.CONVENIENT.PUBLISH_DYNAMIC,
      MENU_ITEMS.CONVENIENT.PUBLISH_ARTICLE,
      MENU_ITEMS.CONVENIENT.PUBLISH_CASE
    ]
  }),

  // 创建内容管理模块
  content: (): MenuModules => ({
    title: '内容管理',
    items: [
      MENU_ITEMS.CONTENT.PERSONAL_COLLECTION,
      MENU_ITEMS.CONTENT.DYNAMIC_MANAGE,
      MENU_ITEMS.CONTENT.ARTICLE_MANAGE,
      MENU_ITEMS.CONTENT.CASE_MANAGE
    ]
  }),

  // 创建服务管理模块
  service: (): MenuModules => ({
    title: '服务管理',
    items: [
      MENU_ITEMS.SERVICE.MY_CASES,
      MENU_ITEMS.SERVICE.MY_FOLLOW,
      MENU_ITEMS.SERVICE.CONTACT_SERVICE,
      MENU_ITEMS.SERVICE.PLACEHOLDER
    ]
  })
}

const Mine: React.FC = () => {
  // 登录状态管理
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [isLawyer, setIsLawyer] = useState(false) // 是否为律师用户
  const [menuModules, setMenuModules] = useState<MenuModules[]>([])
  const [userInfo, setUserInfo] = useState<UserAPI.MemberInfo>({} as UserAPI.MemberInfo)
  const [lawyerInfo, setLawyerInfo] = useState<LawyerAPI.GetOwnerLawyerInfoResponse | null>(null)

  // 获取用户信息和律师信息
  const getUserData = async () => {
    try {
      // 先获取会员基础信息
      const memberResponse = await userApi.getMemberInfo({ showError: false })
      if (memberResponse.code === STATUS_CODE.SUCCESS && memberResponse.data) {
        // 接口成功，设置为已登录状态
        setIsLoggedIn(true)
        // 设置基础用户信息
        setUserInfo(memberResponse.data)

        // 根据用户类型判断是否需要获取律师信息
        if (memberResponse.data.type === 2) {
          // 用户类型为律师，获取律师详细信息
          try {
            const lawyerResponse = await lawyerApi.getOwnerLawyerInfo({ showError: false })
            if (lawyerResponse.code === STATUS_CODE.SUCCESS && lawyerResponse.data) {
              setIsLawyer(true)
              setLawyerInfo(lawyerResponse.data)
              setupLawyerMenus(lawyerResponse.data.authStatus)
            } else {
              // 律师信息获取失败，但用户类型是律师，按普通用户处理
              setIsLawyer(false)
              setLawyerInfo(null)
              setupNormalUserMenus()
            }
          } catch (error) {
            console.log('获取律师详细信息失败')
            setIsLawyer(false)
            setLawyerInfo(null)
            setupNormalUserMenus()
          }
        } else {
          // 用户类型为普通用户
          setIsLawyer(false)
          setLawyerInfo(null)
          setupNormalUserMenus()
        }
      } else {
        // 获取会员信息失败，判断为未登录
        console.log('获取会员信息失败，判断为未登录')
        setIsLoggedIn(false)
        setIsLawyer(false)
        setLawyerInfo(null)
        setupGuestMenus()
      }
    } catch (error) {
      console.log('获取用户数据失败，判断为未登录:', error)
      // 请求失败，判断为未登录
      setIsLoggedIn(false)
      setIsLawyer(false)
      setLawyerInfo(null)
      setupGuestMenus()
    }
  }

  // 设置律师用户菜单
  const setupLawyerMenus = (authStatus: number) => {
    const isAuthenticated = authStatus === 2 // 2表示已认证

    setMenuModules([
      createMenuModules.convenient(isAuthenticated),
      createMenuModules.content(),
      createMenuModules.service()
    ])
  }

  // 设置普通用户菜单
  const setupNormalUserMenus = () => {
    setMenuModules([createMenuModules.service()])
  }

  // 设置未登录用户菜单（显示所有功能但需要登录）
  const setupGuestMenus = () => {
    setMenuModules([
      createMenuModules.convenient(false), // 未认证状态
      createMenuModules.content(),
      createMenuModules.service()
    ])
  }

  // 获取律师等级图标
  const getLevelIcon = (lawyerLevel: number): string => {
    const levelIconMap = {
      1: LawyerLevelIcon1,
      2: LawyerLevelIcon2,
      3: LawyerLevelIcon3,
      4: LawyerLevelIcon4,
      5: LawyerLevelIcon5
    }
    return levelIconMap[lawyerLevel] || LawyerLevelIcon1
  }



  // 处理头像点击的原始函数
  const handleAvatarClickOriginal = useCallback(() => {
    if (!isLoggedIn) {
      // 未登录，跳转到登录页面
      Taro.navigateTo({ url: '/subpackages/login/index' })
    }
    // 已登录时可以添加其他逻辑，比如查看个人信息
  }, [isLoggedIn])

  // 使用防抖函数包装头像点击处理函数
  const handleAvatarClick = useMemo(
    () => debounceForMiniProgram(handleAvatarClickOriginal, 300),
    [handleAvatarClickOriginal]
  )


  // 检查登录状态
  const checkLoginStatus = () => {
    // 通过调用 getMemberInfo 接口来检查登录状态
    // 如果接口成功返回数据，说明已登录
    // 如果接口失败，说明未登录
    getUserData()
  }

  // 处理登出
  const handleLogout = async () => {
    try {
      Taro.showModal({
        title: '确认登出',
        content: '确定要退出登录吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              // 调用登出接口
              await commonApi.logout()

              // 使用UserCacheManager清除所有用户相关缓存和存储
              UserCacheManager.clearUserCache()

              // 重置页面状态
              setIsLoggedIn(false)
              setIsLawyer(false)
              setUserInfo({} as UserAPI.MemberInfo)
              setLawyerInfo(null)
              setMenuModules([])

              // 重新设置菜单
              setupGuestMenus()

              Taro.showToast({
                title: '已退出登录',
                icon: 'success'
              })
            } catch (error) {
              console.error('登出失败:', error)
              Taro.showToast({
                title: '登出失败，请重试',
                icon: 'none'
              })
            }
          }
        }
      })
    } catch (error) {
      console.error('登出操作失败:', error)
    }
  }

  // 检查是否需要登录
  const checkLoginRequired = (menuType: string): boolean => {
    // 联系客服不需要登录
    if (menuType === 'contact-service') {
      return false
    }
    // 其他功能都需要登录
    return !isLoggedIn
  }

  // 菜单项点击处理函数
  const handleMenuClick = (menuType: string) => {
    // 检查是否需要登录
    if (checkLoginRequired(menuType)) {
      Taro.showToast({
        title: '请先登录',
        icon: 'none'
      })
      // 可以选择跳转到登录页面
      setTimeout(() => {
        Taro.navigateTo({ url: '/subpackages/login/index' })
      }, 1500)
      return
    }

    switch (menuType) {
      case 'electronic-card':
        // 跳转到编辑电子名片页面
        Taro.navigateTo({ url: '/subpackages/lawyer/edit-business-card/index' })
        break
      case 'lawyer-auth':
        // 跳转到律师认证页面
        Taro.navigateTo({ url: `/subpackages/lawyer/lawyer-entry/index` })
        break
      case 'publish-article':
        // 跳转到发布文章页面
        Taro.navigateTo({ url: `/subpackages/publish/publish-article/index` })
        break
      case 'publish-dynamic':
        // 跳转到发布动态页面
        Taro.navigateTo({ url: `/subpackages/publish/publish-dynamic/index` })
        break
      case 'publish-case':
        // 跳转到发布案例页面
        Taro.navigateTo({ url: `/subpackages/publish/publish-case/index` })
        break
      case 'dynamic-manage':
        // 跳转到动态管理页面
        Taro.navigateTo({ url: `/subpackages/lawyer/lawyer-dynamics-manage/index?lawyerId=${lawyerInfo?.userId}` })
        break
      case 'article-manage':
        // 跳转到文章管理页面
        Taro.navigateTo({ url: `/subpackages/lawyer/lawyer-article-manage/index?lawyerId=${lawyerInfo?.userId}` })
        break
      case 'case-manage':
        // 跳转到案例管理页面
        Taro.navigateTo({ url: `/subpackages/lawyer/lawyer-case-manage/index?lawyerId=${lawyerInfo?.userId}` })
        break
      case 'personal-collection':
        // 跳转到个人收藏页面
        Taro.navigateTo({ url: `/subpackages/my/article-collection/index` })
        break
      case 'my-cases':
        if (isLawyer) {
          Taro.navigateTo({ url: '/subpackages/lawyer/lawyer-case-orders/index' })
        } else {
          // 跳转到我的案件页面
          Taro.navigateTo({ url: `/subpackages/my/my-cases/index` })
        }
        break
      case 'my-follow':
        // 跳转到我的关注页面
        Taro.navigateTo({ url: `/subpackages/my/my-follow/index` })
        break
      case 'contact-service':
        // 联系客服
        const hotlinePhone = CacheManager.getCache<CommonAPI.MiniConfig>(CACHE_KEYS.MINI_CONFIG)?.hotlinePhone || ''
        makePhoneCall(hotlinePhone)
        break
      default:
        if (menuType !== '-') {
          Taro.showToast({
            title: '功能开发中',
            icon: 'none'
          })
        }
    }
  }

  const onChooseAvatar = async (e: any) => {
    if (!isLoggedIn) {
      Taro.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    const tempAvatarUrl = e.detail.avatarUrl // 微信返回的临时地址
    const originalAvatarUrl = userInfo.avatarUrl // 保存原始头像URL

    // 立即更新本地状态，提供即时反馈
    setUserInfo(prev => ({
      ...prev,
      avatarUrl: tempAvatarUrl
    }))

    try {
      // 显示上传进度
      Taro.showLoading({ title: '上传中...' })

      // 第一步：使用统一的上传API上传图片到服务器，获取永久地址
      const uploadResponse = await uploadApi.uploadImage(tempAvatarUrl)

      if (uploadResponse.code !== STATUS_CODE.SUCCESS || !uploadResponse.data?.url) {
        throw new Error(uploadResponse.message || '图片上传失败')
      }

      const permanentAvatarUrl = uploadResponse.data.url

      // 第二步：使用永久地址更新用户头像
      const updateResponse = await userApi.updateAvatarUrl(permanentAvatarUrl)

      if (updateResponse.code === STATUS_CODE.SUCCESS) {
        // 更新本地状态为永久地址
        setUserInfo(prev => ({
          ...prev,
          avatarUrl: permanentAvatarUrl
        }))

        Taro.hideLoading()
        Taro.showToast({
          title: '头像更新成功',
          icon: 'success'
        })

        // 重新获取用户信息，确保数据同步
        getUserData()
      } else {
        throw new Error(updateResponse.message || '头像更新失败')
      }
    } catch (error: any) {
      console.error('更新头像失败:', error)
      Taro.hideLoading()

      // 更新失败时恢复原头像
      setUserInfo(prev => ({
        ...prev,
        avatarUrl: originalAvatarUrl
      }))

      Taro.showToast({
        title: error.message || '头像更新失败',
        icon: 'none'
      })
    }
  }


  // 页面生命周期
  Taro.useDidShow(() => {
    checkLoginStatus()
  })

  const lawyerLocation = useMemo(() => {
    // 普通用户无地址
    if (lawyerInfo && lawyerInfo.province) {
      return `${lawyerInfo.province}${lawyerInfo.city}${lawyerInfo.district}`
    } else {
      return ''
    }
  }, [lawyerInfo])

  // 获取用户显示名称
  const getUserDisplayName = () => {
    if (!isLoggedIn) {
      return '未登录'
    }
    if (isLawyer && lawyerInfo && lawyerInfo.name) {
      return lawyerInfo.name
    }
    return userInfo?.userName || '用户'
  }

    // 转发朋友功能
    useShareAppMessage(() => {
      return {
        title: '胜张仪专业律师服务平台',
        path: '/pages/index/index',
        imageUrl: HomeBg // 使用首页背景图作为转发图片
      }
    })
  
    // 转发朋友圈功能
    useShareTimeline(() => {
      return {
        title: '胜张仪专业律师服务平台',
        imageUrl: HomeBg // 使用首页背景图作为转发图片
      }
    })
    useEffect(() => {
      // 显示右上角转发按钮
      Taro.showShareMenu({
        withShareTicket: true,
        showShareItems: ['shareAppMessage', 'shareTimeline']
      })
    }, [])

  return (
    <View className='mine-page'>
      {/* 固定背景图片 */}
      <View className='mine-bg'></View>

      <PageLayout
        title='用户中心'
        showBack={false}
        backgroundColor='transparent'
        navBarBgColor='transparent'
        showNavBorder={false}
        showFloatingMenu={false}
      >
        <PageContent padded='b'>
          <View className='mine-content'>
            <View className='user-section'>
              {/* 左侧用户信息模块 */}
              <View className='user-info-module'>
                {isLoggedIn ? <Button
                  className='user-avatar'
                  open-type='chooseAvatar'
                  onChooseAvatar={onChooseAvatar}
                >
                  <Image
                    src={userInfo?.avatarUrl || DefaultAvatar}
                    className='avatar-image'
                    mode='aspectFill'
                  />
                </Button> : <Button
                  className='user-avatar'
                  onClick={handleAvatarClick}
                >
                  <Image
                    src={DefaultAvatar}
                    className='avatar-image'
                    mode='aspectFill'
                  />
                </Button>}

                <View className='user-info'>
                  {isLoggedIn ? (
                    // 已登录状态
                    <>
                      <View className='user-name-level'>
                        <View className='user-name'>{getUserDisplayName()}</View>
                        {lawyerInfo && lawyerInfo.lawyerLevel ? <Image
                          className='lawyer-level-icon'
                          src={getLevelIcon(lawyerInfo.lawyerLevel)}
                          mode='aspectFit'
                        /> : null}
                      </View>
                      {lawyerLocation ? <View className='user-location'>{lawyerLocation}</View> : null}
                      {lawyerInfo && lawyerInfo.lawFirm ? <View className='user-firm'>{lawyerInfo.lawFirm}</View> : null}
                    </>
                  ) : (
                    // 未登录状态
                    <View>
                      <View className='user-name-level' onClick={handleAvatarClick}>
                        <View className='user-name'>请登录</View>
                        {/* <Input type='nickname' onBlur={onNickNameBlur} /> */}
                      </View>
                      <View className='user-desc'>登录后享受更多专业服务</View>
                    </View>
                  )}
                </View>
              </View>

              {/* 右侧登出按钮 - 仅登录后显示 */}
              {isLoggedIn && (
                <View className='message-icon' onClick={handleLogout}>
                  <Image
                    src={LogoutIcon}
                    className='chat-icon'
                    mode='aspectFit'
                  />
                </View>
              )}
            </View>

            <View className='menu-section'>
              {menuModules.map((module, moduleIndex) => (
                <View key={moduleIndex} className='menu-module'>
                  <View className='module-title'>{module.title}</View>
                  <View className='module-content'>
                    {module.items.map((item, itemIndex) => (
                      <View
                        key={itemIndex}
                        className='menu-item'
                        onClick={() => handleMenuClick(item.key)}
                      >
                        <Image className='menu-icon' src={item.icon} mode='aspectFit' />
                        <View className='menu-text'>{item.text}</View>
                      </View>
                    ))}
                  </View>
                </View>
              ))}
            </View>
          </View>
        </PageContent>
      </PageLayout>
    </View>
  )
}

export default Mine
