/**
 * 律师头像组件样式
 */

.lawyer-avatar {
  &__container {
    position: relative;
    width: 690rpx;
    height: 960rpx;
    margin: 0 30rpx;
    overflow: hidden;
    flex-shrink: 0;
  }

  &__img {
    width: 100%;
    height: 100%;
    border-radius: 50rpx;
  }

  // 认证图标
  &__verify {
    position: absolute;
    top: 30rpx;
    right: 30rpx;
    width: 64rpx;
    height: 64rpx;
  }

  &__verify-icon {
    width: 100%;
    height: 100%;
  }

  // 律师等级图标
  &__level-badge-icon {
    margin-top: 10rpx;
    width: 296rpx;
    height: 50rpx;
  }

  // 姓名和关注区域
  &__name-follow {
    position: absolute;
    bottom: 180rpx;
    left: 30rpx;
    right: 30rpx;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
  }
  &__name-section{
    display: flex;
    align-items: center;
    gap:10rpx;
  }
  &__name-text {
    line-height: 36rpx;
    font-size: 36rpx;
    font-weight: 800;
    color: #ffffff;
  }

  &__follow-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
  }

  &__follow-icon {
    width: 48rpx;
    height: 48rpx;
    
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.95);
    }
  }

  &__follow-text {
    font-size: 24rpx;
    color: #ffffff;
    text-align: center;
  }

  // 聚焦信息框
  &__focus {
    position: absolute;
    bottom: 30rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 612rpx;
    height: 102rpx;
    display: flex;
  }

  &__focus-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  &__focus-text {
    position: relative;
    z-index: 2;
    color: #ffffff;
    font-size: 28rpx;
    text-align: center;
    padding: 20rpx 30rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
  }
}
