/**
 * API接口统一导出
 */

// 导入所有类型声明（通过全局类型文件）
/// <reference path="./typing.d.ts" />

// 导出各模块API
export { userApi } from './user'
export { lawyerApi } from './lawyer'
export { caseApi } from './case'
export { articleApi } from './article'
export { dynamicsApi } from './dynamics'
export { uploadApi } from './upload'
export { commonApi } from './common'

// 使用示例：
// import { userApi } from '@/utils/request/apis'
// const response = await userApi.login({ username: 'test', password: '123' })
// response.data 的类型是 UserAPI.LoginResponse
