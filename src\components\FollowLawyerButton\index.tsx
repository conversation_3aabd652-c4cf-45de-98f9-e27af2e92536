/**
 * 关注律师按钮组件
 * 
 * 漂浮在页面右下角的关注按钮，支持关注和取消关注律师
 */
import React, { useState } from 'react'
import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { lawyerApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
// 导入图标
import heartActiveIcon from '@/assets/images/common-icon/heart_active.png'
import heartOutlineIcon from '@/assets/images/common-icon/heart_outline.png'
import { FollowLawyerButtonProps } from './types'
import './index.scss'

const FollowLawyerButton: React.FC<FollowLawyerButtonProps> = ({
  lawyerId,
  isFollowed,
  onFollowChange,
  className = '',
  visible = true
}) => {
  const [isLoading, setIsLoading] = useState(false)

  // 处理关注按钮点击
  const handleFollowClick = async () => {
    if (isLoading) return

    try {
      setIsLoading(true)
      
      if (isFollowed) {
        // 取消关注
        const response = await lawyerApi.cancelFollowLawyer(lawyerId)
        if (response.code === STATUS_CODE.SUCCESS) {
          Taro.showToast({
            title: '已取消关注',
            icon: 'success',
            duration: 1500
          })
          onFollowChange?.(false)
        } else {
          throw new Error('取消关注失败')
        }
      } else {
        // 关注律师
        const response = await lawyerApi.followLawyer(lawyerId)
        if (response.code === STATUS_CODE.SUCCESS) {
          Taro.showToast({
            title: '关注成功',
            icon: 'success',
            duration: 1500
          })
          onFollowChange?.(true)
        } else {
          throw new Error('关注失败')
        }
      }
    } catch (error) {
      console.error('关注操作失败:', error)
      Taro.showToast({
        title: isFollowed ? '取消关注失败' : '关注失败',
        icon: 'none',
        duration: 2000
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 如果不可见，则不渲染
  if (!visible) {
    return null
  }

  return (
    <View
      className={`follow-lawyer-button ${visible ? 'follow-lawyer-button--visible' : 'follow-lawyer-button--hidden'} ${className}`}
      onClick={handleFollowClick}
    >
      <Image
        className='follow-lawyer-button__icon'
        src={isFollowed ? heartActiveIcon : heartOutlineIcon}
        mode='aspectFit'
      />
      <Text className='follow-lawyer-button__text'>
        {isFollowed ? '已关注' : '关注'}
      </Text>
    </View>
  )
}

// 导出组件
export default FollowLawyerButton

// 导出类型
export type { FollowLawyerButtonProps } from './types'
