/**
 * 律师动态Tab内容组件
 *
 * 包含动态搜索、分类筛选和动态列表三个部分
 */
import React, { useState, useEffect } from 'react'
import Taro from '@tarojs/taro'
import { View } from '@tarojs/components'
import { dynamicsApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
import CategoryCacheManager from '@/utils/cache/categoryCache'
import { DynamicsList, DynamicsHeader, DynamicsCategories } from './components'
import './index.scss'
// 组件属性
export interface LawyerDynamicsTabProps {
  // 律师ID
  lawyerId: number
  // 是否显示加载状态
  loading?: boolean
  // 自定义样式类名
  className?: string
}

const PAGE = 1
const PAGE_SIZE = 5

const LawyerDynamicsTab: React.FC<LawyerDynamicsTabProps> = ({
  lawyerId,
  className = ''
}) => {
  
  // 搜索关键词
  const [searchKeyword, setSearchKeyword] = useState('')
  // 动态分类列表
  const [categories, setCategories] = useState<DynamicsAPI.DynamicsCategoryInfo[]>([])
  // 选中的分类ID
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null)
  // list
  const [dynamics, setDynamics] = useState<DynamicsAPI.DynamicsListInfo[]>([])
  // loading
  const [isLoading, setIsLoading] = useState(false)
  // filterParams
  const [filterParams, setFilterParams] = useState<DynamicsAPI.GetDynamicsListRequest>({
    page: PAGE,
    pageSize: PAGE_SIZE,
    lawyerId
  })

  // 加载动态分类（使用缓存）
  const loadCategories = async () => {
    try {
      const categorieList = await CategoryCacheManager.getDynamicsCategoryList()
      const allCategory = { id: 0, name: '全部' }
      setCategories([allCategory, ...categorieList])
      setSelectedCategoryId(0) // 默认选中全部
    } catch (error) {
      console.error('加载分类失败:', error)
    }
  }

  // 加载律师动态列表
  const loadDynamicsList = async (val?: DynamicsAPI.GetDynamicsListRequest) => {
    try {
      setIsLoading(true)
      const response = await dynamicsApi.getDynamicsList({ ...val })
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setDynamics(response.data.list || [])
      }
    } catch (error) {
      console.error('加载动态列表失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理搜索输入（只更新输入框状态，不触发搜索）
  const handleSearchInput = (e: any) => {
    setSearchKeyword(e.detail.value)
  }

  // 执行搜索（点击搜索图标或软键盘搜索时触发）
  const handleSearch = () => {
    setFilterParams(prev => ({
      ...prev,
      title: searchKeyword
    }))
  }

  // 处理软键盘搜索确认
  const handleSearchConfirm = (e: any) => {
    const value = e.detail.value
    setSearchKeyword(value)
    setFilterParams(prev => ({
      ...prev,
      title: value
    }))
  }

  // 处理分类选择
  const handleCategorySelect = (categoryId: number) => {
    setSelectedCategoryId(categoryId)
    setFilterParams(prev => ({
      ...prev,
      categoryId,
      title: searchKeyword
    }))
  }

  const handleDynamicsClick = (dynamicsInfo: DynamicsAPI.DynamicsListInfo) => {
    Taro.navigateTo({
      url: `/subpackages/detail/dynamics/index?id=${dynamicsInfo.id}`
    })
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadCategories()
  }, [])

  useEffect(() => {
    loadDynamicsList(filterParams)
  }, [filterParams])



  return (
    <View className={`lawyer-dynamics-tab ${className}`}>
      {/* 标题和搜索框 */}
      <DynamicsHeader
        searchKeyword={searchKeyword}
        onSearchInput={handleSearchInput}
        onSearchConfirm={handleSearchConfirm}
        onSearchIconClick={handleSearch}
      />

      {/* 动态类型选择 */}
      <DynamicsCategories
        categories={categories}
        selectedCategoryId={selectedCategoryId}
        onCategorySelect={handleCategorySelect}
      />

      {/* 动态列表 */}
      <DynamicsList
        dynamics={dynamics}
        isLoading={isLoading}
        onDynamicsClick={handleDynamicsClick}
      />
    </View>
  )
}

export default LawyerDynamicsTab
