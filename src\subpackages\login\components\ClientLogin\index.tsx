/**
 * 委托人登录组件
 * 使用微信开放能力获取手机号和用户信息
 */
import React, { useState } from 'react'
import { View, Button, Text, CheckboxGroup, Checkbox } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { commonApi } from '@/apis/common'
import { STATUS_CODE } from '@/utils/request/config'
import UserCacheManager from '@/utils/cache/userCache'
import './index.scss'

const ClientLogin: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [agreeTerms, setAgreeTerms] = useState(false)

  // 检查微信登录状态
  const checkWechatSession = async (): Promise<boolean> => {
    try {
      await Taro.checkSession()
      console.log('微信登录状态有效')
      return true
    } catch (error) {
      console.log('微信登录状态已过期，需要重新登录')
      return false
    }
  }

  // 获取微信登录code
  const getWechatLoginCode = async (): Promise<string> => {
    try {
      const loginResult = await Taro.login()
      console.log('微信登录code:', loginResult)
      return loginResult.code
    } catch (error) {
      console.error('获取微信登录code失败:', error)
      throw new Error('获取微信登录code失败')
    }
  }

  // 协议确认处理
  const handleAgreeTermsChange = (e: any) => {
    setAgreeTerms(e.detail.value.length > 0)
  }

  // 获取微信手机号
  const handleGetPhoneNumber = async (e: any) => {
    console.log('微信getPhoneNumber返回数据:', e)

    // 检查是否同意协议
    if (!agreeTerms) {
      Taro.showToast({
        title: '请先同意用户协议和隐私条款',
        icon: 'none'
      })
      return
    }

    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      setLoading(true)
      try {
        // 检查微信登录状态
        const isSessionValid = await checkWechatSession()
        console.log('微信登录状态:', isSessionValid ? '有效' : '已过期')

        // 获取微信登录code
        const loginCode = await getWechatLoginCode()

        console.log('手机号授权code:', e.detail.code)
        console.log('微信登录code:', loginCode)

        // 获取用户信息
        const userInfo = await getUserInfo()

        console.log('用户信息:', userInfo)

        // 调用后端接口进行登录
        const loginResult = await commonApi.wechatLogin({
          phoneCode: e.detail.code,
          loginCode: loginCode
        })

        console.log('登录结果:', loginResult)

        if (loginResult.code === STATUS_CODE.SUCCESS && loginResult.data) {
          // 存储token和用户信息
          Taro.setStorageSync('token', loginResult.data.token)
          const cacheUserInfo = {
            id: loginResult.data.id,
            userName: loginResult.data.userName,
            type: loginResult.data.type,
            expires: loginResult.data.expires
          }
          Taro.setStorageSync('userInfo', cacheUserInfo)

          // 同时更新缓存
          UserCacheManager.setUserInfo(cacheUserInfo)

          Taro.showToast({
            title: '登录成功',
            icon: 'success'
          })

          // 返回上一页或跳转到首页
          setTimeout(() => {
            Taro.switchTab({ url: '/pages/mine/index' })
          }, 1500)
        } else {
          throw new Error(loginResult.message || '登录失败')
        }

      } catch (error: any) {
        console.error('登录失败:', error)
        Taro.showToast({
          title: error.message || '登录失败，请重试',
          icon: 'none'
        })
      } finally {
        setLoading(false)
      }
    } else {
      Taro.showToast({
        title: '获取手机号失败',
        icon: 'none'
      })
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const userInfo = await Taro.getUserInfo()
      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }

  return (
    <View className='client-login'>
      <View className='login-header'>
        <Text className='title'>手机号快速登录</Text>
        <Text className='subtitle'>新用户注册即可享受专业法律服务</Text>
        <Text className='subtitle'>已注册律师用户可快速登录</Text>
      </View>

      <View className='login-form'>
        <View className='wechat-login-section'>
          {/* 用户协议确认 */}
          <View className='agreement-section'>
            <CheckboxGroup onChange={handleAgreeTermsChange}>
              <View className='agreement-item'>
                <Checkbox value='agree' checked={agreeTerms} className='agreement-checkbox' />
                <View className='agreement-text'>
                  <Text className='agreement-prefix'>我已阅读并同意</Text>
                  <Text
                    className='agreement-link'
                    onClick={() => Taro.navigateTo({ url: '/subpackages/agreement/user-agreement/index' })}
                  >
                    《用户协议》
                  </Text>
                  <Text className='agreement-separator'>和</Text>
                  <Text
                    className='agreement-link'
                    onClick={() => Taro.navigateTo({ url: '/subpackages/agreement/privacy-policy/index' })}
                  >
                    《隐私条款》
                  </Text>
                </View>
              </View>
            </CheckboxGroup>
          </View>
          <Button
            className={`wechat-phone-btn ${!agreeTerms ? 'wechat-phone-btn--disabled' : ''}`}
            openType='getPhoneNumber'
            onGetPhoneNumber={handleGetPhoneNumber}
            loading={loading}
            disabled={loading || !agreeTerms}
          >
            {loading ? '登录中...' : '手机号快速登录'}
          </Button>

        </View>
      </View>
    </View>
  )
}

export default ClientLogin

