/**
 * 用户数据缓存管理器
 * 专门管理用户相关的缓存数据，如 userId、userInfo 等
 */
import Taro from '@tarojs/taro'
import CacheManager, { CACHE_KEYS } from './index'

// 扩展缓存键名
export const USER_CACHE_KEYS = {
  ...CACHE_KEYS,
  USER_ID: 'user_id',
  USER_INFO: 'user_info',
  USER_TOKEN: 'user_token'
} as const

// 用户基本信息类型
export interface UserInfo {
  id: number
  userName: string
  type: number
  expires: number
  [key: string]: any
}

class UserCacheManager {
  /**
   * 设置用户ID缓存
   * @param userId 用户ID
   */
  static setUserId(userId: number | string): void {
    CacheManager.setCache(USER_CACHE_KEYS.USER_ID, Number(userId))
  }

  /**
   * 获取用户ID缓存
   * @returns 用户ID或null
   */
  static getUserId(): number | null {
    const userId = CacheManager.getCache<number>(USER_CACHE_KEYS.USER_ID)
    return userId
  }

  /**
   * 设置用户信息缓存（包含userId）
   * @param userInfo 用户信息
   */
  static setUserInfo(userInfo: UserInfo): void {
    CacheManager.setCache(USER_CACHE_KEYS.USER_INFO, userInfo)
    // 同时缓存userId
    this.setUserId(userInfo.id)
  }

  /**
   * 获取用户信息缓存
   * @returns 用户信息或null
   */
  static getUserInfo(): UserInfo | null {
    return CacheManager.getCache<UserInfo>(USER_CACHE_KEYS.USER_INFO)
  }

  /**
   * 检查用户是否已登录（基于缓存）
   * @returns 是否已登录
   */
  static isLoggedIn(): boolean {
    const token = Taro.getStorageSync('token')
    const userId = this.getUserId()
    return !!(token && userId)
  }

  /**
   * 清除所有用户相关缓存
   * 在登出或token失效时调用
   */
  static clearUserCache(): void {
    // 清除用户相关缓存
    CacheManager.removeCache(USER_CACHE_KEYS.USER_ID)
    CacheManager.removeCache(USER_CACHE_KEYS.USER_INFO)
    
    // 清除存储中的用户数据
    Taro.removeStorageSync('token')
    Taro.removeStorageSync('userInfo')
    
    console.log('用户缓存已清除')
  }

  /**
   * 从存储中同步用户数据到缓存
   * 在应用启动时调用，确保缓存与存储同步
   */
  static syncUserDataFromStorage(): void {
    try {
      const userInfo = Taro.getStorageSync('userInfo')
      if (userInfo && userInfo.id) {
        this.setUserInfo(userInfo)
        console.log('用户数据已同步到缓存')
      }
    } catch (error) {
      console.error('同步用户数据失败:', error)
    }
  }

  /**
   * 获取用户缓存状态（调试用）
   */
  static getCacheStatus(): {
    hasUserId: boolean
    hasUserInfo: boolean
    hasToken: boolean
    userId: number | null
  } {
    return {
      hasUserId: CacheManager.hasCache(USER_CACHE_KEYS.USER_ID),
      hasUserInfo: CacheManager.hasCache(USER_CACHE_KEYS.USER_INFO),
      hasToken: !!Taro.getStorageSync('token'),
      userId: this.getUserId()
    }
  }
}

export default UserCacheManager