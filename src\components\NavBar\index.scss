/**
 * 导航栏组件样式
 */

.nav-bar {
  width: 100%;
  top: 0;
  left: 0;
  z-index: 999;
  background-color: #ffffff;

  // 固定定位
  &--fixed {
    position: fixed;
  }

  // 底部边框
  &--border {
    border-bottom: 1rpx solid #e5e5e5;
  }

  // 内容区域
  &__content {
    position: relative; // 为绝对定位的标题提供定位上下文
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    box-sizing: border-box;
    padding-left: 32rpx;
    padding-right: 32rpx;
  }

  // 左侧区域
  &__left {
    display: flex;
    align-items: center;
    min-width: 120rpx;
    justify-content: flex-start;
  }

  // 中间区域
  &__center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    margin: 0 32rpx;
    // 当使用 centerSlot 或 logo 时，中间区域正常显示
    // 当使用 title 时，title 会使用绝对定位，不占用此区域空间
  }

  // 右侧区域
  &__right {
    display: flex;
    align-items: center;
    min-width: 120rpx;
    justify-content: flex-end;
  }

  // 返回按钮
  &__back {
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
    transition: background-color 0.2s;

    &:active {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }

  // 返回按钮图标
  &__back-icon {
    width: 44rpx;
    height: 44rpx;
    padding-right: 20rpx;
  }

  // 标题
  &__title {
    position: absolute;
    left: 50%;
    transform: translate(-50%);
    max-width: 400rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 800;
    font-size: 36rpx;
    color: #BD8A4F;
    text-align: center;
    font-style: normal;
    pointer-events: none; // 防止标题阻挡左右区域的点击事件
  }

  // Logo
  &__logo {
    width: 137rpx;
    height: 43rpx;
    display: block;
  }
}

// 占位符（当导航栏固定定位时，为页面内容预留空间）
.nav-bar-placeholder {
  width: 100%;
  height: var(--nav-bar-height, 88rpx);
}