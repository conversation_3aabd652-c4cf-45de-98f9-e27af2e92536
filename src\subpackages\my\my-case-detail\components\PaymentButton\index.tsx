/**
 * 支付按钮组件
 */
import React from 'react'
import { View, Text, Button } from '@tarojs/components'
import './index.scss'

interface PaymentButtonProps {
  caseDetail: UserAPI.MemberCaseOrderDetailInfo
  onPayment: () => void
}

const PaymentButton: React.FC<PaymentButtonProps> = ({ caseDetail, onPayment }) => {
  // 判断是否需要显示支付按钮
  const shouldShowPaymentButton = (): boolean => {
    // 支付状态为未支付(1)时显示支付按钮
    return caseDetail.paymentStatus === 1 && caseDetail.paymentAmount > 0
  }

  // 获取支付按钮文本
  const getPaymentButtonText = (): string => {
    return `支付保证金 ¥${caseDetail.paymentAmount.toLocaleString()}`
  }

  // 获取支付状态提示文本
  const getPaymentStatusText = (): string => {
    switch (caseDetail.paymentStatus) {
      case 1: return '待支付保证金'
      case 2: return '保证金已支付'
      case 3: return '支付失败，请重新支付'
      default: return ''
    }
  }

  // 获取支付状态样式
  const getPaymentStatusClass = (): string => {
    switch (caseDetail.paymentStatus) {
      case 1: return 'payment-pending'
      case 2: return 'payment-completed'
      case 3: return 'payment-failed'
      default: return ''
    }
  }

  if (!shouldShowPaymentButton() && caseDetail.paymentStatus === 2) {
    // 已支付状态，显示提示信息
    return (
      <View className='payment-button'>
        <View className='payment-button__status-info'>
          <Text className={`payment-button__status-text ${getPaymentStatusClass()}`}>
            {getPaymentStatusText()}
          </Text>
          <Text className='payment-button__amount-text'>
            保证金：¥{caseDetail.paymentAmount.toLocaleString()}
          </Text>
        </View>
      </View>
    )
  }

  if (!shouldShowPaymentButton()) {
    return null
  }

  return (
    <View className='payment-button'>
      <View className='payment-button__info'>
        <Text className={`payment-button__status-text ${getPaymentStatusClass()}`}>
          {getPaymentStatusText()}
        </Text>
        <Text className='payment-button__tip'>
          支付保证金后，律师将开始为您处理案件
        </Text>
      </View>
      
      <Button
        className='payment-button__btn'
        onClick={onPayment}
      >
        {getPaymentButtonText()}
      </Button>
    </View>
  )
}

export default PaymentButton
