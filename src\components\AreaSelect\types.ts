/**
 * 省市区选择组件类型定义
 */
import { ReactNode } from 'react'

// 行政区数据类型
export interface AreaItem {
  id: string
  name: string
}

// 选择结果类型
export interface AreaSelectResult {
  // 省市区字符串，用'/'分隔
  areaString: string
  // 详细信息
  province?: string
  city?: string
  district?: string
}

// 组件Props类型
export interface AreaSelectProps {
  // 触发选择的展示内容
  children: ReactNode
  // 选择完成回调
  onChange?: (result: AreaSelectResult) => void
  // 初始值（省市区ID，用'/'分隔）
  value?: string
  // 是否禁用
  disabled?: boolean
  // 自定义样式类名
  className?: string
  // 占位符文本
  placeholder?: string
}

// 直辖市列表（只有两级行政区）
export const MUNICIPALITIES = ['11', '12', '31', '50'] // 北京、天津、上海、重庆的省级代码
