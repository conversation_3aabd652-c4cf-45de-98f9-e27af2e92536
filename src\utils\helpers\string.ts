/**
 * 字符串相关工具函数
 */

/**
 * 首字母大写
 * @param str 字符串
 */
export const capitalize = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

/**
 * 驼峰转下划线
 * @param str 字符串
 */
export const camelToSnake = (str: string): string => {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
}

/**
 * 下划线转驼峰
 * @param str 字符串
 */
export const snakeToCamel = (str: string): string => {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 截断字符串
 * @param str 字符串
 * @param length 长度
 * @param suffix 后缀
 */
export const truncate = (str: string, length: number, suffix: string = '...'): string => {
  if (str.length <= length) return str
  return str.slice(0, length) + suffix
}

/**
 * 移除HTML标签
 * @param str 字符串
 */
export const stripHtml = (str: string): string => {
  return str.replace(/<[^>]*>/g, '')
}
