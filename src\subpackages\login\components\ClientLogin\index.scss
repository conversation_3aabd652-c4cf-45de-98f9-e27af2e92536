.client-login {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 32rpx;
  min-height: 500rpx;

  .login-header {
    text-align: center;
    margin-bottom: 60rpx;

    .title {
      display: block;
      font-size: 40rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 16rpx;
    }

    .subtitle {
      display: block;
      font-size: 26rpx;
      color: #666666;
      line-height: 36rpx;
    }
  }

  .login-form {
    .wechat-login-section {
      margin-bottom: 60rpx;

      // 协议确认区域
      .agreement-section {
        margin-bottom: 40rpx;

        .agreement-item {
          display: flex;
          align-items: center;
          gap: 16rpx;

          .agreement-checkbox {
            margin-top: 4rpx;
            transform: scale(0.9);
          }

          .agreement-text {
            display: flex;
            align-items: center;
            flex: 1;
            line-height: 1.6;
            font-size: 26rpx;
            color: #666666;
            padding-top: 8rpx;

            .agreement-prefix,
            .agreement-separator {
              color: #666666;
            }

            .agreement-link {
              color: #BD8A4F;
              text-decoration: underline;
              margin: 0 4rpx;

              &:active {
                color: #a67a42;
              }
            }
          }
        }
      }

      .wechat-phone-btn {
        width: 100%;
        height: 88rpx;
        background: #07C160;
        color: #ffffff;
        font-size: 32rpx;
        font-weight: 500;
        border-radius: 44rpx;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;

        &:active {
          background: #06ad56;
        }

        &:disabled {
          background: #cccccc !important;
          color: #999999 !important;
        }

        &--disabled {
          background: #cccccc !important;
          color: #999999 !important;
        }

        &::after {
          border: none;
        }
      }
    }
  }
}