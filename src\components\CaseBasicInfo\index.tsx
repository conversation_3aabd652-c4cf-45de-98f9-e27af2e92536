/**
 * 通用案件基本信息组件
 * 
 * 支持委托人和律师两种视角的案件基本信息展示
 */
import React from 'react'
import { View, Text } from '@tarojs/components'
import {
  getOrderStatusText,
  getOrderStatusClass,
  getPaymentStatusText,
  getPaymentStatusClass,
  getOrderSourceText,
  formatAmount
} from '@/constant'
import { formatTime } from '@/utils'
import './index.scss'

// 通用案件详情信息接口
export interface CaseDetailInfo {
  id: number
  orderNo: string
  caseTypeName: string
  caseStage: string
  handlingAgency: string
  province: string
  city: string
  district: string
  orderStatus: number
  paymentStatus: number
  amountInvolvedOfCase: number
  paymentAmount: number
  source: string
  createdAt: string
  reviewTime?: string
  // 委托人视角字段
  lawyerName?: string
  // 律师视角字段
  clientName?: string
  clientMobile?: string
  lawyerRequirements?: string
  [key: string]: any
}

// 组件属性
export interface CaseBasicInfoProps {
  // 案件详情信息
  caseDetail: CaseDetailInfo
  // 视角类型：member(委托人) 或 lawyer(律师)
  viewType?: 'member' | 'lawyer'
  // 自定义样式类名
  className?: string
}

const CaseBasicInfo: React.FC<CaseBasicInfoProps> = ({
  caseDetail,
  viewType = 'member',
  className = ''
}) => {
  const isLawyerView = viewType === 'lawyer'
  const baseClassName = 'case-basic-info'

  return (
    <View className={`${baseClassName} ${className}`}>
      <View className={`${baseClassName}__header`}>
        <Text className={`${baseClassName}__title`}>案件信息</Text>
        <View className={`${baseClassName}__status-group`}>
          <Text className={`${baseClassName}__order-status ${getOrderStatusClass(caseDetail.orderStatus)}`}>
            {getOrderStatusText(caseDetail.orderStatus)}
          </Text>
          <Text className={`${baseClassName}__payment-status ${getPaymentStatusClass(caseDetail.paymentStatus)}`}>
            {getPaymentStatusText(caseDetail.paymentStatus)}
          </Text>
        </View>
      </View>

      <View className={`${baseClassName}__content`}>
        <View className={`${baseClassName}__row`}>
          <Text className={`${baseClassName}__label`}>订单号：</Text>
          <Text className={`${baseClassName}__value`}>{caseDetail.orderNo}</Text>
        </View>

        <View className={`${baseClassName}__row`}>
          <Text className={`${baseClassName}__label`}>案件类型：</Text>
          <Text className={`${baseClassName}__value`}>{caseDetail.caseTypeName}</Text>
        </View>

        <View className={`${baseClassName}__row`}>
          <Text className={`${baseClassName}__label`}>案件阶段：</Text>
          <Text className={`${baseClassName}__value`}>{caseDetail.caseStage}</Text>
        </View>

        <View className={`${baseClassName}__row`}>
          <Text className={`${baseClassName}__label`}>办案机关：</Text>
          <Text className={`${baseClassName}__value`}>{caseDetail.handlingAgency}</Text>
        </View>

        <View className={`${baseClassName}__row`}>
          <Text className={`${baseClassName}__label`}>案件地区：</Text>
          <Text className={`${baseClassName}__value`}>
            {`${caseDetail.province}/${caseDetail.city}/${caseDetail.district}`}
          </Text>
        </View>

        {/* 律师视角：显示委托人信息 */}
        {isLawyerView && (
          <>
            <View className={`${baseClassName}__row`}>
              <Text className={`${baseClassName}__label`}>委托人：</Text>
              <Text className={`${baseClassName}__value`}>{caseDetail.clientName}</Text>
            </View>

            <View className={`${baseClassName}__row`}>
              <Text className={`${baseClassName}__label`}>联系电话：</Text>
              <Text className={`${baseClassName}__value`}>{caseDetail.clientMobile}</Text>
            </View>
          </>
        )}

        <View className={`${baseClassName}__row`}>
          <Text className={`${baseClassName}__label`}>涉案金额：</Text>
          <Text className={`${baseClassName}__value`}>{formatAmount(caseDetail.amountInvolvedOfCase)}</Text>
        </View>

        <View className={`${baseClassName}__row`}>
          <Text className={`${baseClassName}__label`}>保证金：</Text>
          <Text className={`${baseClassName}__value`}>{formatAmount(caseDetail.paymentAmount)}</Text>
        </View>

        <View className={`${baseClassName}__row`}>
          <Text className={`${baseClassName}__label`}>订单来源：</Text>
          <Text className={`${baseClassName}__value`}>{getOrderSourceText(caseDetail.source)}</Text>
        </View>

        {/* 委托人视角：显示指派律师 */}
        {!isLawyerView && caseDetail.lawyerName && (
          <View className={`${baseClassName}__row`}>
            <Text className={`${baseClassName}__label`}>指派律师：</Text>
            <Text className={`${baseClassName}__value`}>{caseDetail.lawyerName}</Text>
          </View>
        )}

        <View className={`${baseClassName}__row`}>
          <Text className={`${baseClassName}__label`}>创建时间：</Text>
          <Text className={`${baseClassName}__value`}>{formatTime(caseDetail.createdAt)}</Text>
        </View>

        {caseDetail.reviewTime && (
          <View className={`${baseClassName}__row`}>
            <Text className={`${baseClassName}__label`}>审核时间：</Text>
            <Text className={`${baseClassName}__value`}>{formatTime(caseDetail.reviewTime)}</Text>
          </View>
        )}

        {caseDetail.lawyerRequirements && (
          <View className={`${baseClassName}__requirements`}>
            <Text className={`${baseClassName}__label`}>律师要求：</Text>
            <Text className={`${baseClassName}__requirements-text`}>{caseDetail.lawyerRequirements}</Text>
          </View>
        )}
      </View>
    </View>
  )
}

export default CaseBasicInfo
