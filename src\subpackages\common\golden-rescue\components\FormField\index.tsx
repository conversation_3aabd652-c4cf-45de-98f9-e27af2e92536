/**
 * 表单字段组件
 */
import React from 'react'
import { View, Text } from '@tarojs/components'
import type { FormFieldProps } from '../../types'
import './index.scss'

const FormField: React.FC<FormFieldProps> = ({
  label,
  required = false,
  children,
  className = ''
}) => {
  return (
    <View className={`form-field ${className}`}>
      <View className='field-label'>
        {required && <Text className='required-mark'>*</Text>}
        <Text className='label-text'>{label}</Text>
      </View>
      <View className='field-content'>
        {children}
      </View>
    </View>
  )
}

export default FormField
