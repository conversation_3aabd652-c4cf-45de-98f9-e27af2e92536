/**
 * 案例发布/编辑页面样式
 */

.publish-case {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  background: #f8f9fa;

  // 加载状态
  .loading-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    .loading-text {
      font-size: 28rpx;
      color: #999999;
    }
  }

  // 内容区域
  .content-area {
    flex: 1;
    padding: 32rpx;
    padding-bottom: calc(96rpx + 48rpx + env(safe-area-inset-bottom));
  }

  // 表单区域
  .form-section {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

    .form-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 24rpx;

      .word-count {
        font-size: 24rpx;
        font-weight: 400;
        color: #999999;
      }
    }

    // 分类选择器
    .category-picker {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 32rpx;
      background: #f8f9fa;
      border-radius: 16rpx;
      border: 2rpx solid #e9ecef;
      transition: all 0.2s ease;

      &:active {
        background: #e9ecef;
        transform: scale(0.98);
      }

      .picker-text {
        font-size: 30rpx;
        color: #333333;
        flex: 1;
      }

      .picker-arrow {
        font-size: 32rpx;
        color: #999999;
        font-weight: 300;
      }
    }

    // 标题输入框
    .title-textarea {
      width: 100%;
      height: 120rpx;
      padding: 24rpx;
      background: #f8f9fa;
      border-radius: 16rpx;
      border: 2rpx solid #e9ecef;
      font-size: 30rpx;
      color: #333333;
      box-sizing: border-box;
      transition: all 0.2s ease;
      line-height: 1.5;
      resize: none;

      &:focus {
        border-color: #BD8A4F;
        background: #ffffff;
        box-shadow: 0 0 0 4rpx rgba(189, 138, 79, 0.1);
      }
    }

    // 内容输入框
    .content-textarea {
      width: 100%;
      min-height: 400rpx;
      padding: 24rpx;
      background: #f8f9fa;
      border-radius: 16rpx;
      border: 2rpx solid #e9ecef;
      font-size: 30rpx;
      line-height: 1.6;
      color: #333333;
      box-sizing: border-box;
      transition: all 0.2s ease;
      resize: none;

      &:focus {
        border-color: #BD8A4F;
        background: #ffffff;
        box-shadow: 0 0 0 4rpx rgba(189, 138, 79, 0.1);
      }
    }
  }

  // 提交区域 - 固定在底部
  .submit-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    padding: 24rpx 32rpx;
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
    border-top: 1rpx solid #e9ecef;
    z-index: 100;

    .submit-btn {
      width: 100% !important;
      height: 96rpx !important;
      background: linear-gradient(135deg, #BD8A4F 0%, #d4a574 100%) !important;
      border-radius: 48rpx !important;
      border: none !important;
      font-size: 32rpx !important;
      font-weight: 600 !important;
      color: #ffffff !important;
      line-height: 96rpx !important;
      transition: all 0.3s ease;
      box-shadow: 0 6rpx 24rpx rgba(189, 138, 79, 0.3);

      // 重置 Button 组件默认样式
      &::after {
        border: none !important;
      }

      &:not([disabled]):active {
        transform: scale(0.98);
        box-shadow: 0 3rpx 12rpx rgba(189, 138, 79, 0.2);
      }

      &[disabled] {
        background: #e9ecef !important;
        color: #999999 !important;
        box-shadow: none !important;
        opacity: 1 !important;

        &:active {
          transform: none !important;
        }

        &:not(.button-loading) {
          background: #e9ecef !important;
          color: #999999 !important;
        }
      }

      // 加载状态样式
      &.button-loading {
        background: #BD8A4F !important;
        opacity: 0.8 !important;
      }

      // Hover 效果
      &.button-hover {
        background: linear-gradient(135deg, #a67a42 0%, #c19968 100%) !important;
        transform: scale(0.98);
      }
    }
  }
}
