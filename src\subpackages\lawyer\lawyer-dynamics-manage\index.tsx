import React, { useState, useEffect } from 'react'
import { View, Input, Image, Text, ScrollView } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import { useUpdateEffect } from 'ahooks'
import PageLayout, { PageContent } from '@/components/PageLayout'
import { dynamicsApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
import { formatTime } from '@/utils'
import CategoryCacheManager from '@/utils/cache/categoryCache'
import SearchIcon from '@/assets/images/common-icon/search.png'
import AddIcon from '@/assets/images/common-icon/plus.png'
import ClickMoreIcon from '@/assets/images/common-icon/click_more.png'
import '@/styles/lawyer-manage-list.scss'

// 分页参数
const PAGE_SIZE = 20

const LawyerDynamicsManage: React.FC = () => {
  const router = useRouter()
  const { lawyerId } = router.params
  // 状态管理
  const [searchKeyword, setSearchKeyword] = useState('')
  const [dynamicsList, setDynamicsList] = useState<DynamicsAPI.MyDynamicsInfo[]>([])
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [filterParams, setFilterParams] = useState<DynamicsAPI.GetMyDynamicsListRequest>({
    page: 1,
    pageSize: PAGE_SIZE
  })
  // 分类相关
  const [categories, setCategories] = useState<{ id: number; name: string }[]>([])
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>()
  const [categoriesCollapsed, setCategoriesCollapsed] = useState(true)

  // 加载动态分类
  const loadCategories = async () => {
    try {
      const categoryList = await CategoryCacheManager.getDynamicsCategoryList()
      setCategories(categoryList)
    } catch (error) {
      console.error('加载动态分类失败:', error)
      setCategories([])
    } finally {
    }
  }

  // 加载动态列表
  const loadDynamicsList = async (val?: DynamicsAPI.GetMyDynamicsListRequest) => {
    if (loading) return
    try {
      setLoading(true)
      const response = await dynamicsApi.getMyDynamicsList({ ...val })
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        const newList = response.data.list || []
        if (response.data.page === 1) {
          setDynamicsList(newList)
        } else {
          setDynamicsList(prev => [...prev, ...newList])
        }
        // 判断是否还有更多数据
        setHasMore(response.data.page < response.data.pageCount)
      } else {
        console.log('接口无数据返回')
      }
    } catch (error) {
      console.error('加载动态列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 搜索处理
  const handleSearch = () => {
    const newFilters = {
      ...filterParams,
      page: 1,
      title: searchKeyword
    }
    setFilterParams(newFilters)
    setHasMore(true)
  }

  // 搜索输入处理
  const handleSearchInput = (e: any) => {
    setSearchKeyword(e.detail.value)
  }

  // 搜索确认处理
  const handleSearchConfirm = (e: any) => {
    const value = e.detail.value
    setSearchKeyword(value)
    const newFilters = {
      ...filterParams,
      page: 1,
      title: value
    }
    setFilterParams(newFilters)
    setHasMore(true)
  }

  // 分类选择处理
  const handleCategorySelect = (categoryId?: number) => {
    setSelectedCategoryId(categoryId)
    const newFilters = {
      ...filterParams,
      page: 1,
      categoryId: categoryId
    }
    setFilterParams(newFilters)
    setHasMore(true)
  }

  // 切换分类折叠状态
  const toggleCategoriesCollapse = () => {
    setCategoriesCollapsed(prev => !prev)
  }

  // 加载更多
  const handleLoadMore = () => {
    if (!loading && hasMore) {
      const nextPage = (filterParams.page || 1) + 1
      const newFilters = {
        ...filterParams,
        page: nextPage
      }
      setFilterParams(newFilters)
    }
  }

  // 跳转到新建动态页面
  const handleAddDynamics = () => {
    Taro.navigateTo({
      url: '/subpackages/publish/publish-dynamic/index'
    })
  }

  // 跳转到编辑动态页面
  const handleDynamicsClick = (dynamics: DynamicsAPI.MyDynamicsInfo) => {
    Taro.navigateTo({
      url: `/subpackages/publish/publish-dynamic/index?id=${dynamics.id}`
    })
  }

  // 下拉刷新
  const handleRefresh = async () => {
    const newFilters = {
      ...filterParams,
      page: 1
    }
    setFilterParams(newFilters)
    setHasMore(true)
  }



  // 获取审核状态信息
  const getStatusInfo = (status: number) => {
    switch (status) {
      case 1:
        return { text: '待审核', className: 'status-pending' }
      case 2:
        return { text: '审核通过', className: 'status-approved' }
      case 3:
        return { text: '审核不通过', className: 'status-rejected' }
      default:
        return { text: '未知状态', className: 'status-unknown' }
    }
  }



  // 页面初始化
  useEffect(() => {
    loadCategories()
  }, [])

  // 监听过滤参数变化
  useUpdateEffect(() => {
    loadDynamicsList(filterParams)
  }, [filterParams])

  Taro.useDidShow(() => {
    if (lawyerId) {
      loadDynamicsList(filterParams)
    }
  })

  return (
    <PageLayout
      title='动态管理'
      showBack
      scrollable={false}
      showFloatingMenu={false}
    >
      <PageContent>
        <View className='lawyer-content-manage'>
          {/* 搜索栏 */}
          <View className='lawyer-search-header'>
            <View className='lawyer-search-box'>
              <Image
                src={SearchIcon}
                className='lawyer-search-icon'
                mode='aspectFit'
                onClick={handleSearch}
              />
              <Input
                className='lawyer-search-input'
                placeholder='搜索动态标题'
                value={searchKeyword}
                onInput={handleSearchInput}
                onConfirm={handleSearchConfirm}
                confirmType='search'
              />
            </View>
            <View className='lawyer-add-button' onClick={handleAddDynamics}>
              <Image src={AddIcon} className='lawyer-add-icon' mode='aspectFit' />
            </View>
          </View>

          {/* 分类筛选 */}
          <View className='lawyer-filter-section'>
            <View className='lawyer-filter-header' onClick={toggleCategoriesCollapse}>
              <Text className='lawyer-filter-title'>分类筛选</Text>
              <Image
                src={ClickMoreIcon}
                className={`lawyer-filter-arrow ${categoriesCollapsed ? '' : 'expanded'}`}
                mode='aspectFit'
              />
            </View>

            <View className={`lawyer-filter-content ${categoriesCollapsed ? 'collapsed' : 'expanded'}`}>
              <View className='lawyer-category-list'>
                <View
                  className={`lawyer-category-item ${!selectedCategoryId ? 'active' : ''}`}
                  onClick={() => handleCategorySelect(undefined)}
                >
                  <Text className='lawyer-category-text'>全部</Text>
                </View>
                {categories.map((category) => (
                  <View
                    key={category.id}
                    className={`lawyer-category-item ${selectedCategoryId === category.id ? 'active' : ''}`}
                    onClick={() => handleCategorySelect(category.id)}
                  >
                    <Text className='lawyer-category-text'>{category.name}</Text>
                  </View>
                ))}
              </View>
            </View>
          </View>

          {/* 动态列表 */}
          <View className='lawyer-content-list-container'>
            <ScrollView
              className='lawyer-content-list'
              scrollY
              onScrollToLower={handleLoadMore}
              lowerThreshold={100}
              refresherEnabled
              refresherTriggered={loading && filterParams.page === 1}
              onRefresherRefresh={handleRefresh}
            >
              {dynamicsList.map((dynamics) => {
                const statusInfo = getStatusInfo(dynamics.status)
                return (
                  <View
                    key={dynamics.id}
                    className='lawyer-content-card'
                    onClick={() => handleDynamicsClick(dynamics)}
                  >
                    <View className='lawyer-content-header'>
                      <View className='lawyer-content-title-row'>
                        <Text className='lawyer-content-title'>{dynamics.title}</Text>
                        <View className={`lawyer-content-status ${statusInfo.className}`}>
                          <Text className='status-text'>{statusInfo.text}</Text>
                        </View>
                      </View>
                      {dynamics.status === 3 && dynamics.rejectReason && (
                        <View className='reject-reason'>
                          <Text className='reject-reason-text'>驳回原因：{dynamics.rejectReason}</Text>
                        </View>
                      )}
                    </View>
                    <View className='lawyer-content-footer'>
                      <View className='lawyer-content-left'>
                        <Text className='lawyer-content-category'>{dynamics.categoryName}</Text>
                        <Text className='lawyer-content-stats'>浏览 {dynamics.viewCount}</Text>
                      </View>
                      <Text className='lawyer-content-time'>{formatTime(dynamics.createdAt)}</Text>
                    </View>
                  </View>
                )
              })}

              {/* 加载状态 */}
              {loading && (
                <View className='lawyer-loading-more'>
                  <Text>加载中...</Text>
                </View>
              )}

              {/* 没有更多数据 */}
              {!hasMore && dynamicsList.length > 0 && (
                <View className='lawyer-no-more'>
                  <Text>没有更多动态了</Text>
                </View>
              )}

              {/* 空状态 */}
              {!loading && dynamicsList.length === 0 && (
                <View className='lawyer-empty-state'>
                  <Text>暂无动态</Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </PageContent>
    </PageLayout>
  )
}

export default LawyerDynamicsManage
