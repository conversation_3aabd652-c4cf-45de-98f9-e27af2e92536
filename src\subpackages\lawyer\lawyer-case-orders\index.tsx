/**
 * 律师案件订单列表页面
 */
import React, { useState, useEffect } from 'react'
import { View, Text, ScrollView, Input, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'
import { FilterSection, CaseOrderCard } from '@/components'
import type { FilterOption } from '@/components/FilterSection/types'
import { lawyerApi } from '@/utils/request/apis'
import { STATUS_CODE } from '@/utils/request/config'
import CategoryCacheManager from '@/utils/cache/categoryCache'
import SearchIcon from '@/assets/images/common-icon/search.png'
import './index.scss'

const LawyerCaseOrders: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState(false)
  const [caseOrderList, setCaseOrderList] = useState<LawyerAPI.MyCaseOrderInfo[]>([])
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)
  const pageSize = 20

  // 搜索相关状态
  const [searchKeyword, setSearchKeyword] = useState('')

  // 筛选参数
  const [filterParams, setFilterParams] = useState<LawyerAPI.GetMyCaseOrderListRequest>({
    page: 1,
    pageSize
  })

  // 案件阶段数据
  const [caseStages, setCaseStages] = useState<FilterOption[]>([])
  const [stagesCollapsed, setStagesCollapsed] = useState(true)

  // 订单状态选项
  const statusOptions: FilterOption[] = [
    { id: 1, name: '待处理' },
    { id: 2, name: '待签约' },
    { id: 3, name: '跟进中' },
    { id: 4, name: '已完结' },
    { id: 5, name: '已作废' }
  ]
  const [statusCollapsed, setStatusCollapsed] = useState(true)

  // 加载案件阶段数据（使用缓存）
  const loadCaseStages = async () => {
    try {
      const caseStageList = await CategoryCacheManager.getCaseStageList()
      const stages = [
        ...caseStageList
      ]
      setCaseStages(stages)
    } catch (error) {
      console.error('加载案件阶段失败:', error)
      setCaseStages([{ id: 0, name: '全部' }])
    }
  }

  // 统一的数据加载函数
  const loadData = async (params: LawyerAPI.GetMyCaseOrderListRequest) => {
    if (loading) return

    try {
      setLoading(true)

      const response = await lawyerApi.getMyCaseOrderList(params)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        const newList = response.data.list || []

        if (params.page === 1) {
          setCaseOrderList(newList)
        } else {
          setCaseOrderList(prev => [...prev, ...newList])
        }

        // 判断是否还有更多数据
        setHasMore(newList.length === pageSize)
        setPage(params.page!)
      }
    } catch (error) {
      console.error('加载案件订单列表失败:', error)
      Taro.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  // 更新过滤参数并重新加载数据
  const updateFilterAndReload = (newParams: Partial<LawyerAPI.GetMyCaseOrderListRequest>) => {
    const updatedParams = {
      ...filterParams,
      ...newParams,
      page: 1,
      pageSize
    }
    setFilterParams(updatedParams)
    loadData(updatedParams)
  }

  // 案件阶段选择处理
  const handleCaseStageSelect = (stageId?: number) => {
    updateFilterAndReload({
      caseStageId: stageId && stageId !== 0 ? caseStages.find(s => s.id === stageId)?.id : undefined
    })
  }

  // 订单状态选择处理
  const handleStatusSelect = (statusId?: number) => {
    updateFilterAndReload({
      orderStatus: statusId && statusId !== 0 ? statusId : undefined
    })
  }

  // 加载更多数据
  const handleLoadMore = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1
      const params = {
        ...filterParams,
        page: nextPage
      }
      loadData(params)
    }
  }

  // 案件订单点击处理
  const handleCaseOrderClick = (caseOrder: LawyerAPI.MyCaseOrderInfo) => {
    // 跳转到律师案件详情页面
    Taro.navigateTo({
      url: `/subpackages/lawyer/lawyer-case-detail/index?id=${caseOrder.id}`
    })
  }

  // 切换案件阶段折叠状态
  const toggleStagesCollapse = () => {
    setStagesCollapsed(!stagesCollapsed)
  }

  // 切换订单状态折叠状态
  const toggleStatusCollapse = () => {
    setStatusCollapsed(!statusCollapsed)
  }

  // 搜索输入处理
  const handleSearchInput = (e: any) => {
    setSearchKeyword(e.detail.value)
  }

  // 搜索确认处理
  const handleSearchConfirm = (e: any) => {
    const value = e.detail.value
    setSearchKeyword(value)
    updateFilterAndReload({
      clientName: value
    })
  }

  // 搜索图标点击处理
  const handleSearchIconClick = () => {
    updateFilterAndReload({
      clientName: searchKeyword
    })
  }

  // 页面初始化
  useEffect(() => {
    loadCaseStages()
    loadData(filterParams)
  }, [])

  return (
    <PageLayout
      title='我的案件订单'
      showBack
      scrollable={false}
      showFloatingMenu={false}
    >
      <PageContent>
        <View className='lawyer-case-orders'>
          {/* 搜索框 */}
          <View className='lawyer-case-orders-search'>
            <View className='lawyer-case-orders-search-container'>
              <Image
                className='lawyer-case-orders-search-icon'
                src={SearchIcon}
                mode='aspectFit'
                onClick={handleSearchIconClick}
              />
              <Input
                className='lawyer-case-orders-search-input'
                placeholder='搜索委托人姓名'
                value={searchKeyword}
                onInput={handleSearchInput}
                onConfirm={handleSearchConfirm}
                confirmType='search'
                placeholderClass='lawyer-case-orders-search-placeholder'
              />
            </View>
          </View>

          {/* 案件阶段筛选 */}
          <FilterSection
            title='案件阶段'
            options={caseStages}
            selectedValue={caseStages.find(s => s.id === filterParams.caseStageId)?.id}
            collapsed={stagesCollapsed}
            onToggleCollapse={toggleStagesCollapse}
            onSelect={handleCaseStageSelect}
          />

          {/* 订单状态筛选 */}
          <FilterSection
            title='订单状态'
            options={statusOptions}
            selectedValue={filterParams.orderStatus}
            collapsed={statusCollapsed}
            onToggleCollapse={toggleStatusCollapse}
            onSelect={handleStatusSelect}
          />

          {/* 案件订单列表 */}
          <View className='lawyer-case-orders-list-container'>
            <ScrollView
              className='lawyer-case-orders-list'
              scrollY
              onScrollToLower={handleLoadMore}
              lowerThreshold={100}
            >
              {caseOrderList.map((caseOrder) => (
                <CaseOrderCard
                  key={caseOrder.id}
                  caseOrder={caseOrder}
                  viewType='lawyer'
                  onClick={handleCaseOrderClick}
                />
              ))}

              {/* 加载状态 */}
              {loading && (
                <View className='lawyer-case-orders-loading-more'>
                  <Text>加载中...</Text>
                </View>
              )}

              {/* 没有更多数据 */}
              {!hasMore && caseOrderList.length > 0 && (
                <View className='lawyer-case-orders-no-more'>
                  <Text>没有更多案件了</Text>
                </View>
              )}

              {/* 空状态 */}
              {!loading && caseOrderList.length === 0 && (
                <View className='lawyer-case-orders-empty-state'>
                  <Text>暂无案件订单</Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </PageContent>
    </PageLayout>
  )
}

export default LawyerCaseOrders


