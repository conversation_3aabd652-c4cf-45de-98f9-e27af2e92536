/**
 * 案件阶段标签组件
 * 
 * 提供可滑动的案件阶段选择功能，支持高亮下划线
 */
import React from 'react'
import { View, Text, ScrollView } from '@tarojs/components'
import type { FilterOption } from '@/components/FilterSection/types'
import './index.scss'

// 组件属性
export interface CaseStagesTabProps {
  // 案件阶段列表
  caseStages: FilterOption[]
  // 当前选中的阶段ID
  selectedStageId?: number
  // 阶段选择回调
  onStageSelect: (stageId?: number) => void
  // 自定义样式类名
  className?: string
}

const CaseStagesTab: React.FC<CaseStagesTabProps> = ({
  caseStages,
  selectedStageId,
  onStageSelect,
  className = ''
}) => {
  // 处理标签点击
  const handleTabClick = (stageId?: number) => {
    onStageSelect(stageId)
  }

  return (
    <View className={`case-stages-tab ${className}`}>
      <ScrollView
        className='case-stages-tab__scroll-view'
        scrollX
        showScrollbar={false}
        enhanced
        bounces={false}
      >
        <View className='case-stages-tab__container'>
          {/* 全部选项 */}
          <View
            className={`case-stages-tab__item ${!selectedStageId ? 'case-stages-tab__item--active' : ''}`}
            onClick={() => handleTabClick(undefined)}
          >
            <Text className='case-stages-tab__text'>全部</Text>
            {!selectedStageId && <View className='case-stages-tab__underline' />}
          </View>

          {/* 案件阶段选项 */}
          {caseStages.map((stage) => (
            <View
              key={stage.id}
              className={`case-stages-tab__item ${selectedStageId === stage.id ? 'case-stages-tab__item--active' : ''}`}
              onClick={() => handleTabClick(stage.id)}
            >
              <Text className='case-stages-tab__text'>{stage.name}</Text>
              {selectedStageId === stage.id && <View className='case-stages-tab__underline' />}
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  )
}

export default CaseStagesTab
