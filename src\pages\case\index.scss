/**
 * 案例页面样式
 */

.case-content {
  display: flex;
  flex-direction: column;
  gap: 30rpx;

  &__header {
    display: flex;
    flex-direction: column;
    gap: 30rpx;
    padding: 30rpx;
    background: #fff;
    border-radius: 20rpx;
  }

  &__list {
    padding:0 25rpx;
    border-radius: 50rpx;
  }
}

// 搜索容器
.search-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

// 搜索框
.search-box {
  position: relative;
  display: flex;
  align-items: center;
  width: 700rpx;
  height: 88rpx;
  background: #F7F7F7;
  border-radius: 44rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
  
  transition: opacity 0.2s ease;

  &:active {
    opacity: 0.6;
  }
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;

  &::placeholder {
    color: #999;
  }
}

// 分类区域
.category-section {
  display: flex;
  flex-direction: column;
  position: relative;
}

// 分类容器
.category-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  position: relative;
  transition: all 0.3s ease;

  &--collapsed {
    max-height: 106rpx;
    overflow: hidden;

    // 添加渐变遮罩效果，让第三行看起来是"半行"
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 30rpx;
      background: linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0.8) 100%);
      pointer-events: none;
    }
  }
}

// 展开/折叠按钮容器
.category-toggle-container {
  display: flex;
  justify-content: center;
  margin-top: 20rpx;
}

.category-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  background: rgba(189, 138, 79, 0.1);
  border-radius: 50%;
  
  transition: all 0.3s ease;

  &:active {
    background: rgba(189, 138, 79, 0.2);
    transform: scale(0.95);
  }
}

.toggle-icon {
  width: 32rpx;
  height: 32rpx;
  transition: transform 0.3s ease;

  &--expanded {
    transform: rotate(180deg);
  }
}

// 分类项
.category-item {
  padding: 15rpx 20rpx;
  background: #FFFFFF;
  border-radius: 40rpx;
  border: 1rpx solid #E6E9ED;
  display: flex;
  align-items: center;
  justify-content: center;


  &--active {
    border: 1rpx solid #BD8A4F;

    .category-text {
      color: #BD8A4F;
    }
  }
}

.category-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1;
}