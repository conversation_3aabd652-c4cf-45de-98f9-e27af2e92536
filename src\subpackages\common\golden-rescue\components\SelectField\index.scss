/**
 * 选择器字段组件样式
 */

.select-field {
  margin-bottom: 30rpx;

  .field-label {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .required-mark {
      color: #ff4d4f;
      font-size: 28rpx;
      margin-right: 8rpx;
    }

    .label-text {
      font-size: 28rpx;
      color: #333333;
      font-weight: 500;
    }
  }

  .field-content {
    width: 100%;
  }

  .select-trigger {
    width: 100%;
    height: 88rpx;
    background-color: #F7F7F7;
    border-radius: 8rpx;
    padding: 0 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;

    &.placeholder {
      .select-text {
        color: #999999;
      }
    }

    &.loading {
      .select-text {
        color: #999999;
      }
    }

    .select-text {
      font-size: 28rpx;
      color: #333333;
      flex: 1;
    }

    .arrow-icon {
      margin-left: 16rpx;

      .arrow {
        font-size: 32rpx;
        color: #999999;
        transform: rotate(90deg);
        display: inline-block;
      }
    }

    &:active {
      background-color: #f0f0f0;
    }
  }
}
