# 律师详情页面样式文件组织结构

## 文件说明

### 主入口文件
- `../index.scss` - 主样式文件，导入所有拆分的样式文件

### 拆分的样式文件

#### 1. `base.scss` - 基础样式
- 导航栏左侧样式 (`.nav-left`)
- 页面基础容器样式 (`.lawyer-detail`)
- 背景相关样式 (`.lawyer-detail-nav-bg`, `.lawyer-detail-bg`)
- Tab内容容器样式

#### 2. `avatar.scss` - 头像相关样式
- 头部信息区域 (`.lawyer-detail__header`)
- 律师头像 (`.lawyer-detail__avatar`)
- 认证图标 (`.lawyer-detail__verify`)
- 律师等级图标 (`.lawyer-detail__level-badge`)
- 姓名和关注按钮 (`.lawyer-detail__name-follow`)
- 聚焦信息框 (`.lawyer-detail__focus`)

#### 3. `tabs.scss` - Tab导航样式
- Tab导航容器 (`.lawyer-detail__tabs`)
- Tab项样式 (`.lawyer-detail__tab`)
- Tab文字样式 (`.lawyer-detail__tab-text`)

#### 4. `info.scss` - 信息内容样式
- 个人简介区域 (`.lawyer-detail__profile`)
- 擅长领域区域 (`.lawyer-detail__fields`)
- 律师数据卡片 (`.lawyer-detail__stats-card`)
- 律所信息区域 (`.lawyer-detail__firm`)
- 操作按钮区域 (`.lawyer-detail__actions`)

#### 5. `floating-menu.scss` - 浮动菜单样式
- 浮动操作菜单 (`.lawyer-detail__float-menu`)
- 菜单按钮 (`.lawyer-detail__menu-button`)
- 展开的菜单 (`.lawyer-detail__menu-expanded`)
- 菜单动画 (`@keyframes`)
- 菜单项样式 (`.lawyer-detail__menu-item`)

#### 6. `recommendation-list.scss` - 推荐列表样式
- 推荐动态列表 (`.lawyer-detail-dynamics-list`)
- 推荐案例列表 (`.lawyer-detail-case-list`)
- 共用的标题和头部样式

## 样式命名规范

### 推荐列表类名更新
- 原来：`.lawyer-dynamics-list` → 现在：`.lawyer-detail-dynamics-list`
- 原来：`.lawyer-case-list` → 现在：`.lawyer-detail-case-list`

### BEM命名规范
- 块（Block）：`.lawyer-detail`
- 元素（Element）：`.lawyer-detail__header`
- 修饰符（Modifier）：`.lawyer-detail__tab--active`

## 文件拆分的优势

1. **模块化管理**：每个文件负责特定功能区域的样式
2. **易于维护**：修改特定功能时只需要关注对应的样式文件
3. **代码复用**：推荐列表样式可以被动态和案例共用
4. **团队协作**：不同开发者可以同时修改不同的样式文件
5. **性能优化**：可以按需加载特定的样式文件

## 使用方式

在主样式文件中通过 `@import` 导入所有拆分的样式文件：

```scss
// 导入拆分的样式文件
@import './styles/base.scss';
@import './styles/avatar.scss';
@import './styles/tabs.scss';
@import './styles/info.scss';
@import './styles/floating-menu.scss';
@import './styles/recommendation-list.scss';
```

## 注意事项

1. 修改样式时请在对应的拆分文件中进行修改
2. 新增样式时请根据功能归类到合适的文件中
3. 保持文件间的样式独立性，避免相互依赖
4. 推荐列表样式已经统一，动态和案例列表可以共用
