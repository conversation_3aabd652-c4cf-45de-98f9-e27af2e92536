# CaseList 组件

纵向案例列表组件，支持外部传入筛选条件，支持滚动加载功能。

## 功能特性

- 纵向布局展示案例列表
- 支持外部传入筛选条件
- 支持外部传入案例数据
- 支持滚动加载更多数据
- 自动处理加载状态和空状态
- 支持优质案例标识显示

## Props 参数

| 参数 | 类型 | 默认值 | 必填 | 说明 |
|------|------|--------|------|------|
| filterParams | `CaseAPI.GetCaseListRequest` | - | 否 | 案例列表接口筛选条件 |
| list | `CaseAPI.CaseListInfo[]` | - | 否 | 外部传入的案例列表数据 |
| className | `string` | `''` | 否 | 自定义样式类名 |
| enableScrollLoad | `boolean` | `false` | 否 | 是否启用滚动加载功能 |
| scrollHeight | `string` | `'600rpx'` | 否 | 滚动容器高度（仅在enableScrollLoad为true时生效） |
| fullHeight | `boolean` | `false` | 否 | 是否使用全屏高度 |

## 使用方式

### 基础用法

```tsx
import CaseList from '@/components/CaseList'

// 基础用法 - 组件内部处理数据加载
<CaseList
  filterParams={{
    categoryId: 1,
    isExcellent: 1
  }}
  className='my-case-list'
/>
```

### 外部数据传入

```tsx
import CaseList from '@/components/CaseList'

const [cases, setCases] = useState<CaseAPI.CaseListInfo[]>([])

// 外部传入数据
<CaseList
  list={cases}
  className='my-case-list'
/>
```

### 启用滚动加载

```tsx
import CaseList from '@/components/CaseList'

const MyComponent = () => {
  return (
    <>
      {/* 默认高度 600rpx */}
      <CaseList
        filterParams={{
          categoryId: 1,
          isExcellent: 1
        }}
        enableScrollLoad={true}
        className='scroll-case-list'
      />

      {/* 自定义高度 */}
      <CaseList
        filterParams={{
          categoryId: 1,
          isExcellent: 1
        }}
        enableScrollLoad={true}
        scrollHeight='800rpx'
        className='custom-height-case-list'
      />

      {/* 全屏高度 */}
      <CaseList
        filterParams={{
          categoryId: 1,
          isExcellent: 1
        }}
        enableScrollLoad={true}
        fullHeight={true}
        className='full-height-case-list'
      />
    </>
  )
}
```

## 注意事项

1. **数据源优先级**：当同时传入 `list` 和 `filterParams` 时，优先使用 `list` 数据
2. **滚动加载限制**：只有在使用 `filterParams` 且启用 `enableScrollLoad` 时才支持滚动加载，外部传入 `list` 时不支持滚动加载
3. **高度设置**：启用滚动加载时必须设置容器高度，可通过以下方式：
   - 默认高度：不设置任何高度参数，使用默认的 600rpx
   - 自定义高度：通过 `scrollHeight` 参数设置具体高度值
   - 全屏高度：设置 `fullHeight={true}` 使用全屏高度
4. **防抖处理**：组件内部已实现滚动加载的防抖处理，避免重复请求
5. **页码管理**：滚动加载的页码由组件内部自动管理
6. **加载状态**：区分初始加载状态和滚动加载状态，提供不同的用户反馈
7. **自动判断**：组件会根据返回数据的数量自动判断是否还有更多数据

## 样式定制

组件提供了丰富的 CSS 类名，可以通过覆盖样式进行定制：

- `.case-list` - 组件根容器
- `.case-list__loading` - 初始加载状态
- `.case-list__scroll-loading` - 滚动加载状态
- `.case-list__no-more` - 没有更多数据提示
- `.case-list__empty` - 空状态容器
- `.case-item` - 案例项容器
- `.case-item__title` - 案例标题
- `.case-item__category` - 案例分类
- `.case-item__time` - 创建时间
