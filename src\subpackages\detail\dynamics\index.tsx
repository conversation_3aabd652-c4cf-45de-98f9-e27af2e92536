/**
 * 律师动态详情页面
 */
import React, { useState, useEffect } from 'react'
import { View, Text, Image } from '@tarojs/components'
import BookMark from '@/assets/images/common-icon/book_mark_color.png'
import { useRouter } from '@tarojs/taro'
import { dynamicsApi, commonApi } from '@/apis'
import UserCacheManager from '@/utils/cache/userCache'
import { STATUS_CODE } from '@/utils/request/config'
import { formatTime, parseImageUrl } from '@/utils'
import { LawyerBaseInfo, FollowLawyerButton } from '@/components'
import PageLayout, { PageContent } from '@/components/PageLayout'
import clockIcon from '@/assets/images/common-icon/clock.png'
import eyeIcon from '@/assets/images/common-icon/eye.png'
import HorizontalDynamicsList from '@/components/HorizontalDynamicsList'
import './index.scss'

interface DynamicsDetailInfo {
  id: number
  title: string
  content: string
  categoryId: number
  categoryName: string
  viewCount: number
  createdAt: string
}

const DynamicsDetail: React.FC = () => {
  const router = useRouter()
  const { id } = router.params
  const userId = UserCacheManager.getUserId()
  const [dynamicsInfo, setDynamicsInfo] = useState<DynamicsDetailInfo | null>(null)
  const [isFollowed, setIsFollowed] = useState(false)
  const [lawyerInfo, setLawyerInfo] = useState<LawyerAPI.LawyerInfo | null>(null)
  // 推荐动态列表
  const [recommendDynamics, setRecommendDynamics] = useState<DynamicsAPI.DynamicsListInfo[]>([])

  // 加载推荐动态列表
  const loadRecommendDynamics = async () => {
    try {
      const response = await dynamicsApi.getRecommendDynamicsList(Number(id), {
        page: 1,
        pageSize: 5,
        dynamicsId: Number(id)
      })
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setRecommendDynamics(response.data.list || [])
      }
    } catch (error) {
      console.error('加载推荐动态列表失败:', error)
    }
  }



  // 检查关注状态
  const checkFollowStatus = async (lawyerId: number) => {
    if (!userId) {
      return
    }
    try {
      const response = await commonApi.checkUserBusinessStatus({
        bizId: lawyerId,
        userId,
        bizType: ['follow']
      })
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setIsFollowed(response.data.follow)
      }
    } catch (error) {
      console.error('检查关注状态失败:', error)
    }
  }

  // 处理关注状态变化
  const handleFollowChange = (followed: boolean) => {
    setIsFollowed(followed)
  }

  // 加载动态详情
  const loadDynamicsDetail = async (dynamicsId: number) => {
    try {
      if (id) {
        const response = await dynamicsApi.getDynamicsDetail(dynamicsId)
        if (response.code === STATUS_CODE.SUCCESS && response.data) {
          setDynamicsInfo(response.data.detail)
          setLawyerInfo({ ...response.data.lawyerInfo, figurePhotoUrl: parseImageUrl(response.data.lawyerInfo.figurePhotoUrl) })
          // 检查关注状态
          checkFollowStatus(response.data.lawyerInfo.id)
        }
      }
    } catch (error) {
      console.error('加载动态详情失败:', error)
    }
  }

  useEffect(() => {
    if (id) {
      loadDynamicsDetail(Number(id))
      loadRecommendDynamics()
    }
  }, [id])

  return (
    <PageLayout
      title='律师动态详情'
      backgroundColor='#fff'
      navBarTextColor='#BD8A4F'
      showBack
    >
      <PageContent padded='b'>
        <View className='dynamics-detail'>
          {dynamicsInfo && (
            <View className='dynamics-detail__content'>
              {lawyerInfo && <LawyerBaseInfo
                lawyerInfo={lawyerInfo}
              />}
              {/* 动态信息卡片 */}
              <View className='dynamics-info-card'>
                {/* 标题 */}
                <Text className='dynamics-info-card__title'>{dynamicsInfo.title}</Text>

                {/* 信息模块 */}
                <View className='dynamics-info-card__meta'>
                  <Text className='dynamics-info-card__category'>{dynamicsInfo.categoryName}</Text>
                  <View className='dynamics-info-card__time'>
                    <Image
                      className='dynamics-info-card__time-icon'
                      src={clockIcon}
                      mode='aspectFit'
                    />
                    <Text className='dynamics-info-card__time-text'>
                      {formatTime(dynamicsInfo.createdAt)}
                    </Text>
                  </View>
                  <View className='dynamics-info-card__view'>
                    <Image
                      className='dynamics-info-card__view-icon'
                      src={eyeIcon}
                      mode='aspectFit'
                    />
                    <Text className='dynamics-info-card__view-text'>{dynamicsInfo.viewCount}</Text>
                  </View>
                </View>
              </View>
              {/* 动态内容 */}
              <View className='dynamics-content'>
                <Text className='dynamics-content__title'>动态详情</Text>
                <Text className='dynamics-content__text'>
                  {dynamicsInfo.content || '暂无详细内容'}
                </Text>
              </View>
              <View className='dynamics-detail-list'>
                <View className='dynamics-detail-list__header'>
                  <View className='dynamics-detail-list__title'>
                    <Image className='dynamics-detail-list__title-icon' src={BookMark} mode='aspectFit' />
                    <Text className='dynamics-detail-list__title-text'>相关动态</Text>
                  </View>
                </View>
                <HorizontalDynamicsList list={recommendDynamics} />
              </View>
            </View>
          )}
        </View>
        {/* 关注律师按钮 */}
        {lawyerInfo && <FollowLawyerButton
          lawyerId={lawyerInfo.id}
          isFollowed={isFollowed}
          onFollowChange={handleFollowChange}
        />}
      </PageContent>
    </PageLayout>
  )
}

export default DynamicsDetail
