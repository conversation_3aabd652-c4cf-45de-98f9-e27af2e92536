/**
 * 文章相关接口
 * 基于 api.json 接口文档定义
 */
import request from '../../index'

// 文章相关接口
export const articleApi = {
  // 获取文章列表
  getArticleList: (params?: ArticleAPI.GetArticleListRequest) =>
    request.get<ArticleAPI.GetArticleListResponse>('/mini/find-law-article/list', params),

  // 获取文章详情
  getArticleDetail: (articleId: number) =>
    request.get<ArticleAPI.GetArticleDetailResponse>(`/mini/find-law-article/${articleId}/detail`),

  // 获取律师文章数据统计
  getLawyerArticleCount: (lawyerId: number) =>
    request.get<ArticleAPI.GetLawyerArticleCountResponse>('/mini/find-law-article/lawyer-count', { lawyerId }),

  // 收藏文章 (需要认证)
  collectArticle: (articleId: number) =>
    request.post<ArticleAPI.CollectArticleResponse>(`/mini/auth-law-article/${articleId}/collect`, { articleId }),

  // 点赞文章 (需要认证)
  likeArticle: (articleId: number) =>
    request.post<ArticleAPI.LikeArticleResponse>(`/mini/auth-law-article/${articleId}/nice`, { articleId }),

  // 取消收藏文章 (需要认证)
  cancelCollectArticle: (articleId: number) =>
    request.post<ArticleAPI.CancelCollectArticleResponse>(`/mini/auth-law-article/${articleId}/cancel-collect`, { articleId }),

  // 取消点赞文章 (需要认证)
  cancelLikeArticle: (articleId: number) =>
    request.post<ArticleAPI.CancelLikeArticleResponse>(`/mini/auth-law-article/${articleId}/cancel-nice`, { articleId }),

  // 获取我的收藏文章列表 (律师个人中心)
  getMyCollectedArticles: (params?: ArticleAPI.GetMyCollectedArticlesRequest) =>
    request.get<ArticleAPI.GetMyCollectedArticlesResponse>('/mini/owner-center-lawyer/law-article-collect/list', params),

  // 获取我的文章列表 (律师个人中心)
  getMyArticleList: (params?: ArticleAPI.GetMyArticleListRequest) =>
    request.get<ArticleAPI.GetMyArticleListResponse>('/mini/owner-center-lawyer/law-article/list', params),

  // 获取我的文章详情 (律师个人中心)
  getMyArticleDetail: (articleId: number) =>
    request.get<ArticleAPI.GetMyArticleDetailResponse>(`/mini/owner-center-lawyer/law-article/${articleId}/detail`),

  // 发布文章 (律师个人中心)
  publishArticle: (data: ArticleAPI.PublishArticleRequest) =>
    request.post<ArticleAPI.PublishArticleResponse>('/mini/owner-center-lawyer/law-article/add', data),

  // 修改文章 (律师个人中心)
  updateMyArticle: (articleId: number, data: ArticleAPI.UpdateMyArticleRequest) =>
    request.post<ArticleAPI.UpdateMyArticleResponse>(`/mini/owner-center-lawyer/law-article/${articleId}/save`, data),
}
