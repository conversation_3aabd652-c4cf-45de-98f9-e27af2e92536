/**
 * 横向案例组件
 * 
 * 展示最多5个案例卡片，支持横向滑动，支持外部传入筛选条件
 */
import React, { useState, useEffect } from 'react'
import { View, ScrollView, Text, Image } from '@tarojs/components'
import { caseApi } from '@/utils/request/apis'
import { STATUS_CODE } from '@/utils/request/config'
import { navigateToPage } from '@/utils'
import ClockIcon from '@/assets/images/common-icon/clock.png'
import { HorizontalCaseListProps } from './types'
import './index.scss'


const HorizontalCaseList: React.FC<HorizontalCaseListProps> = ({
  filterParams,
  list,
  className = ''
}) => {
  // 案例列表数据
  const [cases, setCases] = useState<CaseAPI.CaseListInfo[]>([])
  // 加载状态
  const [isLoading, setIsLoading] = useState(false)

  // 加载案例列表数据
  const loadCases = async (val?: CaseAPI.GetCaseListRequest) => {
    try {
      setIsLoading(true)
      const params = {
        page: 1,
        pageSize: 5,
        ...val,
      }

      const response = await caseApi.getCaseList(params)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setCases(response.data.list || [])
      }
    } catch (error) {
      console.error('加载案例列表失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理案例卡片点击 - 跳转到案例详情页
  const handleCaseClick = (caseInfo: CaseAPI.CaseListInfo) => {
    navigateToPage(`/subpackages/detail/case/index?id=${caseInfo.id}`)
  }

  // 使用外部传入的数据
  useEffect(() => {
    if (list) {
      setCases(list)
      setIsLoading(false)
    }
  }, [list])

  // 监听filterParams变化，重新加载数据（仅在没有传入list时）
  useEffect(() => {
    if (!list) {
      loadCases(filterParams)
    }
  }, [filterParams, list])

  // 显示加载状态
  const showLoading = isLoading

  return (
    <View className={`horizontal-case-list ${className}`}>
      {showLoading ? (
        <View className='horizontal-case-list__loading'>
          <Text>加载中...</Text>
        </View>
      ) : (
        <>
          {cases.length > 0 ? (
            <ScrollView
              className='horizontal-case-list__scroll-view'
              scrollX
              showScrollbar={false}
              enhanced
              bounces={false}

            >
              <View className='horizontal-case-list__container'>
                {cases.map((caseInfo) => (
                  <View
                    key={caseInfo.id}
                    className='horizontal-case-card'
                    onClick={() => handleCaseClick(caseInfo)}
                  >
                    <Text className='horizontal-case-card__title'>{caseInfo.title}</Text>
                    <View className='horizontal-case-card__meta'>
                      <Text className='horizontal-case-card__category'>{caseInfo.categoryName}</Text>
                      <Image src={ClockIcon} mode='aspectFit' className='horizontal-case-card__clock-icon' />
                      <Text className='horizontal-case-card__time'>{caseInfo.createdAt}</Text>
                    </View>
                  </View>
                ))}
              </View>
            </ScrollView>
          ) : (
            <View className='horizontal-case-list__empty'>
              <Text className='horizontal-case-list__empty-title'>暂无案例信息</Text>
              <Text className='horizontal-case-list__empty-desc'>当前没有相关案例，请稍后再试</Text>
            </View>
          )}
        </>
      )}
    </View>
  )
}

// 导出组件
export default HorizontalCaseList

// 导出类型
export type { HorizontalCaseListProps } from './types'
