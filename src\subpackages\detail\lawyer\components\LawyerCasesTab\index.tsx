/**
 * 律师案例Tab内容组件
 *
 * 包含案例搜索、分类筛选和案例列表三个部分
 */
import React, { useState, useEffect } from 'react'
import Taro from '@tarojs/taro'
import { View } from '@tarojs/components'
import { caseApi } from '@/utils/request/apis'
import { STATUS_CODE } from '@/utils/request/config'
import CategoryCacheManager from '@/utils/cache/categoryCache'
import { CasesHeader, CasesCategories, CaseList } from './components'
import './index.scss'

// 组件属性
export interface LawyerCasesTabProps {
  // 律师ID
  lawyerId: number
  // 是否显示加载状态
  loading?: boolean
  // 自定义样式类名
  className?: string
}

const PAGE = 1
const PAGE_SIZE = 5

const LawyerCasesTab: React.FC<LawyerCasesTabProps> = ({
  lawyerId,
  className = ''
}) => {
  // 搜索关键词
  const [searchKeyword, setSearchKeyword] = useState('')
  // 案例分类列表
  const [categories, setCategories] = useState<CaseAPI.CaseCategoryInfo[]>([])
  // 选中的分类ID
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null)
  // 分类是否展开
  const [isCategoryExpanded, setIsCategoryExpanded] = useState(false)
  // 案例列表
  const [cases, setCases] = useState<CaseAPI.CaseListInfo[]>([])
  const [isLoading, setIsLoading] = useState(false)
  // 过滤条件
  const [filterParams, setFilterParams] = useState<CaseAPI.GetCaseListRequest>({
    page: PAGE,
    pageSize: PAGE_SIZE,
    lawyerId
  })

  // 加载案例分类（使用缓存）
  const loadCategories = async () => {
    try {
      const categorieList = await CategoryCacheManager.getCaseCategoryList()
      const allCategory = { id: 0, name: '全部' }
      setCategories([allCategory, ...categorieList])
      setSelectedCategoryId(0) // 默认选中全部
    } catch (error) {
      console.error('加载分类失败:', error)
    }
  }

  // 加载律师案例列表
  const loadCasesList = async (val?: CaseAPI.GetCaseListRequest) => {
    try {
      setIsLoading(true)
      const response = await caseApi.getCaseList({ ...val })
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setCases(response.data.list || [])
      }
    } catch (error) {
      console.error('加载案例列表失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理搜索输入（只更新输入框状态，不触发搜索）
  const handleSearchInput = (e: any) => {
    setSearchKeyword(e.detail.value)
  }

  // 执行搜索（点击搜索图标或软键盘搜索时触发）
  const handleSearch = () => {
    setFilterParams(prev => ({
      ...prev,
      title: searchKeyword
    }))
  }

  // 处理软键盘搜索确认
  const handleSearchConfirm = (e: any) => {
    const value = e.detail.value
    setSearchKeyword(value)
    setFilterParams(prev => ({
      ...prev,
      title: value
    }))
  }

  // 处理分类选择
  const handleCategorySelect = (categoryId: number) => {
    setSelectedCategoryId(categoryId)
    setFilterParams(prev => ({
      ...prev,
      categoryId,
      title: searchKeyword
    }))
  }

  // 切换分类展开状态
  const toggleCategoryExpanded = () => {
    setIsCategoryExpanded(!isCategoryExpanded)
  }

  // 处理案例点击
  const handleCaseClick = (caseInfo: CaseAPI.CaseListInfo) => {
    Taro.navigateTo({
      url: `/subpackages/detail/case/index?id=${caseInfo.id}`
    })
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadCategories()
  }, [])

  useEffect(() => {
    loadCasesList(filterParams)
  }, [filterParams])

  return (
    <View className={`lawyer-cases-tab ${className}`}>
      {/* 标题和搜索框 */}
      <CasesHeader
        searchKeyword={searchKeyword}
        onSearchInput={handleSearchInput}
        onSearchConfirm={handleSearchConfirm}
        onSearchIconClick={handleSearch}
      />

      {/* 案例类型选择 */}
      <CasesCategories
        categories={categories}
        selectedCategoryId={selectedCategoryId}
        isExpanded={isCategoryExpanded}
        onCategorySelect={handleCategorySelect}
        onToggleExpanded={toggleCategoryExpanded}
      />

      {/* 案例列表 */}
      <CaseList
        cases={cases}
        isLoading={isLoading}
        onCaseClick={handleCaseClick}
        className='lawyer-cases-tab__list'
      />
    </View>
  )
}

export default LawyerCasesTab
