/**
 * 横向文章列表组件样式
 */

.horizontal-article-list {
  width: 100%;

  &__loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200rpx;
    color: #999;
    font-size: 28rpx;
  }

  &__empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 60rpx 40rpx;
    text-align: center;
  }

  &__empty-title {
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
    margin-bottom: 12rpx;
  }

  &__empty-desc {
    font-size: 22rpx;
    color: #999;
    line-height: 1.4;
    max-width: 400rpx;
  }

  &__scroll-view {
    width: 100%;
    padding-right: 30rpx;
    white-space: nowrap;
  }

  &__container {
    display: flex;
    flex-direction: row;
    padding: 0 30rpx;
  }
}

// 文章卡片样式
.horizontal-article-card {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  justify-content: space-between;
  width: 520rpx;
  height: 243rpx;
  background: #FAE8CD;
  box-shadow: 0rpx 2rpx 48rpx 0rpx rgba(0, 0, 0, 0.04);
  border-radius: 14rpx;
  padding: 30rpx;
  margin-right: 30rpx;
  vertical-align: top;
  white-space: normal;
  box-sizing: border-box;
  
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }

  &:last-child {
    margin-right: 0;
  }

  &__title {
    font-weight: 600;
    font-size: 28rpx;
    color: #000000;
    line-height: 36rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    margin-bottom: 20rpx;
  }

  &__meta {
    display: flex;
    align-items: center;
    margin-top: auto;
  }

  &__info-icon {
    width: 24rpx;
    height: 24rpx;
    margin-right: 8rpx;
  }

  &__info-text {
    font-weight: 500;
    font-size: 20rpx;
    color: #666666;
    line-height: 27rpx;
    margin-right: 20rpx;
  }
}
