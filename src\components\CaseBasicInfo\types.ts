/**
 * CaseBasicInfo 组件类型定义
 */

// 通用案件详情信息接口
export interface CaseDetailInfo {
  id: number
  orderNo: string
  caseTypeName: string
  caseStage: string
  handlingAgency: string
  province: string
  city: string
  district: string
  orderStatus: number
  paymentStatus: number
  amountInvolvedOfCase: number
  paymentAmount: number
  source: string
  createdAt: string
  reviewTime?: string
  // 委托人视角字段
  lawyerName?: string
  // 律师视角字段
  clientName?: string
  clientMobile?: string
  lawyerRequirements?: string
  [key: string]: any
}

// 组件属性
export interface CaseBasicInfoProps {
  // 案件详情信息
  caseDetail: CaseDetailInfo
  // 视角类型：member(委托人) 或 lawyer(律师)
  viewType?: 'member' | 'lawyer'
  // 自定义样式类名
  className?: string
}
