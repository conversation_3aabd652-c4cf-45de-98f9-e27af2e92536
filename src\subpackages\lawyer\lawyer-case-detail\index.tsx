/**
 * 律师案件详情页面
 */
import React, { useState, useEffect, useMemo } from 'react'
import { View, PageContainer } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'
import { CaseBasicInfo, CaseStageTimeline } from '@/components'
import { lawyerApi } from '@/utils/request/apis'
import { STATUS_CODE } from '@/utils/request/config'
import { NextStageButton, ProgressEditModal } from './components'
import './index.scss'

const LawyerCaseDetail: React.FC = () => {
  const router = useRouter()
  const { id } = router.params
  const orderId = Number(id)

  // 数据状态
  const [caseDetail, setCaseDetail] = useState<LawyerAPI.GetMyCaseOrderDetailResponse | null>(null)
  const [stageRecords, setStageRecords] = useState<LawyerAPI.GetMyCaseOrderStageRecordsResponse | null>(null)

  // 弹窗状态
  const [showEditModal, setShowEditModal] = useState(false)
  const [editingProgress, setEditingProgress] = useState<LawyerAPI.ProgressData | null>(null)

  // 进入下一阶段状态
  const [nextStageLoading, setNextStageLoading] = useState(false)

  // 加载案件详情
  const loadCaseDetail = async () => {
    try {
      const response = await lawyerApi.getMyCaseOrderDetail(orderId)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setCaseDetail(response.data)
      } else {
        throw new Error(response.message || '获取案件详情失败')
      }
    } catch (e) {
      console.error('加载案件详情失败:', e)
      throw e
    }
  }

  // 加载案件阶段记录
  const loadStageRecords = async () => {
    try {
      const response = await lawyerApi.getMyCaseOrderStageRecords(orderId)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setStageRecords(response.data)
      } else {
        throw new Error(response.message || '获取阶段记录失败')
      }
    } catch (e) {
      console.error('加载阶段记录失败:', e)
      throw e
    }
  }

  // 加载页面数据
  const loadPageData = async () => {
    try {
      await Promise.all([
        loadCaseDetail(),
        loadStageRecords()
      ])
    } catch (e) {
      console.error('加载页面数据失败:', e)
    }
  }
  // 完成当前阶段进度
  const handleCompleteProgress = async (orderStageId: number, progressId: number) => {
    try {
      // 真实API调用
      const response = await lawyerApi.completeCaseOrderProgress(orderStageId, progressId!)
      if (response.code === STATUS_CODE.SUCCESS) {
        Taro.showToast({
          title: '进度已完成',
          icon: 'success'
        })
      } else {
        throw new Error(response.message || '操作失败')
      }
      // 刷新阶段记录
      await loadStageRecords()
    } catch (error) {
      console.error('完成进度失败:', error)
      Taro.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  }

  // 更新阶段记录
  const handleFollowProgress = async (data: LawyerAPI.FollowCaseOrderProgressRequest) => {
    try {
      if (!data) return
      const response = await lawyerApi.followCaseOrderProgress(data.orderStageId, data)
      if (response.code === STATUS_CODE.SUCCESS) {
        setStageRecords(response.data)
        Taro.showToast({
          title: '记录已更新',
          icon: 'success'
        })
      } else {
        throw new Error(response.message || '操作失败')
      }
      // 关闭弹窗
      setShowEditModal(false)
      setEditingProgress(null)
      // 刷新阶段记录
      await loadStageRecords()
    } catch (error) {
      console.error('更新记录失败:', error)
      Taro.showToast({
        title: '更新失败',
        icon: 'none'
      })
    }
  }

  // 打开编辑弹窗
  const handleEditProgress = (progress: LawyerAPI.ProgressData) => {
    setEditingProgress(progress)
    setShowEditModal(true)
  }

  // 关闭编辑弹窗
  const handleCloseEditModal = () => {
    setShowEditModal(false)
    setEditingProgress(null)
  }

  // 更新进度
  const handleProceedToNext = async () => {
    if (!stageRecords?.progressData || stageRecords.progressData.length === 0) return
    const lastProgess = stageRecords.progressData.find(progress => progress.done === 1)
    setNextStageLoading(true)
    if (!lastProgess) return
    try {
      await handleCompleteProgress(lastProgess?.orderStageId, lastProgess?.id!)
    } catch (error) {
      console.error('更新进度失败:', error)
      Taro.showToast({
        title: '操作失败',
        icon: 'none'
      })
    } finally {
      setNextStageLoading(false)
    }
  }

  // 判断是否可以进入下一阶段
  const canProceedToNext = useMemo(() => {
    const progressDataList = stageRecords?.progressData
    if (!progressDataList) return false
    return !progressDataList.every(progress => progress.done === 2)
  }, [stageRecords])

  // 页面初始化
  useEffect(() => {
    if (orderId) {
      loadPageData()
    }
  }, [orderId])

  // 下拉刷新
  const handleRefresh = async () => {
    await loadPageData()
  }

  return (
    <>
      <PageLayout
        title='案件详情'
        showBack
        backgroundColor='#f5f5f5'
        enablePullRefresh
        onPullRefresh={handleRefresh}
        showFloatingMenu={false}
      >
        <PageContent>
          {(caseDetail && stageRecords) ? (
            <View
              className='lawyer-case-detail'
              style={{
                paddingBottom: canProceedToNext ? '300rpx' : '20rpx'
              }}
            >
              {/* 案件基本信息 */}
              <CaseBasicInfo caseDetail={caseDetail} viewType='lawyer' />
              {/* 案件阶段时间线 */}
              {stageRecords.progressData ? <CaseStageTimeline
                stageRecords={stageRecords}
                onEditProgress={handleEditProgress}
                isLawyer
              /> : null}
            </View>
          ) : null}
        </PageContent>
      </PageLayout>

      {/* 进入下一阶段按钮 */}
      {(caseDetail && stageRecords && canProceedToNext) && (
        <NextStageButton
          stageRecords={stageRecords}
          canProceedToNext={canProceedToNext}
          onProceedToNext={handleProceedToNext}
          loading={nextStageLoading}
        />
      )}

      {/* 编辑进度弹窗 */}
      <PageContainer
        show={showEditModal}
        round
        overlay
        onClickOverlay={handleCloseEditModal}
      >
        {editingProgress && (
          <ProgressEditModal
            progress={editingProgress}
            onSubmit={handleFollowProgress}
            onCancel={handleCloseEditModal}
          />
        )}
      </PageContainer>
    </>
  )
}

export default LawyerCaseDetail

