/**
 * 律师详情页面
 */
import React, { useState, useEffect } from 'react'
import { View, Text, Image } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import { useNavBarHeight } from '@/components/NavBar/hooks/useNavBarHeight'
import { navigateBack, parseImageUrl } from '@/utils'
import { lawyerApi, commonApi, articleApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
import PageLayout, { PageContent } from '@/components/PageLayout'
import UserCacheManager from '@/utils/cache/userCache'
import leftButtonIcon from '@/assets/images/common-icon/left_button.png'
import { LawyerBg } from '@/constant/image'

import './index.scss'
import {
  LawyerAvatar,
  LawyerTabs,
  LawyerInfoTab,
  LawyerDynamicsTab,
  LawyerArticleTab,
  LawyerCasesTab,
  type TabType
} from './components'

const LawyerDetail: React.FC = () => {
  const router = useRouter()
  const { lawyerId } = router.params
  const cachedUserId = UserCacheManager.getUserId()

  const [lawyerInfo, setLawyerInfo] = useState<LawyerAPI.LawyerInfo | null>(null)
  const [lawyerCountData, setLawyerCountData] = useState<LawyerAPI.LawyerCountDataDto | null>(null)
  const [contentNum, setContentNum] = useState<ArticleAPI.GetLawyerArticleCountResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [isFollowed, setIsFollowed] = useState(false)
  const [activeTab, setActiveTab] = useState<TabType>('info')
  const [isProfileExpanded, setIsProfileExpanded] = useState(false)

  // 记录每个tab的滚动位置
  const [tabScrollPositions, setTabScrollPositions] = useState<Record<TabType, number>>({
    info: 0,
    dynamics: 0,
    cases: 0,
    articles: 0
  })

  // 切换关注状态
  const toggleFollow = async () => {
    if (!lawyerId) return

    try {
      if (isFollowed) {
        // 取消关注
        const response = await lawyerApi.cancelFollowLawyer(Number(lawyerId))
        if (response.code === STATUS_CODE.SUCCESS) {
          setIsFollowed(false)
          Taro.showToast({
            title: '已取消关注',
            icon: 'success',
            duration: 1500
          })
        } else {
          Taro.showToast({
            title: '取消关注失败',
            icon: 'error',
            duration: 1500
          })
        }
      } else {
        // 关注律师
        const response = await lawyerApi.followLawyer(Number(lawyerId))
        if (response.code === STATUS_CODE.SUCCESS) {
          setIsFollowed(true)
          Taro.showToast({
            title: '关注成功',
            icon: 'success',
            duration: 1500
          })
        } else {
          Taro.showToast({
            title: '关注失败',
            icon: 'error',
            duration: 1500
          })
        }
      }
    } catch (error) {
      console.error('关注操作失败:', error)
      // 检查是否是认证相关错误（401未登录）
      if (error?.response?.status === 401) {
        Taro.showToast({
          title: '请先登录',
          icon: 'none',
          duration: 1500
        })
        // 可以在这里跳转到登录页面
        // Taro.navigateTo({ url: '/pages/login/index' })
      } else {
        Taro.showToast({
          title: isFollowed ? '取消关注失败' : '关注失败',
          icon: 'error',
          duration: 1500
        })
      }
    }
  }

  // 切换简介展开状态
  const toggleProfile = () => {
    setIsProfileExpanded(!isProfileExpanded)
  }

  // 保存当前tab的滚动位置
  const saveCurrentScrollPosition = () => {
    return new Promise<void>((resolve) => {
      // 获取页面滚动位置
      Taro.createSelectorQuery()
        .selectViewport()
        .scrollOffset()
        .exec((res) => {
          if (res && res[0]) {
            const scrollTop = res[0].scrollTop
            setTabScrollPositions(prev => ({
              ...prev,
              [activeTab]: scrollTop
            }))
          }
          resolve()
        })
    })
  }

  // 恢复指定tab的滚动位置
  const restoreScrollPosition = (tab: TabType) => {
    const savedPosition = tabScrollPositions[tab] || 0

    if (savedPosition > 0) {
      // 延迟执行，确保页面内容已渲染
      setTimeout(() => {
        Taro.pageScrollTo({
          scrollTop: savedPosition,
          duration: 0 // 不使用动画，直接跳转
        })
      }, 150)
    }
  }

  // 切换tab
  const handleTabChange = async (tab: TabType) => {
    // 保存当前tab的滚动位置
    await saveCurrentScrollPosition()

    // 切换tab
    setActiveTab(tab)

    // 恢复新tab的滚动位置
    setTimeout(() => {
      restoreScrollPosition(tab)
    }, 100) // 延迟确保tab内容已渲染
  }

  // 加载律师基本信息
  const loadLawyerInfo = async (id: number) => {
    try {
      const response = await lawyerApi.getLawyerDetailInfo(id)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setLawyerInfo({ ...response.data.lawyerInfo, figurePhotoUrl: parseImageUrl(response.data.lawyerInfo.figurePhotoUrl) })
      } else {
        console.error('获取律师基本信息失败:', response)
      }
    } catch (error) {
      console.error('加载律师基本信息失败:', error)
    }
  }

  // 加载律师统计数据
  const loadLawyerCountData = async (id: number) => {
    try {
      const response = await lawyerApi.getLawyerCountData(id)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setLawyerCountData(response.data.lawyerCountData)
      } else {
        console.error('获取律师统计数据失败:', response)
      }
    } catch (error) {
      console.error('加载律师统计数据失败:', error)
    }
  }

  // 加载律师内容统计
  const loadLawyerContentNum = async (id: number) => {
    try {
      const response = await articleApi.getLawyerArticleCount(id)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setContentNum(response.data)
      } else {
        console.error('获取律师内容统计失败:', response)
      }
    } catch (error) {
      console.error('加载律师内容统计失败:', error)
    }
  }

  // 加载关注状态
  const loadFollowStatus = async (id: number) => {
    if (!cachedUserId) {
      return
    }
    try {
      const response = await commonApi.checkUserBusinessStatus({
        bizId: id,
        userId: cachedUserId,
        bizType: ['follow']
      })
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setIsFollowed(response.data.follow)
      } else {
        console.error('获取关注状态失败:', response)
        // 关注状态失败时设置为未关注
        setIsFollowed(false)
      }
    } catch (error) {
      console.error('加载关注状态失败:', error)
      // 关注状态失败时设置为未关注
      setIsFollowed(false)
    }
  }

  // 加载律师详情（统一调用）
  const loadLawyerDetail = async (id: number) => {
    setLoading(true)
    // 独立调用各个接口，互不影响
    // 使用 Promise.all 但每个函数内部已经处理了错误，不会影响其他接口
    try {
      await Promise.all([
        loadLawyerInfo(id),
        loadLawyerCountData(id),
        loadLawyerContentNum(id),
        loadFollowStatus(id)
      ])
    } catch (error) {
      // 这里不应该进入，因为每个函数都已经处理了自己的错误
      console.error('加载律师详情时出现未预期的错误:', error)
    }
    setLoading(false)
  }
  useEffect(() => {
    lawyerId && loadLawyerDetail(Number(lawyerId))
  }, [lawyerId])

  const navLeftSolt = (value: LawyerAPI.LawyerInfo | null) => {
    return (
      <View className='nav-left'>
        <Image
          className='nav-left__icon'
          src={leftButtonIcon}
          mode='aspectFit'
          onClick={() => navigateBack()}
        />
        <Text className='nav-left__text'>{`${value?.name} 电子名片` || '律师详情'}</Text>
      </View>
    )
  }
  const { navBarHeight } = useNavBarHeight()
  return (
    <View className='lawyer-detail'>
      <View className='lawyer-detail-nav-bg' style={`clip-path: inset(0 0 calc(100% - ${navBarHeight}px - 1px) 0); background-image: url(${LawyerBg})`} />
      <PageLayout
        backgroundColor='#F8F8F8'
        navBarBgColor='transparent'
        showNavBorder={false}
        showBack={false}
        navBarLeft={navLeftSolt(lawyerInfo)}
      >
        <PageContent padded='b'
          className='lawyer-detail-bg'
          style={`background-image: url(${LawyerBg});
          background-position: 0 -${navBarHeight}px;
          background-size: 100% calc(var(--taro-nav-height, 420rpx) + 280rpx);`}
        >
          {lawyerInfo && (
            <View className='lawyer-detail__content'>
              {/* 律师头像和基本信息 */}
              <LawyerAvatar
                lawyerInfo={lawyerInfo}
                isFollowed={isFollowed}
                onFollowToggle={toggleFollow}
                className='lawyer-detail__header'
              />

              {/* Tab导航 */}
              <LawyerTabs
                activeTab={activeTab}
                onTabChange={handleTabChange}
              />

              {/* Tab内容 */}
              {activeTab === 'info' && (
                <View className='lawyer-detail__tab-content'>
                  <LawyerInfoTab
                    lawyerInfo={lawyerInfo}
                    lawyerCountData={lawyerCountData}
                    isProfileExpanded={isProfileExpanded}
                    onProfileToggle={toggleProfile}
                    loading={loading}
                  />
                </View>
              )}

              {activeTab === 'dynamics' && (
                <View className='lawyer-detail__tab-content'>
                  <LawyerDynamicsTab
                    lawyerId={Number(lawyerId)}
                    loading={loading}
                  />
                </View>
              )}

              {activeTab === 'cases' && (
                <View className='lawyer-detail__tab-content'>
                  <LawyerCasesTab
                    lawyerId={Number(lawyerId)}
                    loading={loading}
                  />
                </View>
              )}

              {activeTab === 'articles' && (
                <View className='lawyer-detail__tab-content'>
                  <LawyerArticleTab
                    lawyerId={Number(lawyerId)}
                    contentNum={contentNum}
                    loading={loading}
                  />
                </View>
              )}
            </View>
          )}
        </PageContent>
      </PageLayout>
    </View>
  )
}

export default LawyerDetail
