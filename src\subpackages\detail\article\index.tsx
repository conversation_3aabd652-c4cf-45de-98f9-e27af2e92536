/**
 * 文章详情页面
 */
import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { View, Text, Image } from '@tarojs/components'
import BookMark from '@/assets/images/common-icon/book_mark_color.png'
import Taro, { useRouter } from '@tarojs/taro'
import { articleApi, commonApi } from '@/apis'
import UserCacheManager from '@/utils/cache/userCache'
import { STATUS_CODE } from '@/utils/request/config'
import { formatTime, parseImageUrls } from '@/utils'
import PageLayout, { PageContent } from '@/components/PageLayout'
import clockIcon from '@/assets/images/common-icon/clock.png'
import eyeIcon from '@/assets/images/common-icon/eye.png'
import starIcon from '@/assets/images/common-icon/star_fill.png'
import likeIcon from '@/assets/images/common-icon/like_outlone_g.png'
import likeActiveIcon from '@/assets/images/common-icon/like_active.png'
import likeOutlineIcon from '@/assets/images/common-icon/like_outlone.png'
import starRedIcon from '@/assets/images/common-icon/star_red.png'
import starFillIcon from '@/assets/images/common-icon/star_outline.png'
import HorizontalArticleList from '@/components/HorizontalArticleList'
import shareIcon from '@/assets/images/common-icon/share.png'
import { FloatingMenu } from '@/components'
import './index.scss'

const ArticleDetail: React.FC = () => {
  const router = useRouter()
  const { id } = router.params
  const userId = UserCacheManager.getUserId()
  const [filterParams, setFilterParams] = useState<ArticleAPI.GetArticleListRequest>({})
  const [articleInfo, setArticleInfo] = useState<ArticleAPI.ArticleDetailInfo | null>(null)
  const [isMenuExpanded, setIsMenuExpanded] = useState(false)
  const [isMenuAnimating, setIsMenuAnimating] = useState(false)
  const [isCollected, setIsCollected] = useState(false)
  const [isLiked, setIsLiked] = useState(false)



  // 检查收藏和点赞的状态
  const checkCollectAndLikeStatus = async (articleId: number) => {
    if (!userId) {
      return
    }
    try {
      const response = await commonApi.checkUserBusinessStatus({
        bizId: articleId,
        userId,
        bizType: ['collect', 'nice']
      })
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setIsCollected(response.data.collect)
        setIsLiked(response.data.nice)
      }
      else {
        console.error('获取收藏和点赞状态失败:', response)
      }
    }
    catch (error) {
      console.error('检查收藏和点赞状态失败:', error)
    }
  }


  // 处理收藏操作
  const handleCollect = useCallback(async () => {
    if (!articleInfo) return

    try {
      if (isCollected) {
        // 取消收藏
        const response = await articleApi.cancelCollectArticle(articleInfo.id)
        if (response.code === STATUS_CODE.SUCCESS) {
          setIsCollected(false)
          Taro.showToast({
            title: '已取消收藏',
            icon: 'success',
            duration: 1500
          })
        }
      } else {
        // 收藏文章
        const response = await articleApi.collectArticle(articleInfo.id)
        if (response.code === STATUS_CODE.SUCCESS) {
          setIsCollected(true)
          Taro.showToast({
            title: '收藏成功',
            icon: 'success',
            duration: 1500
          })
        }
      }
    } catch (error) {
      console.error('收藏操作失败:', error)
      Taro.showToast({
        title: isCollected ? '取消收藏失败' : '收藏失败',
        icon: 'none',
        duration: 2000
      })
    }
  }, [articleInfo, isCollected])

  // 处理点赞操作
  const handleLike = useCallback(async () => {
    if (!articleInfo) return

    try {
      if (isLiked) {
        // 取消点赞
        const response = await articleApi.cancelLikeArticle(articleInfo.id)
        if (response.code === STATUS_CODE.SUCCESS) {
          setIsLiked(false)
          Taro.showToast({
            title: '已取消点赞',
            icon: 'success',
            duration: 1500
          })
        }
      } else {
        // 点赞文章
        const response = await articleApi.likeArticle(articleInfo.id)
        if (response.code === STATUS_CODE.SUCCESS) {
          setIsLiked(true)
          Taro.showToast({
            title: '点赞成功',
            icon: 'success',
            duration: 1500
          })
        }
      }
    } catch (error) {
      console.error('点赞操作失败:', error)
      Taro.showToast({
        title: isLiked ? '取消点赞失败' : '点赞失败',
        icon: 'none',
        duration: 2000
      })
    }
  }, [articleInfo, isLiked])

  // 加载文章详情
  const loadArticleDetail = async () => {
    try {
      const response = await articleApi.getArticleDetail(Number(id))
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setArticleInfo(response.data.detail)
        setFilterParams(prev => ({
          ...prev,
          lawyerId: response.data.lawyerInfo.id
        }))
        // 检查收藏和点赞状态
        checkCollectAndLikeStatus(response.data.detail.id)
      }
    } catch (error) {
      console.error('加载文章详情失败:', error)
    } finally {
    }
  }

  const onClickMoreArticle = () => {
    Taro.navigateTo({
      url: '/pages/article/index'
    })
  }

  // 切换浮动菜单
  const toggleMenu = () => {
    if (isMenuExpanded) {
      // 收起菜单：先设置动画状态，然后延迟隐藏
      setIsMenuAnimating(true)
      // 立即触发收起动画，300ms后完全隐藏
      setTimeout(() => {
        setIsMenuExpanded(false)
        setIsMenuAnimating(false)
      }, 300)
    } else {
      // 展开菜单：直接显示并播放展开动画
      setIsMenuExpanded(true)
      setIsMenuAnimating(false)
    }
  }

  const handleShare = () => {
    setIsMenuAnimating(true)
    setTimeout(() => {
      setIsMenuExpanded(false)
      setIsMenuAnimating(false)
    }, 300)
  }
  const imgUrls = useMemo(() => {
    if (!articleInfo?.imagesContexts) return []
    return parseImageUrls(articleInfo.imagesContexts)
  }, [articleInfo])

  useEffect(() => {
    if (id) {
      loadArticleDetail()
    }
  }, [id])

  return (
    <PageLayout
      title='法律文章详情'
      backgroundColor='#F8F8F8'
      navBarTextColor='#BD8A4F'
      showBack
      showFloatingMenu={false}
    >
      <PageContent padded='b'>
        <View className='article-detail'>
          {articleInfo && (
            <View className='article-detail__content'>
              {/* 文章信息卡片 */}
              <View className='article-info-card'>
                {/* 标题 */}
                <Text className='article-info-card__title'>{articleInfo.title}</Text>

                {/* 信息模块 */}
                <View className='article-info-card__meta'>
                  <Text className='article-info-card__category'>{articleInfo.categoryName}</Text>
                  <View className='article-info-card__time'>
                    <Image
                      className='article-info-card__time-icon'
                      src={clockIcon}
                      mode='aspectFit'
                    />
                    <Text className='article-info-card__time-text'>
                      {formatTime(articleInfo.createdAt)}
                    </Text>
                  </View>
                  <View className='article-info-card__view'>
                    <Image
                      className='article-info-card__view-icon'
                      src={eyeIcon}
                      mode='aspectFit'
                    />
                    <Text className='article-info-card__view-text'>{articleInfo.viewCount}</Text>
                  </View>
                  <View className='article-info-card__favorite'>
                    <Image
                      className='article-info-card__favorite-icon'
                      src={starIcon}
                      mode='aspectFit'
                    />
                    <Text className='article-info-card__favorite-text'>{articleInfo.favoriteCount}</Text>
                  </View>
                  <View className='article-info-card__like'>
                    <Image
                      className='article-info-card__like-icon'
                      src={likeIcon}
                      mode='aspectFit'
                    />
                    <Text className='article-info-card__like-text'>{articleInfo.likeCount}</Text>
                  </View>
                </View>
              </View>
              {/* 文章内容 */}
              <View className='article-content'>
                <Text className='article-content__title'>文章详情</Text>
                <Text className='article-content__text'>
                  {articleInfo.content || '暂无详细内容'}
                </Text>
                {imgUrls.length ?
                  <View className='article-content__images'>
                    {imgUrls.map(item =>
                      <Image
                        key={item}
                        className='article-content__image'
                        src={item}
                        mode='aspectFill'
                        onClick={() => {
                          // 预览图片
                          Taro.previewImage({
                            current: item,
                            urls: imgUrls
                          })
                        }}
                      />
                    )}
                  </View> : null}
              </View>
              <View className='article-detail-list'>
                <View className='article-detail-list__header'>
                  <View className='article-detail-list__title'>
                    <Image className='article-detail-list__title-icon' src={BookMark} mode='aspectFit' />
                    <Text className='article-detail-list__title-text'>相关文章</Text>
                  </View>
                  <View className='article-detail-list__more' onClick={onClickMoreArticle}>
                    <Text>更多</Text>
                  </View>
                </View>
                <HorizontalArticleList filterParams={filterParams} />
              </View>
            </View>
          )}
        </View>
        {/* 浮动操作菜单 */}
        <FloatingMenu
          isExpanded={isMenuExpanded}
          isAnimating={isMenuAnimating}
          onToggle={toggleMenu}
          menuItems={[
            {
              key: 'collect',
              label: isCollected ? '已收藏' : '收藏',
              icon: isCollected ? starRedIcon : starFillIcon,
              onClick: handleCollect
            },
            {
              key: 'like',
              label: isLiked ? '已点赞' : '点赞',
              icon: isLiked ? likeActiveIcon : likeOutlineIcon,
              onClick: handleLike
            },
            {
              key: 'share',
              label: '分享',
              icon: shareIcon,
              onClick: handleShare
            }
          ]}
        />
      </PageContent>
    </PageLayout>
  )
}

export default ArticleDetail
