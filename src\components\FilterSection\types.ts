/**
 * FilterSection 组件类型定义
 */

// 选项类型
export interface FilterOption {
  id: number
  name: string
}

// 组件属性
export interface FilterSectionProps {
  // 标题
  title: string
  // 选项列表
  options: FilterOption[]
  // 当前选中的值
  selectedValue?: number
  // 是否折叠
  collapsed: boolean
  // 折叠状态切换回调
  onToggleCollapse: () => void
  // 选项选择回调
  onSelect: (value?: number | string) => void
  // 自定义样式类名
  className?: string
}
