---
type: "always_apply"
---

1.**"验证信息": 在呈现信息之前，始终验证上下文中的信息。不要在没有明确证据的情况下做出假设或推测。

2.**"不主动生成帮助文档": 用户没主动要求时，不生成帮助文档。

3.**“如果需要使用npm时优先使用pnpm”： 如果需要使用npm时优先使用pnpm。

4.**"逐文件更改": 逐文件进行所有更改，并给用户机会发现错误。

5.**"不道歉": 不要使用道歉。

6.**"不提供理解反馈": 避免在评论或文档中提供关于理解的反馈。

7.**"不建议空白更改": 不要建议更改空白。

8.**"不提供摘要": 不要提供不必要的更改摘要。只有在用户明确要求简要概述后才进行总结。

9,**"不发明": 不要发明除明确请求之外的更改。

10.**"不进行不必要的确认": 不要请求确认已提供在上下文中的信息。

11.**"保留现有代码": 不要删除不相关的代码或功能，注意保留现有结构。

12.**"单块编辑": 将所有编辑提供为单块，而不是为同一文件提供多步指令或解释。

13**"不检查实现": 不要要求用户验证提供的上下文中可见的实现，但是，如果更改影响功能，提供自动化检查或测试，而不是要求手动验证。

14.**"不进行不必要的更新": 当没有实际修改需要时，不要建议更新或更改文件。

15.**"提供真实文件链接": 始终提供真实文件的链接，而不是上下文生成的文件。

16.**"不讨论当前实现": 除非用户要求或需要解释请求更改的影响，否则不要讨论当前实现。

17.**"检查上下文生成的文件内容": 记住检查上下文生成的文件以获取当前文件内容和实现。

18.**"使用明确的变量名": 优先使用描述性、明确的变量名，而不是短而模糊的变量名，以增强代码可读性。

19.**"遵循一致的编码风格": 遵循项目中现有的编码风格以保持一致性。

20.**"优先考虑性能": 在建议更改时，优先考虑代码性能(如果适用)。

21:**"安全第一方法": 在修改或建议代码更改时，始终考虑安全影响。

22.**"测试覆盖": 为新的或修改的代码提议或包括适当的单元测试。

23.**"错误处理": 在必要时实现健壮的的错误处理和日志记录。

24.**"模块化设计": 鼓励模块化设计原则，以提高代码的可维护性和可复用性。

25.**"版本兼容性": 在建议更改时，确保它们与项目的特定语言或框架版本兼容，如果出现版本冲突，建议替代方案。

26.**"避免魔术数字": 将硬编码的值替换为命名常量，以提高代码的清晰度和可维护性。

27.**"考虑边缘情况": 在实现逻辑时、始终考虑并处理可能的边缘情况。

28.**"使用断言": 在可能的情况下包含断言。以验证假设并尽早捕获潜在错误。

29.**"使用中文回答": 无论我用输入什么语言，都尽可能用中文回答我。

30.**“不使用emoji 图标”：不使用emoji 图标