.collection-article-manage {
  height: 100vh;
  display: flex;
  flex-direction: column;

  .collection-search-header {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    padding: 15rpx 30rpx;
    background: #fff;
    border-bottom: 2rpx solid #f0f0f0;

    .collection-search-box {
      flex: 1;
      display: flex;
      align-items: center;
      height: 60rpx;
      background: #f7f7f7;
      border-radius: 30rpx;
      padding: 0 24rpx;
      box-sizing: border-box;

      .collection-search-icon {
        width: 28rpx;
        height: 28rpx;
        margin-right: 16rpx;
        flex-shrink: 0;
      }

      .collection-search-input {
        flex: 1;
        height: 100%;
        font-size: 26rpx;
        color: #333;
        background: transparent;
        border: none;
        outline: none;

        &::placeholder {
          color: #999;
        }
      }
    }
  }

  .collection-article-list-container {
    flex: 1;
    overflow: hidden;
  }

  .collection-article-list {
    height: 100%;
    background: #f8f9fa;

    .collection-article-card {
      background: #fff;
      margin: 20rpx 30rpx;
      border-radius: 16rpx;
      padding: 30rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      }

      .collection-article-header {
        margin-bottom: 20rpx;

        .collection-article-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          line-height: 1.4;
          min-height: 89.6rpx; /* 32rpx * 1.4 * 2 = 两行的最小高度 */
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          word-break: break-all;
        }
      }

      .collection-article-footer {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .collection-article-time {
          font-size: 24rpx;
          color: #999;
        }
      }
    }

    .collection-loading-more,
    .collection-no-more,
    .collection-empty-state {
      text-align: center;
      padding: 60rpx 30rpx;
      color: #999;
      font-size: 28rpx;
    }

    .collection-empty-state {
      padding: 120rpx 30rpx;
    }
  }
}
