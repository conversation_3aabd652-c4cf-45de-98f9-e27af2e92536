/**
 * Banner 轮播组件样式
 */

.banner {
  position: relative;
  width: 750rpx;
  height: 460rpx;
  overflow: hidden;
  border-radius: 0;

  // Swiper 容器
  &__swiper {
    width: 100%;
    height: 100%;
  }

  // 轮播项
  &__item {
    width: 100%;
    height: 100%;
  }

  // 内容容器
  &__content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;

    // 可点击状态
    &--clickable {
      &:active {
        opacity: 0.9;
        transform: scale(0.98);
        transition: all 0.1s ease;
      }
    }
  }

  // 图片样式
  &__image {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
    border-radius: inherit;
  }

  // 文字覆盖层
  &__overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.6) 0%,
      rgba(0, 0, 0, 0.3) 50%,
      transparent 100%
    );
    padding: 32rpx;
    color: #ffffff;
  }

  // 标题样式
  &__title {
    font-size: 32rpx;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 8rpx;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
    
    // 文字超长处理
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // 描述样式
  &__description {
    font-size: 24rpx;
    line-height: 1.5;
    opacity: 0.9;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
    
    // 最多显示两行
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }

  // 自定义指示器
  &__indicators {
    position: absolute;
    bottom: 24rpx;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 12rpx;
    z-index: 10;
  }

  // 指示器点
  &__indicator {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;

    // 激活状态
    &--active {
      width: 32rpx;
      border-radius: 8rpx;
      background-color: #ffffff;
    }
  }
}

// 圆角变体
.banner--rounded {
  border-radius: 16rpx;

  .banner__image {
    border-radius: 16rpx;
  }
}

// 卡片变体
.banner--card {
  margin: 24rpx;
  width: 702rpx; // 750 - 24*2
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);

  .banner__image {
    border-radius: 16rpx;
  }
}

// 小尺寸变体
.banner--small {
  height: 300rpx;
}

// 大尺寸变体
.banner--large {
  height: 600rpx;
}

// 无指示器变体
.banner--no-indicators {
  .banner__indicators {
    display: none;
  }
}

// 深色主题
.banner--dark {
  .banner__overlay {
    background: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.8) 0%,
      rgba(0, 0, 0, 0.4) 50%,
      transparent 100%
    );
  }

  .banner__indicator {
    background-color: rgba(255, 255, 255, 0.3);

    &--active {
      background-color: rgba(255, 255, 255, 0.9);
    }
  }
}

// 加载状态
.banner--loading {
  .banner__content {
    background-color: #f5f5f5;
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #e0e0e0;
      border-top-color: #BD8A4F;
      border-radius: 50%;
      animation: banner-loading 1s linear infinite;
    }
  }
}

// 加载动画
@keyframes banner-loading {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}


// 无障碍访问
.banner__content[aria-label] {
  &::after {
    content: attr(aria-label);
    position: absolute;
    left: -9999rpx;
    width: 1rpx;
    height: 1rpx;
    overflow: hidden;
  }
}
