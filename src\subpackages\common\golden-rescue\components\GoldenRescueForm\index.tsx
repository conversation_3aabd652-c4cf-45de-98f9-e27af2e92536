/**
 * 黄金救援表单组件
 */
import React from 'react'
import { View, Text, Input, Textarea, Button } from '@tarojs/components'
import { FormField, SelectField, AreaSelectField, PhoneCodeInput } from '../'
import type { GoldenRescueFormProps } from '../../types'
import './index.scss'

const GoldenRescueForm: React.FC<GoldenRescueFormProps> = ({
  formData,
  caseCategories,
  caseStages,
  // lawyerList,
  categoriesLoading,
  stagesLoading,
  // lawyersLoading,
  submitting,
  onFormDataChange,
  onAreaChange,
  onSubmit,
  pageType
}) => {
  // 处理输入框变化
  const handleInputChange = (field: keyof UserAPI.CreateLawEntrustOrderRequest, value: string) => {
    onFormDataChange({ [field]: value })
  }

  // 处理数字输入框变化
  const handleNumberInputChange = (field: keyof UserAPI.CreateLawEntrustOrderRequest, value: string) => {
    const numValue = value ? Number(value) : undefined
    onFormDataChange({ [field]: numValue })
  }

  // 处理案件类型选择
  const handleCaseTypeChange = (value: number) => {
    onFormDataChange({ caseTypeId: value })
  }

  // 处理案件阶段选择
  const handleCaseStageChange = (value: number) => {
    onFormDataChange({ caseStageId: value })
  }

  // 处理律师选择（优配律师场景使用）
  // const handleLawyerChange = (value: number) => {
  //   onFormDataChange({ lawyerId: value })
  // }

  // 处理地区选择
  const handleAreaChange = (province: string, city: string, district: string) => {
    // 调用父组件的地区变化处理（会更新表单数据并刷新律师列表）
    onAreaChange(province, city, district)
  }

  // 处理手机号变化
  const handlePhoneChange = (value: string) => {
    onFormDataChange({ mobile: value })
  }

  // 处理验证码变化
  const handleCodeChange = (value: string) => {
    onFormDataChange({ code: value })
  }

  return (
    <View className='golden-rescue-form'>
      {/* 服务介绍 */}
      <View className='intro-section'>
        {pageType === '1' ? (
          // 黄金救援介绍
          <View className='intro-content'>
            <Text className='intro-desc'>
              胜张仪平台的&ldquo;黄金救援&ldquo;服务是在刑案最初阶段，为解决法律服务需求者最快速找到最适合办理自己案件的专业律师而设置的。嫌疑人自被刑拘之日始的37天内为黄金救援期；黄金救援期即有律师介入，对嫌疑人十分关键，此时律师的主要作用：
            </Text>
            <View className='intro-points'>
              <View className='point-item'>
                <Text className='point-title'>首先，办理律师会见。</Text>
                <Text className='point-desc'>让嫌疑人第一时间获得相关刑事法律法规知识，为嫌疑人正确认识涉案事实，在被提审时精准还原案件实情，为案件得到公正处理打下基础；</Text>
              </View>
              <View className='point-item'>
                <Text className='point-title'>其次，办理取保候审。</Text>
                <Text className='point-desc'>经济犯罪、职务犯罪的刑案，大多数情况下，可以办理取保候审，若能成功办理取保候审，可以让嫌疑人重获人身自由，避免生活、工作受到重大负面影响；</Text>
              </View>
              <View className='point-item'>
                <Text className='point-title'>其三，固定有利证据。</Text>
                <Text className='point-desc'>此期间是公安搜集嫌疑人核心有罪证据并报检察院批捕的时期，也是律师搜集和固定对嫌疑人有利证据的关键期。某些案件，通过律师的证据搜集，及时将能证明嫌疑人无罪或者罪轻的证据与公安侦查人员良性沟通，可促使案件向着不批捕、不起诉的方向发展。</Text>
              </View>
            </View>
            <Text className='intro-note'>平台在选择律师时，匹配的方式与优配律师一样</Text>
          </View>
        ) : (
          // 优配律师介绍
          <View className='intro-content'>
            <Text className='intro-desc'>
              胜张仪平台的&ldquo;优配律师&ldquo;功能就是为了解决法律服务需求者如何找到最适合办理自己案件的专业律师而设置的。优配律师的底层逻辑是，平台所有的律师，承接案件必须以曾经办理过的案件的绩效为依据，在法律服务需求者将自己的案件基本情况提交给平台后，平台大数据库将对办理过同类案件而且绩效优秀的律师进行比对，电脑自动挑选出最符合办理法律服务需求者案件的三位律师并推荐给他，由他自己再亲自面谈确定最终人选。这种以办案实效为依据的律师推荐方式，确保推荐律师为最优匹配律师。
            </Text>
          </View>
        )}
      </View>

      {/* 案件信息 */}
      <View className='form-section'>
        <Text className='section-title'>案件信息</Text>

        <SelectField
          label='案件类型'
          options={caseCategories}
          value={formData.caseTypeId}
          onChange={handleCaseTypeChange}
          placeholder='请选择案件类型'
          required
          loading={categoriesLoading}
        />

        <SelectField
          label='案件阶段'
          options={caseStages}
          value={formData.caseStageId}
          onChange={handleCaseStageChange}
          placeholder='请选择案件阶段'
          required
          loading={stagesLoading}
        />

        <FormField label='办案机关' required>
          <Input
            className='form-input'
            placeholder='请输入办案机关'
            value={formData.handlingAgency}
            onInput={(e) => handleInputChange('handlingAgency', e.detail.value)}
          />
        </FormField>

        <AreaSelectField
          label='案件地区'
          province={formData.province}
          city={formData.city}
          district={formData.district}
          onChange={handleAreaChange}
          required
          placeholder='请选择案件发生地区'
        />

        {/* 优配律师场景显示律师选择 */}
        {/* {pageType === '2' && (
          <SelectField
            label='选择律师'
            value={formData.lawyerId}
            options={lawyerList}
            loading={lawyersLoading}
            onChange={handleLawyerChange}
            required
            placeholder={
              !formData.province || !formData.city || !formData.district
                ? '请先选择案件地区'
                : lawyersLoading
                ? '正在加载律师列表...'
                : lawyerList.length === 0
                ? '该地区暂无可选律师'
                : '请选择律师'
            }
            disabled={!formData.province || !formData.city || !formData.district || lawyersLoading}
          />
        )} */}

        <FormField label='案件涉及金额'>
          <Input
            className='form-input'
            placeholder='请输入案件涉及金额（元）'
            type='number'
            value={formData.amountInvolvedOfCase?.toString() || ''}
            onInput={(e) => handleNumberInputChange('amountInvolvedOfCase', e.detail.value)}
          />
        </FormField>
      </View>

      {/* 委托人信息 */}
      <View className='form-section'>
        <Text className='section-title'>委托人信息</Text>

        <FormField label='委托人姓名' required>
          <Input
            className='form-input'
            placeholder='请输入委托人姓名'
            value={formData.clientName}
            onInput={(e) => handleInputChange('clientName', e.detail.value)}
          />
        </FormField>

        <PhoneCodeInput
          phoneValue={formData.mobile}
          codeValue={formData.code}
          onPhoneChange={handlePhoneChange}
          onCodeChange={handleCodeChange}
        />
      </View>

      {/* 律师要求 */}
      <View className='form-section'>
        <Text className='section-title'>
          {pageType === '2' ? '律师要求（重要）' : '律师要求'}
        </Text>

        <FormField label='对律师的要求' required={pageType === '2'}>
          <Textarea
            className='form-textarea'
            placeholder={
              pageType === '2'
                ? '请详细描述您对律师的具体要求，包括专业领域、经验等（必填）'
                : '请描述您对律师的具体要求（选填）'
            }
            value={formData.lawyerRequirements || ''}
            onInput={(e) => handleInputChange('lawyerRequirements', e.detail.value)}
            maxlength={500}
            showConfirmBar={false}
          />
        </FormField>
      </View>

      {/* 提交按钮 */}
      <View className='submit-section'>
        <Button
          className='submit-btn'
          onClick={onSubmit}
          loading={submitting}
          disabled={submitting}
        >
          {submitting ? '提交中...' : (pageType === '2' ? '提交优配申请' : '提交救援申请')}
        </Button>
      </View>
    </View>
  )
}

export default GoldenRescueForm
