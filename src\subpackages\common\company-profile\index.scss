/**
 * 公司简介页面样式
 */

.company-profile {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

  &__container {
    padding: 20rpx;
  }

  // 标题区域
  .title-section {
    text-align: center;
    background: linear-gradient(135deg, #BD8A4F 0%, #D4A574 100%);
    color: #ffffff;
    margin-bottom: 30rpx;
    padding: 50rpx 30rpx;
    border-radius: 20rpx;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      pointer-events: none;
    }

    .title-text {
      font-size: 36rpx;
      font-weight: 700;
      margin-bottom: 16rpx;
      display: block;
      position: relative;
      z-index: 1;
    }

    .subtitle-text {
      font-size: 24rpx;
      opacity: 0.9;
      display: block;
      position: relative;
      z-index: 1;
    }
  }

  // 平台简介
  .intro-section {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    border-left: 6rpx solid #BD8A4F;

    .intro-text {
      font-size: 28rpx;
      color: #2c3e50;
      line-height: 2.0;
      text-align: justify;
    }
  }

  // 章节标题
  .section-title {
    font-size: 32rpx;
    font-weight: 700;
    color: #BD8A4F;
    margin-bottom: 30rpx;
    display: block;
    position: relative;
    padding-left: 20rpx;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8rpx;
      height: 32rpx;
      background: linear-gradient(135deg, #BD8A4F, #D4A574);
      border-radius: 4rpx;
    }
  }

  // 特色功能区域
  .feature-section {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .feature-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 24rpx;
    }

    .feature-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: 30rpx 20rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4rpx);
        box-shadow: 0 8rpx 24rpx rgba(189, 138, 79, 0.15);
      }
    }

    .feature-icon {
      width: 60rpx;
      height: 60rpx;
      background: linear-gradient(135deg, #BD8A4F, #D4A574);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16rpx;

      .feature-icon-text {
        font-size: 24rpx;
        font-weight: 700;
        color: #ffffff;
      }
    }

    .feature-title {
      font-size: 26rpx;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 12rpx;
    }

    .feature-desc {
      font-size: 22rpx;
      color: #666666;
      line-height: 1.6;
      text-align: left;
    }
  }

  // 服务区域
  .service-section {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .service-list {
      display: flex;
      flex-direction: column;
      gap: 24rpx;
    }

    .service-item {
      background: #f8f9fa;
      border-radius: 12rpx;
      padding: 30rpx;
      border-left: 4rpx solid #BD8A4F;
      transition: all 0.3s ease;

      &:hover {
        transform: translateX(8rpx);
        box-shadow: 0 6rpx 20rpx rgba(189, 138, 79, 0.15);
      }
    }

    .service-header {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;
    }

    .service-badge {
      background: linear-gradient(135deg, #BD8A4F, #D4A574);
      color: #ffffff;
      font-size: 20rpx;
      font-weight: 600;
      padding: 6rpx 12rpx;
      border-radius: 20rpx;
      margin-right: 16rpx;
    }

    .service-title {
      font-size: 26rpx;
      font-weight: 600;
      color: #2c3e50;
    }

    .service-desc {
      font-size: 24rpx;
      color: #666666;
      line-height: 1.8;
      text-align: justify;
      margin-bottom: 16rpx;
    }

    .service-details {
      margin: 16rpx 0;
      padding-left: 12rpx;
      display: flex;
      flex-direction: column;
    }

    .detail-item {
      font-size: 22rpx;
      color: #555555;
      line-height: 1.8;
      margin-bottom: 12rpx;
      text-align: justify;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .service-summary {
      font-size: 24rpx;
      color: #BD8A4F;
      font-weight: 600;
      margin-top: 16rpx;
      text-align: center;
      font-style: italic;
    }
  }

  // 标语区域
  .slogan-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx;
    text-align: center;
    margin: 30rpx 0;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(189, 138, 79, 0.1) 0%, transparent 70%);
      pointer-events: none;
    }

    .slogan-text {
      font-size: 28rpx;
      font-weight: 600;
      position: relative;
      z-index: 1;
    }

    .slogan-tags {
      display: grid;
      // 2*2
      grid-template-columns: repeat(2, 1fr);
      justify-content: center;
      gap: 12rpx;
      position: relative;
      z-index: 1;
      margin-top: 24rpx;
    }

    .slogan-tag {
      background: rgba(189, 138, 79, 0.2);
      color: #BD8A4F;
      font-size: 20rpx;
      font-weight: 500;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      border: 1rpx solid rgba(189, 138, 79, 0.3);
    }
  }

  // 优势区域
  .advantage-section {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .advantage-list {
      display: flex;
      flex-direction: column;
      gap: 20rpx;
    }

    .advantage-item {
      display: flex;
      align-items: flex-start;
      padding: 24rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      transition: all 0.3s ease;

      &:hover {
        background: #f0f8ff;
        transform: translateY(-2rpx);
      }
    }

    .advantage-number {
      width: 48rpx;
      height: 48rpx;
      background: linear-gradient(135deg, #BD8A4F, #D4A574);
      color: #ffffff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20rpx;
      font-weight: 700;
      margin-right: 20rpx;
      flex-shrink: 0;
    }

    .advantage-content {
      flex: 1;
    }

    .advantage-title {
      font-size: 26rpx;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8rpx;
    }

    .advantage-desc {
      font-size: 22rpx;
      color: #666666;
      line-height: 1.6;
    }
  }

  // 内容区域（保留原有样式用于隐藏的长文本）
  .content-section {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 50rpx 40rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
    border: 1rpx solid #e0e0e0;
    position: relative;
    overflow: hidden;

    .content-text {
      font-size: 28rpx;
      color: #2c3e50;
      line-height: 2.2;
      text-align: justify;
      white-space: pre-line;
      word-break: break-word;
    }
  }

  // 合作模式区域
  .cooperation-section {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .cooperation-tabs {
      display: flex;
      gap: 16rpx;
      margin-bottom: 24rpx;
    }

    .cooperation-tab {
      flex: 1;
      background: #f8f9fa;
      border-radius: 12rpx;
      padding: 20rpx;
      text-align: center;
      transition: all 0.3s ease;

      &.active {
        background: linear-gradient(135deg, #BD8A4F, #D4A574);
        color: #ffffff;
      }

      .tab-title {
        font-size: 24rpx;
        font-weight: 600;
        display: block;
        margin-bottom: 4rpx;
      }

      .tab-subtitle {
        font-size: 20rpx;
        opacity: 0.8;
        display: block;
      }
    }

    .cooperation-content {
      background: #f8f9fa;
      border-radius: 12rpx;
      padding: 24rpx;
    }

    .cooperation-desc {
      font-size: 24rpx;
      color: #666666;
      line-height: 1.8;
      text-align: justify;
    }
  }

  // 章节区域
  .chapter-section {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .chapter-item {
      margin-bottom: 40rpx;
      padding-bottom: 40rpx;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
      }
    }

    .chapter-title {
      font-size: 30rpx;
      font-weight: 700;
      color: #BD8A4F;
      margin-bottom: 20rpx;
      line-height: 1.5;
    }

    .chapter-content {
      font-size: 26rpx;
      color: #2c3e50;
      line-height: 1.8;
      margin-bottom: 24rpx;
      text-align: justify;
    }

    .chapter-subsection {
      margin: 20rpx 0;
      padding: 20rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      border-left: 4rpx solid #BD8A4F;
    }

    .subsection-title {
      font-size: 24rpx;
      font-weight: 600;
      color: #BD8A4F;
      margin-bottom: 12rpx;
    }

    .subsection-content {
      font-size: 22rpx;
      color: #555555;
      line-height: 1.8;
      text-align: justify;
      display: flex;
      flex-direction: column;

      .content-item {
        display: block;
        margin-bottom: 12rpx;
        line-height: 1.8;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .benefit-list {
      margin: 20rpx 0;
    }

    .benefit-item {
      margin-bottom: 24rpx;
      padding: 24rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      border-left: 4rpx solid #BD8A4F;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .benefit-title {
      font-size: 24rpx;
      font-weight: 600;
      color: #BD8A4F;
      margin-bottom: 12rpx;
      line-height: 1.5;
    }

    .benefit-desc {
      font-size: 22rpx;
      color: #555555;
      line-height: 1.8;
      text-align: justify;
      display: flex;
      flex-direction: column;

      .desc-item {
        display: block;
        margin-bottom: 12rpx;
        line-height: 1.8;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .chapter-summary {
      display: inline-block;
      font-size: 24rpx;
      color: #BD8A4F;
      font-weight: 600;
      margin-top: 20rpx;
      padding: 20rpx;
      background: linear-gradient(135deg, rgba(189, 138, 79, 0.1), rgba(212, 165, 116, 0.1));
      border-radius: 12rpx;
      border: 1rpx solid rgba(189, 138, 79, 0.2);
      text-align: justify;
      line-height: 1.8;
      font-style: italic;
    }
  }

  // 章节标签样式
  .chapter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
    margin-top: 20rpx;
    padding-top: 20rpx;
    border-top: 1rpx solid #f0f0f0;
  }

  .chapter-tag {
    display: inline-block;
    padding: 8rpx 16rpx;
    background: rgba(189, 138, 79, 0.1);
    color: #BD8A4F;
    font-size: 20rpx;
    border-radius: 20rpx;
    border: 1rpx solid rgba(189, 138, 79, 0.3);
  }

  // 章节标签样式
  .chapter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
    margin-top: 20rpx;
    padding-top: 20rpx;
    border-top: 1rpx solid #f0f0f0;
  }

  .chapter-tag {
    display: inline-block;

    padding: 8rpx 16rpx;
    background: rgba(189, 138, 79, 0.1);
    color: #BD8A4F;
    font-size: 20rpx;
    border-radius: 20rpx;
    border: 1rpx solid rgba(189, 138, 79, 0.3);
  }

}

// 微信小程序全局字体优化
:global {
  .company-profile {
    font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;

    // 优化文本渲染
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}