/**
 * 律师页面样式
 */

// 导航栏左侧插槽样式
.nav-left-slot {
  display: flex;
  align-items: center;
  gap: 20rpx;
  height: 100%;

  &__back {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44rpx;
    height: 44rpx;

    &:active {
      opacity: 0.7;
    }
  }

  &__back-icon {
    width: 44rpx;
    height: 44rpx;
  }
}

.area-select-trigger{
  display: flex;
  align-items: center;
  gap: 10rpx;
  &__text{
    font-size: 28rpx;
    color: #333;
    white-space: nowrap;
    max-width: 140rpx; // 限制最大宽度，约5个文字（28rpx * 5 = 140rpx）
    overflow: hidden;
    text-overflow: ellipsis;
    &--placeholder{
      color: #999;
    }
  }
  &__arrow{
    width: 44rpx;
    height: 44rpx;
  }
}
.lawyer-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;

  .lawyer-list-container {
    padding: 0 30rpx;
  }
}