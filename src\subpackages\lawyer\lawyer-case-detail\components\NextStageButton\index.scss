/**
 * 进入下一阶段按钮组件样式
 */

.next-stage-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;

  &__info {
    margin-bottom: 20rpx;
  }

  &__stage-name {
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 8rpx;
  }

  &__progress {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #666666;
  }

  &__completed-badge {
    margin-left: 12rpx;
    padding: 4rpx 12rpx;
    background: #e8f5e8;
    color: #388e3c;
    border-radius: 12rpx;
    font-size: 20rpx;
    font-weight: 500;
  }

  &__btn {
    width: 100%;
    height: 88rpx;
    background: #BD8A4F;
    color: #ffffff;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:active {
      background: #a67a45;
      transform: scale(0.98);
    }

    &.disabled {
      background: #d0d0d0;
      color: #999999;
      transform: none;

      &:active {
        background: #d0d0d0;
        transform: none;
      }
    }

    &::after {
      border: none;
    }
  }
}
