.lawyer-login {
  padding: 40rpx 32rpx;
  min-height: 500rpx;

  .login-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;

    .title {
      display: block;
      font-size: 40rpx;
      font-weight: 600;
      color: #333333;
    }


    .header-switch {
      font-size: 24rpx;

      .switch-link {
        font-size: 24rpx;
        color: #BD8A4F;
        text-decoration: underline;

        &:active {
          color: #a67a42;
        }
      }
    }
  }

  .login-form-container {
    min-height: 500rpx;
    animation: fadeIn 0.3s ease-in-out;

    .form-section {
      margin-bottom: 40rpx;
    }

    .login-actions {
      display: flex;
      flex-direction: column;

      .login-btn {
        width: 100%;
        height: 88rpx;
        background: #BD8A4F;
        color: #ffffff;
        font-size: 32rpx;
        font-weight: 500;
        border-radius: 44rpx;
        border: none;
        margin-bottom: 20rpx;

        &:disabled {
          background: #cccccc;
        }

        &::after {
          border: none;
        }
      }

      .mode-switch {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 40rpx;

        .mode-switch-link {
          font-size: 24rpx;
          color: #BD8A4F;
          text-decoration: underline;

          &:active {
            color: #a67a42;
          }
        }
      }
    }
  }
}

// 内容切换动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}