/**
 * 案件阶段时间线组件
 */
import React from 'react'
import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { getCaseStageStatusText, getCaseStageStatusClass } from '@/constant'
import { formatTime } from '@/utils'
import EditIcon from '@/assets/images/common-icon/edit_b.png'
import './index.scss'

interface CaseStageTimelineProps {
  stageRecords: UserAPI.MemberCaseOrderStageRecordsInfo
  onEditProgress?: (progress: LawyerAPI.ProgressData) => void
  isLawyer?: boolean
}

const CaseStageTimeline: React.FC<CaseStageTimelineProps> = ({
  stageRecords,
  onEditProgress,
  isLawyer = false
}) => {

  // 渲染进度数据
  const renderProgressData = (progressData: UserAPI.MemberCaseOrderStageProgressData[]) => {
    if (!progressData || progressData.length === 0) {
      return null
    }
    // 转换imageUrls 为数组
    const processImageUrls = (val: string) => {
      if (!val) return []
      try {
        return JSON.parse(val) as string[]
      } catch (error) {
        console.error('解析图片URL失败:', error)
        return []
      }
    }

    return (
      <View className='timeline-progress'>
        {progressData.map((progress) => (
          <View key={progress.id} className={`timeline-progress__item ${progress.done === 2 ? 'done' : 'pending'}`}>
            <View className='timeline-progress__header'>
              <Text className='timeline-progress__name'>{progress.orderProgressName}</Text>
              <Text className={`timeline-progress__status ${progress.done === 2 ? 'done' : 'pending'}`}>
                {progress.done === 2 ? '已完成' : '进行中'}
              </Text>
              {progress.done === 1 && isLawyer ? (
                <Image
                  className='timeline-progress__edit-icon'
                  src={EditIcon}
                  mode='aspectFit'
                  onClick={() => onEditProgress?.(progress)}
                />
              ) : null}
            </View>

            {/* 跟进记录 */}
            {progress.stageFollowRecords && progress.stageFollowRecords.length > 0 ? (
              <View className='timeline-progress__records'>
                {progress.stageFollowRecords.map((record) => (
                  <View key={record.id} className='timeline-record'>
                    <View className='timeline-record__header'>
                      <Text className='timeline-record__time'>{formatTime(record.createdAt)}</Text>
                    </View>

                    {record.content && (
                      <Text className='timeline-record__content'>{record.content}</Text>
                    )}

                    {/* 图片展示 */}
                    {processImageUrls(record.imageUrls) && processImageUrls(record.imageUrls).length > 0 && (
                      <View className='timeline-record__images'>
                        {processImageUrls(record.imageUrls).map((imageUrl, imgIndex) => (
                          <Image
                            key={imgIndex}
                            className='timeline-record__image'
                            src={imageUrl}
                            mode='aspectFill'
                            onClick={() => {
                              // 预览图片
                              Taro.previewImage({
                                current: imageUrl,
                                urls: processImageUrls(record.imageUrls)
                              })
                            }}
                          />
                        ))}
                      </View>
                    )}
                  </View>
                ))}
              </View>
            ) : null}
          </View>
        ))}
      </View>
    )
  }

  return (
    <View className='case-stage-timeline'>
      <View className='case-stage-timeline__header'>
        <Text className='case-stage-timeline__title'>案件进度</Text>
      </View>

      <View className='case-stage-timeline__content'>
        <View className='timeline-stage'>
          <View className='timeline-stage__header'>
            <View className='timeline-stage__dot-container'>
              <View className={`timeline-stage__dot ${getCaseStageStatusClass(stageRecords.status)}`} />
              <View className='timeline-stage__line' />
            </View>
            <View className='timeline-stage__info'>
              <View className='timeline-stage__name-container'>
                <Text className='timeline-stage__name'>{stageRecords.stageName}</Text>
                <Text className={`timeline-stage__status ${getCaseStageStatusClass(stageRecords.status)}`}>
                  {getCaseStageStatusText(stageRecords.status)}
                </Text>
              </View>
              <Text className='timeline-stage__time'>{formatTime(stageRecords.createdAt)}</Text>
            </View>
          </View>

          {/* 进度数据 */}
          {renderProgressData(stageRecords.progressData)}
        </View>
      </View>
    </View>
  )
}

export default CaseStageTimeline
