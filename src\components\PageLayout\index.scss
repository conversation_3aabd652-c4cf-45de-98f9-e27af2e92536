/**
 * 页面布局组件样式
 */

.page-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;

  // 全屏模式
  &--fullscreen {
    .page-layout__content {
      padding-top: 0;
    }
  }

  // 安全区域适配
  &--safe-area {
    padding-bottom: env(safe-area-inset-bottom);
  }

  // 内容区域
  &__content {
    flex: 1;
    width: 100%;
    height: 0; // 重要：配合 flex: 1 使用，确保正确的高度计算

    // 内容容器
    position: relative;
    z-index: 1;

    // 确保 ScrollView 能正常滚动
    box-sizing: border-box;

    // 注意：padding-top 现在通过 JavaScript 动态设置
    // 以确保与实际导航栏高度匹配
  }

  // 下拉刷新包装器
  &__refresh-wrapper {
    min-height: 100%;
    width: 100%;
  }
}

// 页面内容通用样式
.page-content {
  // 移除默认 padding，让组件可以占满全屏
  // 需要内边距的组件可以自行添加

  // 带内边距的内容区域
  &--padded {
    padding: 32rpx;
  }

  // 小内边距
  &--padded-sm {
    padding: 16rpx;
  }

  // 大内边距
  &--padded-lg {
    padding: 48rpx;
  }

  // 只有水平内边距
  &--padded-x {
    padding-left: 32rpx;
    padding-right: 32rpx;
  }

  // 只有垂直内边距
  &--padded-y {
    padding-top: 32rpx;
    padding-bottom: 32rpx;
  }

  // 只有底部内边距
    &--padded-b {
    padding-bottom: 32rpx;
  }

  // 卡片容器
  &__card {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  // 列表容器
  &__list {
    background: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
    margin-bottom: 24rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  // 标题样式
  &__title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 24rpx;
    line-height: 1.4;
  }

  // 副标题样式
  &__subtitle {
    font-size: 28rpx;
    font-weight: 500;
    color: #666666;
    margin-bottom: 16rpx;
    line-height: 1.4;
  }

  // 文本样式
  &__text {
    font-size: 28rpx;
    color: #333333;
    line-height: 1.6;
    margin-bottom: 16rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  // 描述文本样式
  &__desc {
    font-size: 24rpx;
    color: #999999;
    line-height: 1.5;
  }
}

// 加载状态样式
.page-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  
  &__text {
    font-size: 28rpx;
    color: #999999;
    margin-left: 16rpx;
  }
}

// 空状态样式
.page-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  
  &__icon {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 32rpx;
    opacity: 0.6;
  }
  
  &__text {
    font-size: 28rpx;
    color: #999999;
    text-align: center;
    line-height: 1.5;
  }
}

// 错误状态样式
.page-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  
  &__icon {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 32rpx;
    opacity: 0.6;
  }
  
  &__text {
    font-size: 28rpx;
    color: #ff4757;
    text-align: center;
    line-height: 1.5;
    margin-bottom: 32rpx;
  }
  
  &__retry {
    background: #BD8A4F;
    color: #ffffff;
    border: none;
    border-radius: 8rpx;
    padding: 16rpx 32rpx;
    font-size: 28rpx;
    
    &:active {
      opacity: 0.8;
    }
  }
}

// 底部加载更多样式
.page-load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  
  &__text {
    font-size: 24rpx;
    color: #999999;
    margin-left: 16rpx;
  }
  
  &--no-more {
    .page-load-more__text {
      color: #cccccc;
    }
  }
}
