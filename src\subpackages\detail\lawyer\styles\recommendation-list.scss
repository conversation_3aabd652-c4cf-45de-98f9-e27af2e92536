/**
 * 推荐列表共用样式（动态和案例）
 */

// 推荐动态列表样式
.lawyer-detail-dynamics-list,
.lawyer-detail-case-list {
  display: flex;
  flex-direction: column;

  &__header {
    padding: 0 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__title {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    &-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 12rpx;
    }

    &-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #000000;
    }
  }

  &__more {
    text-align: right;
    margin-bottom: 24rpx;

    text {
      font-size: 28rpx;
      color: #828D99;
    }
  }
}
