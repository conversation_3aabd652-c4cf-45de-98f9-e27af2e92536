/**
 * 请求库通用类型声明
 *
 * 此文件包含所有模块共用的类型定义，避免重复声明
 * 基于 api.json 接口文档定义
 */

declare namespace CommonTypes {
  // 标准分页响应类型（基于API文档）
  interface PaginationResponse<T> {
    list: T[]
    page: number
    pageSize: number
    pageCount: number
    totalCount: number
  }

  // 简单列表响应类型
  interface ListResponse<T> {
    list: T[]
  }

  // 基础分页请求参数
  interface BasePaginationRequest {
    page?: number
    pageSize?: number
  }

  // 基础搜索请求参数
  interface BaseSearchRequest extends BasePaginationRequest {
    title?: string  // 标题模糊搜索
  }

  // 基础实体类型
  interface BaseEntity {
    id: number
    createdAt?: string
  }

  // 基础响应类型
  interface BaseApiResponse {
    code: number
    message: string
  }



  // 分类信息类型
  interface CategoryDto {
    id: number
    name: string
  }

  // 分类统计类型
  interface CategoryCountDto {
    id: number
    name: string
    num: number
  }

  // 律师统计数据类型
  interface LawyerCountDataDto {
    caseCount: number
    caseCategoryGroup: number
    topCategory: string[]
  }

  // 内容统计类型
  interface ContentNumDto {
    caseNum: number
    dynamicsNum: number
    articleNum: number
  }

  // 律师文章统计类型
  interface LawyerArticleCountDto {
    articleNum: number
    viewNum: number
    likeNum: number
  }
}
