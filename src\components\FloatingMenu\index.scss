/**
 * 浮动操作菜单组件样式
 */

.floating-menu {
  position: fixed;
  right: 25rpx;
  bottom: 65rpx;
  z-index: 999;
  transition: all 0.1s ease;

  // 菜单按钮
  &__button {
    width: 88rpx;
    height: 88rpx;
    background: #FFFFFF;
    box-shadow: 0rpx 2rpx 48rpx 0rpx rgba(0, 0, 0, 0.04);
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:active {
      transform: scale(1.1);
      box-shadow: 0rpx 4rpx 60rpx 0rpx rgba(0, 0, 0, 0.1);
    }
  }

  &__button-icon {
    width: 45rpx;
    height: 45rpx;
  }

  // 展开的菜单
  &__expanded {
    display: flex;
    align-items: center;
    background: #FFFFFF;
    box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
    border-radius: 44rpx;
    padding: 0 20rpx;
    gap: 10rpx;
    transform-origin: right center;
    animation: menuSlideIn 0.3s ease-out forwards;

    // 右侧展开（在左半屏时）
    &--right {
      transform-origin: left center;
      animation: menuSlideInRight 0.3s ease-out forwards;
    }
  }

  // 收起动画类
  &__collapsed {
    display: flex;
    align-items: center;
    background: #FFFFFF;
    box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
    border-radius: 44rpx;
    padding: 0 20rpx;
    gap: 10rpx;
    transform-origin: right center;
    animation: menuSlideOut 0.3s ease-in forwards;

    // 右侧收起（在左半屏时）
    &--right {
      transform-origin: left center;
      animation: menuSlideOutRight 0.3s ease-in forwards;
    }
  }

  &__item {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 15rpx 10rpx;


    &--close {
      padding: 9rpx;
    }
  }

  &__icon {
    width: 45rpx;
    height: 45rpx;
  }

  &__text {
    font-size: 24rpx;
    color: #666666;
    white-space: nowrap;
  }

  &__close {
    width: 70rpx;
    height: 70rpx;
  }
}

// 菜单展开动画（向左展开）
@keyframes menuSlideIn {
  0% {
    transform: scaleX(0);
    opacity: 0;
  }

  100% {
    transform: scaleX(1);
    opacity: 1;
  }
}

// 菜单展开动画（向右展开）
@keyframes menuSlideInRight {
  0% {
    transform: scaleX(0);
    opacity: 0;
  }

  100% {
    transform: scaleX(1);
    opacity: 1;
  }
}

// 菜单收起动画
@keyframes menuSlideOut {
  0% {
    transform: scaleX(1);
    opacity: 1;
  }

  100% {
    transform: scaleX(0);
    opacity: 0;
  }
}

// 右侧收起动画
@keyframes menuSlideOutRight {
  0% {
    transform: scaleX(1);
    opacity: 1;
  }

  100% {
    transform: scaleX(0);
    opacity: 0;
  }
}