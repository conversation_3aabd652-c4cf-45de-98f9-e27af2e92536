/**
 * 缓存工具类
 * 用于管理不经常变化的数据，如分类列表等
 */
import Taro from '@tarojs/taro'

// 缓存键名常量
export const CACHE_KEYS = {
  CASE_CATEGORY_LIST: 'case_category_list',
  DYNAMICS_CATEGORY_LIST: 'dynamics_category_list',
  CASE_STAGE_LIST: 'case_stage_list',
  MINI_CONFIG: 'mini_config'
} as const

// 缓存数据结构
interface CacheData<T = any> {
  data: T
  timestamp: number
  expireTime?: number
}

class CacheManager {
  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 缓存数据
   * @param expireTime 过期时间（毫秒），可选
   */
  static setCache<T>(key: string, data: T, expireTime?: number): void {
    try {
      const cacheData: CacheData<T> = {
        data,
        timestamp: Date.now(),
        expireTime
      }
      Taro.setStorageSync(key, JSON.stringify(cacheData))
    } catch (error) {
      console.error(`设置缓存失败: ${key}`, error)
    }
  }

  /**
   * 获取缓存
   * @param key 缓存键
   * @returns 缓存数据或null
   */
  static getCache<T>(key: string): T | null {
    try {
      const cacheStr = Taro.getStorageSync(key)
      if (!cacheStr) {
        return null
      }

      const cacheData: CacheData<T> = JSON.parse(cacheStr)
      
      // 检查是否过期
      if (cacheData.expireTime && Date.now() - cacheData.timestamp > cacheData.expireTime) {
        this.removeCache(key)
        return null
      }
      return cacheData.data
    } catch (error) {
      console.error(`获取缓存失败: ${key}`, error)
      return null
    }
  }

  /**
   * 移除缓存
   * @param key 缓存键
   */
  static removeCache(key: string): void {
    try {
      Taro.removeStorageSync(key)
    } catch (error) {
      console.error(`移除缓存失败: ${key}`, error)
    }
  }

  /**
   * 检查缓存是否存在
   * @param key 缓存键
   * @returns 是否存在有效缓存
   */
  static hasCache(key: string): boolean {
    return this.getCache(key) !== null
  }
}

export default CacheManager
