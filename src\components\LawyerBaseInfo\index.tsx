/**
 * 律师基础信息组件
 * 
 * 展示律师头像、姓名、等级、地址、律所和认证信息
 */
import React from 'react'
import { View, Text, Image } from '@tarojs/components'

// 导入律师等级图标
import { LawyerLevelIcon1, LawyerLevelIcon2, LawyerLevelIcon3, LawyerLevelIcon4, LawyerLevelIcon5 } from '@/constant/image'
import DefaultAvatar from '@/assets/images/common-icon/avatar.png'
// 导入认证图标
import verifyIcon from '@/assets/images/common-icon/verify.png'

import './index.scss'

// 组件属性
export interface LawyerBaseInfoProps {
  // 律师信息
  lawyerInfo: LawyerAPI.LawyerInfo
  // 自定义样式类名
  className?: string
}

const LawyerBaseInfo: React.FC<LawyerBaseInfoProps> = ({
  lawyerInfo,
  className = ''
}) => {
  // 获取律师等级图标
  const getLevelIcon = (lawyerLevel: number): string => {
    const levelIconMap = {
      1: LawyerLevelIcon1,
      2: LawyerLevelIcon2,
      3: LawyerLevelIcon3,
      4: LawyerLevelIcon4,
      5: LawyerLevelIcon5
    }
    return levelIconMap[lawyerLevel] || LawyerLevelIcon1
  }

  // 格式化地址信息
  const formatAddress = () => {
    const { province, city, district } = lawyerInfo
    return [province, city, district].filter(Boolean).join(' ')
  }

  return (
    <View className={`lawyer-base-info ${className}`}>
      {/* 头像 */}
      <View className='lawyer-base-info__avatar'>
        <Image
          className='lawyer-base-info__avatar-img'
          src={lawyerInfo.figurePhotoUrl || DefaultAvatar}
          mode='aspectFill'
        />
      </View>

      {/* 右侧信息区域 */}
      <View className='lawyer-base-info__content'>
        {/* 第一行：姓名 + 律师等级图标 */}
        <View className='lawyer-base-info__name-row'>
          <Text className='lawyer-base-info__name'>{lawyerInfo.name}</Text>
          {lawyerInfo.lawyerLevel > 0 && (
            <Image
              className='lawyer-base-info__level-icon'
              src={getLevelIcon(lawyerInfo.lawyerLevel)}
              mode='aspectFit'
            />
          )}
        </View>

        {/* 第二行：地址信息 */}
        <View className='lawyer-base-info__address-row'>
          <Text className='lawyer-base-info__address'>{formatAddress()}</Text>
        </View>

        {/* 第三行：律所信息 */}
        <View className='lawyer-base-info__firm-row'>
          <Text className='lawyer-base-info__firm'>{lawyerInfo.lawFirm}</Text>
        </View>

        {/* 第四行：认证信息 */}
        <View className='lawyer-base-info__verify-row'>
          <Image
            className='lawyer-base-info__verify-icon'
            src={verifyIcon}
            mode='aspectFit'
          />
          <Text className='lawyer-base-info__verify-text'>平台认证/担保</Text>
        </View>
      </View>
    </View>
  )
}

export default LawyerBaseInfo
