/**
 * 编辑电子名片页面样式
 */

.edit-business-card-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #ffffff;

  // 表单容器
  .form-container {
    flex: 1;
    padding: 32rpx;
    padding-bottom: 220rpx; // 为底部按钮留出空间
    display: flex;
    flex-direction: column;
    gap: 32rpx;
    overflow-y: auto;
  }

  // 表单项
  .form-section {
    display: flex;
    flex-direction: column;
    gap: 16rpx;

    // 表单标签
    .form-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      line-height: 1.4;

      // 字数统计
      .word-count {
        font-size: 24rpx;
        font-weight: 400;
        color: #999999;
      }
    }

    // 通用输入框
    .form-input {
      width: 100%;
      height: 88rpx;
      padding: 0 24rpx;
      background: #f8f9fa;
      border-radius: 16rpx;
      border: 2rpx solid #e9ecef;
      font-size: 30rpx;
      color: #333333;
      box-sizing: border-box;
      transition: all 0.2s ease;

      &:focus {
        border-color: #BD8A4F;
        background: #ffffff;
        box-shadow: 0 0 0 4rpx rgba(189, 138, 79, 0.1);
      }
    }

    // 个人简介输入框
    .profile-textarea {
      width: 100%;
      min-height: 200rpx;
      padding: 24rpx;
      background: #f8f9fa;
      border-radius: 16rpx;
      border: 2rpx solid #e9ecef;
      font-size: 30rpx;
      line-height: 1.6;
      color: #333333;
      box-sizing: border-box;
      transition: all 0.2s ease;
      resize: none;

      &:focus {
        border-color: #BD8A4F;
        background: #ffffff;
        box-shadow: 0 0 0 4rpx rgba(189, 138, 79, 0.1);
      }
    }

    // 图片上传区域
    .image-upload {
      width: 200rpx;
      height: 200rpx;
      border-radius: 16rpx;
      overflow: hidden;
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.98);
      }

      .uploaded-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .upload-placeholder {
        width: 100%;
        height: 100%;
        background: #f8f9fa;
        border: 2rpx dashed #e9ecef;
        border-radius: 16rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover {
          border-color: #BD8A4F;
          background: rgba(189, 138, 79, 0.05);
        }

        .upload-icon {
          font-size: 48rpx;
          color: #BD8A4F;
          font-weight: 300;
          margin-bottom: 8rpx;
        }

        .upload-text {
          font-size: 24rpx;
          color: #666666;
          text-align: center;
        }
      }
    }

    // 地区选择器
    .area-select-input {
      width: 100%;

      .area-select-display {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 88rpx;
        padding: 0 24rpx;
        background: #f8f9fa;
        border-radius: 16rpx;
        border: 2rpx solid #e9ecef;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
        box-sizing: border-box;

        // 添加微妙的渐变背景
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 249, 250, 0.9) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
          pointer-events: none;
        }

        .area-text {
          flex: 1;
          font-size: 30rpx;
          font-weight: 500;
          color: #333333;
          line-height: 1.4;
          letter-spacing: 0.5rpx;
          position: relative;
          z-index: 1;
        }

        .area-placeholder {
          flex: 1;
          font-size: 30rpx;
          color: #999999;
          line-height: 1.4;
          position: relative;
          z-index: 1;
        }

        .area-arrow {
          font-size: 28rpx;
          color: #BD8A4F;
          font-weight: 600;
          transform: rotate(90deg);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          z-index: 1;
          width: 32rpx;
          height: 32rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background: rgba(189, 138, 79, 0.1);
        }

        // 悬停效果（在支持的设备上）
        &:hover {
          border-color: rgba(189, 138, 79, 0.6);
          box-shadow: 0 2rpx 8rpx rgba(189, 138, 79, 0.15);

          &::before {
            opacity: 1;
          }

          .area-arrow {
            background: rgba(189, 138, 79, 0.2);
            transform: rotate(90deg) scale(1.1);
          }
        }

        // 激活状态
        &:active {
          border-color: #BD8A4F;
          background: #ffffff;
          box-shadow: 0 0 0 4rpx rgba(189, 138, 79, 0.15), 0 4rpx 12rpx rgba(189, 138, 79, 0.1);
          transform: translateY(-1rpx);

          &::before {
            opacity: 1;
          }

          .area-arrow {
            background: rgba(189, 138, 79, 0.3);
            transform: rotate(90deg) scale(1.2);
            color: #A67A45;
          }
        }

        // 聚焦状态（通过类名控制）
        &.focused {
          border-color: #BD8A4F;
          background: #ffffff;
          box-shadow: 0 0 0 4rpx rgba(189, 138, 79, 0.1);

          .area-arrow {
            background: rgba(189, 138, 79, 0.2);
            color: #A67A45;
          }
        }
      }
    }

    // 擅长领域标签
    .field-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;

      .field-tag {
        padding: 12rpx 24rpx;
        background: #f8f9fa;
        border: 2rpx solid #e9ecef;
        border-radius: 24rpx;
        transition: all 0.2s ease;

        .tag-text {
          font-size: 28rpx;
          color: #666666;
          white-space: nowrap;
        }

        &.selected {
          background: rgba(189, 138, 79, 0.1);
          border-color: #BD8A4F;

          .tag-text {
            color: #BD8A4F;
            font-weight: 500;
          }
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }

  // 提交按钮区域
  .submit-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 32rpx;
    background: #ffffff;
    border-top: 1rpx solid #f0f0f0;
    box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
    z-index: 100;

    .submit-btn {
      width: 100%;
      height: 88rpx;
      background: linear-gradient(135deg, #BD8A4F 0%, #D4A574 100%);
      border-radius: 44rpx;
      border: none;
      font-size: 32rpx;
      font-weight: 600;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      box-shadow: 0 4rpx 12rpx rgba(189, 138, 79, 0.3);

      &:not([disabled]):active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 8rpx rgba(189, 138, 79, 0.4);
      }

      &[disabled] {
        background: #cccccc;
        color: #999999;
        box-shadow: none;
        transform: none;
      }

      &.button-hover {
        background: linear-gradient(135deg, #A67A45 0%, #C19A6A 100%);
        box-shadow: 0 6rpx 16rpx rgba(189, 138, 79, 0.4);
      }
    }
  }
}