/**
 * 本地文件相关工具函数
 */
import Taro from '@tarojs/taro'

/**
 * 保存文件到本地
 * @param tempFilePath 临时文件路径
 */
export const saveFile = (tempFilePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.saveFile({
      tempFilePath,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 获取已保存的文件列表
 */
export const getSavedFileList = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.getSavedFileList({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 获取本地文件信息
 * @param filePath 文件路径
 */
export const getFileInfo = (filePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.getFileInfo({
      filePath,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 删除本地文件
 * @param filePath 文件路径
 */
export const removeSavedFile = (filePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.removeSavedFile({
      filePath,
      success: resolve,
      fail: reject
    })
  })
}
