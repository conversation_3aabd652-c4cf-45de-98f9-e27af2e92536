/**
 * HomeNav 首页导航组件
 * 
 * 2行4列的导航图标组件，包含8个导航项目
 */
import React, { useCallback } from 'react'
import Taro from '@tarojs/taro'
import { View, Image, Text } from '@tarojs/components'
import { navigateToPage, setClipboardData } from '@/utils'
// 导入图标
import { HomeNavIcon1, HomeNavIcon2, HomeNavIcon3, HomeNavIcon4, HomeNavIcon5, HomeNavIcon6, HomeNavIcon7, HomeNavIcon8 } from '@/constant/image'
import { HomeNavProps, NavItem } from './types'
import './index.scss'


// 默认导航项目配置
const defaultNavItems: NavItem[] = [
  {
    id: 1,
    iconSrc: HomeNavIcon1,
    title: '黄金救援',
    linkUrl: '/subpackages/common/golden-rescue/index?type=1',
    needCheckLogin: true,
    needCheckLawyer: false
  },
  {
    id: 2,
    iconSrc: HomeNavIcon2,
    title: '优配律师',
    linkUrl: '/subpackages/common/golden-rescue/index?type=2',
    needCheckLogin: true,
    needCheckLawyer: false
  },
  {
    id: 3,
    iconSrc: HomeNavIcon3,
    title: '品牌律师',
    linkUrl: '/subpackages/lawyer/lawyer-list/index?type=2',
    needCheckLogin: false,
    needCheckLawyer: false
  },
  {
    id: 4,
    iconSrc: HomeNavIcon4,
    title: '金牌律师',
    linkUrl: '/subpackages/lawyer/lawyer-list/index?type=1',
    needCheckLogin: false,
    needCheckLawyer: false
  },
  {
    id: 5,
    iconSrc: HomeNavIcon5,
    title: '我的案件',
    linkUrl: '/subpackages/my/my-cases/index',
    needCheckLogin: true,
    needCheckLawyer: false
  },
  {
    id: 6,
    iconSrc: HomeNavIcon6,
    title: '履约保证',
    linkUrl: '/subpackages/common/contract-management/index',
    needCheckLogin: false,
    needCheckLawyer: false
  },
  {
    id: 7,
    iconSrc: HomeNavIcon7,
    title: '律师入驻',
    linkUrl: '/subpackages/lawyer/lawyer-entry/index',
    needCheckLogin: true,
    needCheckLawyer: true
  },
  {
    id: 8,
    iconSrc: HomeNavIcon8,
    title: '关于我们',
    linkUrl: '/subpackages/common/about-us/index',
    needCheckLogin: false,
    needCheckLawyer: false
  }
]

const HomeNav: React.FC<HomeNavProps> = ({
  navItems = defaultNavItems,
  className = '',
  onItemClick,
  disabled = false,
  isLoggedIn,
  isLawyer
}) => {
  // 处理导航项点击
  const handleItemClick = useCallback(async (item: NavItem, index: number) => {
    if (disabled) return

    // 如果有自定义点击事件，优先执行
    if (onItemClick) {
      onItemClick(item, index)
      return
    }

    // 如果没有跳转链接，不执行任何操作
    if (!item.linkUrl) {
      console.log(`点击了 ${item.title}，但没有配置跳转链接`)
      return
    }

    // 执行页面跳转
    try {
      // 增加登录校验和律师身份校验
      if (item.needCheckLogin && !isLoggedIn) {
        // 2秒后跳转到登录页面
        Taro.showToast({
          title: '请先登录',
          icon: 'none'
        })
        setTimeout(() => {
          Taro.navigateTo({ url: '/subpackages/login/index' })
        }, 2000)
        return
      }

      if (item.linkUrl.startsWith('http')) {
        // 外部链接，复制到剪贴板
        await setClipboardData(item.linkUrl)
      } else {
        // 我的案件根据用户身份跳转不同页面
        if (item.title === '我的案件') {
          if (isLawyer) {
            await navigateToPage('/subpackages/lawyer/lawyer-case-orders/index')
          } else {
            await navigateToPage('/subpackages/my/my-cases/index')
          }
        } else {
          // 使用全局导航函数
          await navigateToPage(item.linkUrl)
        }
      }
    } catch (error) {
      console.error('导航跳转失败:', error)
    }
  }, [onItemClick, disabled, isLoggedIn, isLawyer])

  return (
    <View className={`home-nav ${className} ${disabled ? 'home-nav--disabled' : ''}`}>
      <View className='home-nav__container'>
        {navItems.map((item, index) => (
          <View
            key={item.id}
            className={`home-nav__item ${!item.linkUrl ? 'home-nav__item--no-link' : ''}`}
            onClick={() => handleItemClick(item, index)}
          >
            {/* 图标 */}
            <View className='home-nav__icon-wrapper'>
              <Image
                className='home-nav__icon'
                src={item.iconSrc}
                mode='aspectFit'
              />
            </View>

            {/* 标题 */}
            <Text className='home-nav__title'>
              {item.title}
            </Text>
          </View>
        ))}
      </View>
    </View>
  )
}

export default HomeNav
