/**
 * 律师头像组件
 * 
 * 展示律师头像、认证图标、等级图标、姓名、关注按钮和个人简介
 */
import React from 'react'
import { View, Text, Image } from '@tarojs/components'

// 导入图标
import focusIcon from '@/assets/images/common-icon/focus.png'
import heartOutlineIcon from '@/assets/images/common-icon/heart_outline_w.png'
import heartActiveIcon from '@/assets/images/common-icon/heart_active.png'

// 导入律师等级图标
import { LawyerLevelIcon1, LawyerLevelIcon2, LawyerLevelIcon3, LawyerLevelIcon4, LawyerLevelIcon5 } from '@/constant/image'
import './index.scss'

// 组件属性
export interface LawyerAvatarProps {
  // 律师信息
  lawyerInfo: LawyerAPI.LawyerInfo
  // 是否已关注
  isFollowed: boolean
  // 关注状态切换回调
  onFollowToggle: () => void
  // 自定义样式类名
  className?: string
}

const LawyerAvatar: React.FC<LawyerAvatarProps> = ({
  lawyerInfo,
  isFollowed,
  onFollowToggle,
  className = ''
}) => {
  // 获取律师等级图标
  const getLevelIcon = (lawyerLevel: number): string => {
    const levelIconMap = {
      1: LawyerLevelIcon1,
      2: LawyerLevelIcon2,
      3: LawyerLevelIcon3,
      4: LawyerLevelIcon4,
      5: LawyerLevelIcon5
    }
    return levelIconMap[lawyerLevel] || LawyerLevelIcon1
  }

  return (
    <View className={`lawyer-avatar ${className}`}>
      <View className='lawyer-avatar__container'>
        <Image
          className='lawyer-avatar__img'
          src={lawyerInfo.figurePhotoUrl}
          mode='aspectFill'
        />

        {/* 姓名和关注按钮 */}
        <View className='lawyer-avatar__name-follow'>
          <View className='lawyer-avatar__name-section'>
            <Text className='lawyer-avatar__name-text'>{lawyerInfo.name}</Text>
            {/* 律师等级图标 */}
            <Image
              className='lawyer-avatar__level-badge-icon'
              src={getLevelIcon(lawyerInfo.lawyerLevel)}
              mode='aspectFit'
            />
          </View>
          <View className='lawyer-avatar__follow-section'>
            <Image
              className='lawyer-avatar__follow-icon'
              src={isFollowed ? heartActiveIcon : heartOutlineIcon}
              mode='aspectFit'
              onClick={onFollowToggle}
            />
            <Text className='lawyer-avatar__follow-text'>
              {isFollowed ? '已关注' : '关注'}
            </Text>
          </View>
        </View>

        {/* 聚焦信息框 */}
        <View className='lawyer-avatar__focus'>
          <Image
            className='lawyer-avatar__focus-bg'
            src={focusIcon}
            mode='aspectFit'
          />
          <Text className='lawyer-avatar__focus-text'>{lawyerInfo.personalProfile}</Text>
        </View>
      </View>
    </View>
  )
}

export default LawyerAvatar
