.follow-card {
  display: flex;
  align-items: center;
  background: #fff;
  margin: 20rpx 30rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }

  .follow-card__avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 24rpx;
    flex-shrink: 0;

    .follow-card__avatar-img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
  }

  .follow-card__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;

    .follow-card__name {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      line-height: 1.4;
    }

    .follow-card__time {
      font-size: 26rpx;
      color: #999;
      line-height: 1.4;
    }
  }
}
