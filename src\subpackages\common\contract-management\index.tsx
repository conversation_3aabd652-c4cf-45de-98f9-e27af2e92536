/**
 * 合同管理页面
 */
import React from 'react'
import { View, Text } from '@tarojs/components'
import PageLayout, { PageContent } from '@/components/PageLayout'
import './index.scss'

const ContractManagement: React.FC = () => {

  return (
    <View className='contract-management-page'>
      <PageLayout
        title='履约保证'
        showBack
        backgroundColor='#f5f5f5'
        showFloatingMenu={false}
      >
        <PageContent>
          <View className='development-notice'>
            <View className='notice-icon'>
              <Text className='notice-icon-text'>🚧</Text>
            </View>
            <Text className='notice-title'>功能开发中</Text>
            <Text className='notice-description'>
              履约保证功能正在紧急开发中，敬请期待
            </Text>
            <Text className='notice-tip'>
              如有疑问，请联系客服
            </Text>
          </View>
        </PageContent>
      </PageLayout>
    </View>
  )
}

export default ContractManagement
