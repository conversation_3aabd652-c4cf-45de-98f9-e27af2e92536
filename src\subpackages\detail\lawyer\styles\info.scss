/**
 * 律师信息内容相关样式
 */

.lawyer-detail {
  // 个人简介区域
  &__profile {
    background-color: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  }

  &__profile-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #000000;
  }

  &__profile-content {
    margin-top: 30rpx;
    max-height: 520rpx;
    overflow: hidden;
    transition: max-height 0.3s ease;

    &--expanded {
      max-height: none;
    }
  }

  &__profile-text {
    font-size: 28rpx;
    color: #333333;
    line-height: 1.6;
  }

  &__profile-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20rpx;
    gap: 10rpx;
  }

  &__profile-icon {
    width: 45rpx;
    height: 45rpx;
  }

  &__profile-toggle-text {
    font-weight: 400;
    font-size: 24rpx;
    color: #727E8C;
    line-height: 42rpx;
  }

  // 擅长领域区域
  &__fields {
    background-color: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  }

  &__fields-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #000000;
  }

  &__fields-list {
    margin-top: 30rpx;
    display: flex;
    flex-wrap: wrap;
    gap: 15rpx;
  }

  &__field-tag {
    width: 160rpx;
    height: 56rpx;
    background: #BD8A4F;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__field-text {
    font-weight: 600;
    font-size: 28rpx;
    color: #FFFFFF;
  }

  // 律师数据卡片
  &__stats-card {
    margin-bottom: 30rpx;
  }

  // 律所信息区域
  &__firm {
    background-color: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  }

  &__firm-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #000000;
  }

  &__firm-item {
    margin-top: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    &:last-child {
      padding-top: 20rpx;
      border-top: 1rpx solid #eee;
      margin-bottom: 0;
    }
  }

  &__firm-label {
    font-size: 24rpx;
    color: #666666;
    font-weight: 400;
  }

  &__firm-value {
    font-size: 26rpx;
    color: #333333;
    font-weight: 500;
    text-align: right;
    flex: 1;
    margin-left: 20rpx;
  }

  // 操作按钮区域
  &__actions {
    display: flex;
    gap: 20rpx;
    padding: 20rpx 0;
  }

  &__btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 600;
    border: none;

    &--primary {
      background-color: #BD8A4F;
      color: #ffffff;

      &::after {
        border: none;
      }
    }

    &--secondary {
      background-color: #ffffff;
      color: #BD8A4F;
      border: 2rpx solid #BD8A4F;

      &::after {
        border: none;
      }
    }
  }
}
