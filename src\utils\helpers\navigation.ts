/**
 * 导航相关工具函数
 */
import Taro from '@tarojs/taro'

/**
 * 判断是否为 tabbar 页面
 * @param url 页面路径
 * @returns 是否为 tabbar 页面
 */
export const isTabBarPage = (url: string): boolean => {
  const tabBarPages = [
    '/pages/index/index',
    '/pages/lawyer/index',
    '/pages/case/index',
    '/pages/mine/index'
  ]

  const pagePath = url.split('?')[0]
  return tabBarPages.includes(pagePath)
}

/**
 * 页面跳转处理
 * @param url 跳转链接
 * @param options 跳转选项
 */
export const navigateToPage = async (
  url: string,
  options: {
    showToast?: boolean
    toastTitle?: string
  } = {}
): Promise<void> => {
  const { showToast = true, toastTitle = '跳转失败' } = options

  if (!url) {
    throw new Error('跳转链接不能为空')
  }

  try {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      // 外部链接，复制到剪贴板
      await Taro.setClipboardData({
        data: url
      })
      if (showToast) {
        Taro.showToast({
          title: '链接已复制',
          icon: 'success'
        })
      }
    } else if (isTabBarPage(url)) {
      // tabbar 页面
      await Taro.switchTab({
        url: url.split('?')[0] // tabbar 页面不支持参数
      })
    } else {
      // 普通页面
      await Taro.navigateTo({
        url
      })
    }
  } catch (error) {
    console.error('页面跳转失败:', error)
    if (showToast) {
      Taro.showToast({
        title: toastTitle,
        icon: 'error'
      })
    }
    throw error
  }
}

/**
 * 返回上一页
 * @param delta 返回的页面数
 */
export const navigateBack = (delta: number = 1): void => {
  Taro.navigateBack({ delta })
}

/**
 * 重定向到指定页面
 * @param url 页面路径
 */
export const redirectTo = async (url: string): Promise<void> => {
  try {
    await Taro.redirectTo({ url })
  } catch (error) {
    console.error('页面重定向失败:', error)
    throw error
  }
}

/**
 * 重新加载当前页面
 */
export const relaunchCurrentPage = async (): Promise<void> => {
  const pages = Taro.getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const url = `/${currentPage.route}`

  try {
    await Taro.reLaunch({ url })
  } catch (error) {
    console.error('页面重新加载失败:', error)
    throw error
  }
}
