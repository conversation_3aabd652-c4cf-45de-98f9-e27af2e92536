/**
 * 律师基础信息组件样式
 */

.lawyer-base-info {
  display: flex;
  align-items: flex-start;
  padding: 40rpx 20rpx;
  background-color: #ffffff;

  // 头像区域
  &__avatar {
    width: 166rpx;
    height: 166rpx;
    margin-right: 18rpx;
    flex-shrink: 0;
    border: 1rpx solid #BD8A4F;
    border-radius: 50%;
    overflow: hidden;
  }

  &__avatar-img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }

  // 内容区域
  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }

  // 姓名行
  &__name-row {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
  }

  &__name {
    color: #000000;
    font-weight: 600;
    font-size: 32rpx;
    line-height: 1.2;
    margin-right: 12rpx;
  }

  &__level-icon {
    height: 32rpx;
    width: auto;
  }

  // 地址行
  &__address-row {
    margin-bottom: 12rpx;
  }

  &__address {
    color: #828D99;
    font-size: 28rpx;
    line-height: 1.2;
  }

  // 律所行
  &__firm-row {
    margin-bottom: 12rpx;
  }

  &__firm {
    color: #828D99;
    font-size: 28rpx;
    line-height: 1.2;
  }

  // 认证行
  &__verify-row {
    display: flex;
    align-items: center;
  }

  &__verify-icon {
    width: 24rpx;
    height: 24rpx;
    margin-right: 8rpx;
  }

  &__verify-text {
    color: #828D99;
    font-size: 28rpx;
    line-height: 1.2;
  }
}
