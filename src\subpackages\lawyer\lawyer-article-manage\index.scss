.lawyer-article-manage {
  height: 100vh;
  display: flex;
  flex-direction: column;

  .lawyer-search-header {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    padding: 15rpx 30rpx;
    background: #fff;
    border-bottom: 2rpx solid #f0f0f0;
    gap: 20rpx;

    .lawyer-search-box {
      flex: 1;
      display: flex;
      align-items: center;
      height: 60rpx;
      background: #f7f7f7;
      border-radius: 30rpx;
      padding: 0 24rpx;
      box-sizing: border-box;

      .lawyer-search-icon {
        width: 28rpx;
        height: 28rpx;
        margin-right: 16rpx;
        flex-shrink: 0;
      }

      .lawyer-search-input {
        flex: 1;
        height: 100%;
        font-size: 26rpx;
        color: #333;
        background: transparent;
        border: none;
        outline: none;

        &::placeholder {
          color: #999;
        }
      }
    }

    .lawyer-add-button {
      width: 60rpx;
      height: 60rpx;
      background: #ffffff;
      border-radius: 20rpx;
      border: 2rpx solid #e0e0e0;
      display: flex;
      align-items: center;
      justify-content: center;

      .lawyer-add-icon {
         width: 32rpx;
        height: 32rpx;
        filter: none;
      }
    }
  }

  .lawyer-article-list-container {
    flex: 1;
    overflow: hidden;
  }

  .lawyer-article-list {
    height: 100%;
    background: #f8f9fa;

    .lawyer-article-card {
      background: #fff;
      margin: 20rpx 30rpx;
      border-radius: 16rpx;
      padding: 30rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      }

      .lawyer-article-header {
        margin-bottom: 20rpx;

        .lawyer-article-title-row {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12rpx;

          .lawyer-article-title {
            flex: 1;
            font-size: 32rpx;
            font-weight: 600;
            color: #333;
            line-height: 1.4;
            margin-right: 20rpx;
            min-height: 89.6rpx; /* 32rpx * 1.4 * 2 = 两行的最小高度 */
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            word-break: break-all;
          }

          .lawyer-article-status {
            flex-shrink: 0;
            padding: 6rpx 12rpx;
            border-radius: 12rpx;
            font-size: 22rpx;
            font-weight: 500;

            .status-text {
              color: inherit;
            }

            &.status-pending {
              background: rgba(255, 193, 7, 0.1);
              color: #ff8f00;
              border: 1rpx solid rgba(255, 193, 7, 0.3);
            }

            &.status-approved {
              background: rgba(76, 175, 80, 0.1);
              color: #388e3c;
              border: 1rpx solid rgba(76, 175, 80, 0.3);
            }

            &.status-rejected {
              background: rgba(244, 67, 54, 0.1);
              color: #d32f2f;
              border: 1rpx solid rgba(244, 67, 54, 0.3);
            }

            &.status-unknown {
              background: rgba(158, 158, 158, 0.1);
              color: #757575;
              border: 1rpx solid rgba(158, 158, 158, 0.3);
            }
          }
        }

        .lawyer-article-reject-reason {
          margin-top: 12rpx;
          padding: 12rpx;
          background: rgba(244, 67, 54, 0.05);
          border-left: 4rpx solid #f44336;
          border-radius: 8rpx;

          .reject-reason-text {
            font-size: 24rpx;
            color: #d32f2f;
            line-height: 1.4;
          }
        }
      }

      .lawyer-article-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .lawyer-article-time {
          font-size: 24rpx;
          color: #999;
        }

        .lawyer-article-stats {
          display: flex;
          gap: 20rpx;

          .lawyer-stat-item {
            font-size: 24rpx;
            color: #666;
          }
        }
      }
    }

    .lawyer-loading-more,
    .lawyer-no-more,
    .lawyer-empty-state {
      text-align: center;
      padding: 60rpx 30rpx;
      color: #999;
      font-size: 28rpx;
    }

    .lawyer-empty-state {
      padding: 120rpx 30rpx;
    }
  }
}