/**
 * 分享相关工具函数
 */
import Taro, { useShareAppMessage, useShareTimeline } from '@tarojs/taro'
import { useEffect } from 'react'
import { HomeBg } from '@/constant/image'

/**
 * 分享配置
 */
export interface ShareConfig {
  title?: string
  path?: string
  imageUrl?: string
}

/**
 * 默认分享配置
 */
const DEFAULT_SHARE_CONFIG: ShareConfig = {
  title: '胜张仪刑辩律师服务平台',
  path: '/pages/index/index',
  imageUrl: HomeBg
}

/**
 * 设置页面分享功能
 * @param config 分享配置
 */
export const usePageShare = (config: ShareConfig = {}) => {
  const shareConfig = { ...DEFAULT_SHARE_CONFIG, ...config }
  const { title, path, imageUrl } = shareConfig

  // 转发朋友功能
  useShareAppMessage(() => ({
    title,
    path,
    imageUrl
  }))

  // 转发朋友圈功能
  useShareTimeline(() => ({
    title,
    imageUrl
  }))

  useEffect(() => {
    // 显示右上角转发按钮
    Taro.showShareMenu({
      withShareTicket: true,
      showShareItems: ['shareAppMessage', 'shareTimeline']
    })
  }, [])
}

/**
 * 手动显示分享菜单
 */
export const showShareMenu = () => {
  Taro.showShareMenu({
    withShareTicket: true,
    showShareItems: ['shareAppMessage', 'shareTimeline']
  })
}