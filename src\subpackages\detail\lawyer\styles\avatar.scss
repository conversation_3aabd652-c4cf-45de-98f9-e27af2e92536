/**
 * 律师头像相关样式
 */

.lawyer-detail {
  // 头部信息区域
  &__header {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx 0;
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  }

  &__avatar {
    position: relative;
    width: 690rpx;
    height: 960rpx;
    margin: 0 30rpx;
    overflow: hidden;
    flex-shrink: 0;
  }

  &__avatar-img {
    width: 100%;
    height: 100%;
  }

  // 右上角认证图标
  &__verify {
    position: absolute;
    top: 30rpx;
    right: 30rpx;
  }

  &__verify-icon {
    width: 64rpx;
    height: 64rpx;
  }

  // 律师等级图标
  &__level-badge {
    position: absolute;
    bottom: 300rpx;
    right: 30rpx;
  }

  &__level-badge-icon {
    max-width: 296rpx;
    height: 52rpx;
  }

  // 姓名和关注按钮
  &__name-follow {
    position: absolute;
    bottom: 180rpx;
    left: 30rpx;
    right: 30rpx;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
  }

  &__name-text {
    font-size: 36rpx;
    font-weight: 800;
    color: #ffffff;
  }

  &__follow-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
  }

  &__follow-icon {
    width: 48rpx;
    height: 48rpx;
  }

  &__follow-text {
    font-size: 24rpx;
    color: #ffffff;
    text-align: center;
  }

  // 聚焦信息框
  &__focus {
    position: absolute;
    bottom: 30rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 612rpx;
    height: 102rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__focus-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  &__focus-text {
    position: relative;
    z-index: 2;
    color: #ffffff;
    font-size: 28rpx;
    text-align: center;
    padding: 0 30rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
  }
}
