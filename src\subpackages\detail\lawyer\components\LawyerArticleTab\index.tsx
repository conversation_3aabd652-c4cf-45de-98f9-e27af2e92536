/**
 * 律师文章Tab内容组件
 * 
 * 包含律师文章数据统计和文章列表两部分
 */
import React from 'react'
import { View } from '@tarojs/components'
import LawyerArticleStats from './components/LawyerArticleStats'
import LawyerArticleList from './components/LawyerArticleList'
import './index.scss'

// 组件属性
export interface LawyerArticleTabProps {
  // 律师ID
  lawyerId: number
  // 内容统计数据
  contentNum: ArticleAPI.GetLawyerArticleCountResponse | null
  // 是否显示加载状态
  loading?: boolean
  // 自定义样式类名
  className?: string
}

const LawyerArticleTab: React.FC<LawyerArticleTabProps> = ({
  lawyerId,
  contentNum,
  loading = false,
  className = '',
}) => {
  return (
    <View className={`lawyer-article-tab ${className}`}>
      {/* 律师文章数据统计 */}
      <LawyerArticleStats
        contentNum={contentNum}
        loading={loading}
        className='lawyer-article-tab__stats'
      />

      {/* 分割线 */}
      <View className='lawyer-article-tab__divider' />

      {/* 律师文章列表 */}
      <LawyerArticleList
        lawyerId={lawyerId}
        className='lawyer-article-tab__list'
      />
    </View>
  )
}

export default LawyerArticleTab
