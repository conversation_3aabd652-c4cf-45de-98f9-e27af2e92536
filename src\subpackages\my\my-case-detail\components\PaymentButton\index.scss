/**
 * 支付按钮组件样式
 */

.payment-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, #ffffff 100%);
  padding: 24rpx 30rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid rgba(189, 138, 79, 0.2);
  z-index: 100;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);

  // 添加进入动画
  animation: slideUp 0.3s ease-out;

  &__info {
    margin-bottom: 24rpx;
    padding: 20rpx;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 16rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.04);
  }

  &__status-info {
    text-align: center;
    padding: 24rpx 20rpx;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 16rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.04);
  }

  &__status-text {
    font-size: 30rpx;
    font-weight: 600;
    margin-bottom: 12rpx;
    display: block;
    letter-spacing: 0.5rpx;

    &.payment-pending {
      color: #e67e22;
      text-shadow: 0 2rpx 4rpx rgba(230, 126, 34, 0.2);
    }

    &.payment-completed {
      color: #27ae60;
      text-shadow: 0 2rpx 4rpx rgba(39, 174, 96, 0.2);
    }

    &.payment-failed {
      color: #e74c3c;
      text-shadow: 0 2rpx 4rpx rgba(231, 76, 60, 0.2);
    }
  }

  &__amount-text {
    font-size: 26rpx;
    color: #5a6c7d;
    display: block;
    font-weight: 500;
  }

  &__tip {
    font-size: 24rpx;
    color: #7f8c8d;
    line-height: 36rpx;
    display: block;
    font-weight: 400;
    opacity: 0.8;
  }

  &__btn {
    width: 100%;
    height: 96rpx;
    background: linear-gradient(135deg, #BD8A4F 0%, #d4a574 50%, #BD8A4F 100%);
    color: #ffffff;
    font-size: 34rpx;
    font-weight: 700;
    border-radius: 48rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6rpx 20rpx rgba(189, 138, 79, 0.4);
    transition: all 0.3s ease;
    letter-spacing: 1rpx;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.6s;
    }

    &:hover::before {
      left: 100%;
    }

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 4rpx 15rpx rgba(189, 138, 79, 0.3);
    }

    &.button-disabled {
      background: linear-gradient(135deg, #cccccc 0%, #d5d5d5 100%);
      color: #999999;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      cursor: not-allowed;
    }

    &.button-loading {
      background: linear-gradient(135deg, #cccccc 0%, #d5d5d5 100%);

      &::after {
        content: '';
        width: 20rpx;
        height: 20rpx;
        border: 2rpx solid #999999;
        border-top: 2rpx solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-left: 16rpx;
      }
    }
  }
}

// 滑入动画
@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// 旋转动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
