/**
 * 律师详情Tab导航组件样式
 */

.lawyer-tabs {
  display: flex;
  margin-bottom: 30rpx;

  &__tab {
    flex: 1;
    text-align: center;
    padding: 30rpx 0;
    position: relative;
    transition: all 0.3s ease;
    

    &--active {
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background-color: #BD8A4F;
        border-radius: 2rpx;
      }
    }

    &:not(&--active):active {
      background-color: #F5F5F5;
    }
  }

  &__tab-text {
    font-size: 28rpx;
    font-weight: 500;
    color: #666666;
    transition: all 0.2s ease;

    .lawyer-tabs__tab--active & {
      color: #BD8A4F;
      font-weight: 600;
    }
  }
}
