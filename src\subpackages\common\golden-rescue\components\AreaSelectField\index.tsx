/**
 * 地区选择字段组件
 */
import React, { useMemo } from 'react'
import { View, Text } from '@tarojs/components'
import AreaSelect from '@/components/AreaSelect'
import type { AreaSelectResult } from '@/components/AreaSelect/types'
import type { AreaSelectFieldProps } from '../../types'
import './index.scss'

const AreaSelectField: React.FC<AreaSelectFieldProps> = ({
  label,
  province,
  city,
  district,
  onChange,
  required = false,
  placeholder = '请选择地区',
  className = ''
}) => {
  // 处理地区选择变化
  const handleAreaChange = (result: AreaSelectResult) => {
    onChange(
      result.province || '',
      result.city || '',
      result.district || ''
    )
  }

  // 使用 useMemo 优化显示文本计算
  const displayText = useMemo((): string => {
    if (province && city && district) {
      return `${province}/${city}/${district}`
    }
    return placeholder
  }, [province, city, district, placeholder])

  // 使用 useMemo 优化初始值计算
  const initialValue = useMemo((): string => {
    if (province && city && district) {
      // AreaSelect组件期望的格式是 "省份名/城市名/区县名"
      return `${province}/${city}/${district}`
    }
    return ''
  }, [province, city, district])

  return (
    <View className={`area-select-field ${className}`}>
      <View className='field-label'>
        {required && <Text className='required-mark'>*</Text>}
        <Text className='label-text'>{label}</Text>
      </View>
      
      <View className='field-content'>
        <AreaSelect
          value={initialValue}
          onChange={handleAreaChange}
          placeholder={placeholder}
        >
          <View className={`area-select-trigger ${!province ? 'placeholder' : ''}`}>
            <Text className='area-text'>{displayText}</Text>
            <View className='arrow-icon'>
              <Text className='arrow'>›</Text>
            </View>
          </View>
        </AreaSelect>
      </View>
    </View>
  )
}

export default AreaSelectField
