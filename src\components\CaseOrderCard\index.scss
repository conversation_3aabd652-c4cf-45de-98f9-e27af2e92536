/**
 * 通用案件订单卡片样式
 */

.case-order-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
  }

  // 订单头部
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .case-order-card__order-info {
      flex: 1;

      .case-order-card__order-no {
        font-size: 26rpx;
        font-weight: 600;
        color: #333333;
        line-height: 1.4;
      }
    }

    .case-order-card__order-status {
      font-size: 24rpx;
      font-weight: 500;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      white-space: nowrap;

      // 订单状态样式
      &.status-created {
        background: #fff3e0;
        color: #f57c00;
      }

      &.status-pending {
        background: #f3e5f5;
        color: #7b1fa2;
      }

      &.status-processing {
        background: #e3f2fd;
        color: #1976d2;
      }

      &.status-completed {
        background: #e8f5e8;
        color: #388e3c;
      }

      &.status-cancelled {
        background: #ffebee;
        color: #d32f2f;
      }

      &.status-unknown {
        background: #f5f5f5;
        color: #757575;
      }
    }
  }

  // 内容区域
  &__content {
    .case-order-card__info-row {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16rpx;
      min-height: 40rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .case-order-card__info-label {
        font-size: 26rpx;
        color: #666666;
        width: 140rpx;
        flex-shrink: 0;
        line-height: 40rpx;
      }

      .case-order-card__info-value {
        font-size: 26rpx;
        color: #333333;
        flex: 1;
        line-height: 40rpx;
        word-break: break-all;

        &.case-order-card__requirements {
          line-height: 36rpx;
          max-height: 108rpx;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }
      }

      // 支付信息特殊布局
      .case-order-card__payment-info {
        display: flex;
        align-items: center;
        gap: 16rpx;
        flex: 1;

        .case-order-card__info-value {
          flex: 0 0 auto;
        }

        .case-order-card__payment-status {
          font-size: 22rpx;
          font-weight: 500;
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
          white-space: nowrap;

          // 支付状态样式
          &.payment-pending {
            background: #fff3e0;
            color: #f57c00;
          }

          &.payment-completed {
            background: #e8f5e8;
            color: #388e3c;
          }

          &.payment-refunded {
            background: #ffebee;
            color: #d32f2f;
          }

          &.payment-unknown {
            background: #f5f5f5;
            color: #757575;
          }
        }
      }
    }
  }
}