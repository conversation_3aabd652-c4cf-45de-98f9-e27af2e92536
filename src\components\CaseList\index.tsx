/**
 * 纵向案例组件
 *
 * 展示案例列表，支持外部传入筛选条件，纵向布局，支持滚动加载
 */
import React, { useState, useEffect, useRef } from 'react'
import { View, Text, Image, ScrollView } from '@tarojs/components'
import { caseApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
import { navigateToPage } from '@/utils'
import ClockIcon from '@/assets/images/common-icon/clock.png'
import Yu from '@/assets/images/common-icon/yu.png'
import { CaseListProps } from './types'
import './index.scss'

const CaseList: React.FC<CaseListProps> = ({
  filterParams,
  list,
  className = '',
  enableScrollLoad = false,
  onScrollLoad,
  hasMore = false,
  scrollLoading = false
}) => {
  // 案例列表数据
  const [cases, setCases] = useState<CaseAPI.CaseListInfo[]>([])
  // 加载状态
  const [isLoading, setIsLoading] = useState(false)
  // 当前页码（用于滚动加载）
  const [currentPage, setCurrentPage] = useState(1)
  // 滚动加载防抖
  const scrollLoadingRef = useRef(false)

  // 加载案例列表数据
  const loadCases = async (val?: CaseAPI.GetCaseListRequest, isLoadMore = false) => {
    try {
      if (!isLoadMore) {
        setIsLoading(true)
        setCurrentPage(1)
      }

      const params = {
        page: isLoadMore ? currentPage + 1 : 1,
        pageSize: 10, // 纵向列表可以显示更多案例
        ...val
      }

      const response = await caseApi.getCaseList(params)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        const newCases = response.data.list || []
        if (isLoadMore) {
          setCases(prev => [...prev, ...newCases])
          setCurrentPage(prev => prev + 1)
        } else {
          setCases(newCases)
        }
      }
    } catch (error) {
      console.error('加载案例列表失败:', error)
    } finally {
      if (!isLoadMore) {
        setIsLoading(false)
      }
    }
  }

  // 处理滚动到底部加载更多
  const handleScrollToLower = async () => {
    if (!enableScrollLoad || !onScrollLoad || !hasMore || scrollLoading || scrollLoadingRef.current) {
      return
    }

    try {
      scrollLoadingRef.current = true
      await onScrollLoad(currentPage + 1)
    } catch (error) {
      console.error('滚动加载失败:', error)
    } finally {
      scrollLoadingRef.current = false
    }
  }

  // 处理案例卡片点击 - 跳转到案例详情页
  const handleCaseClick = (caseInfo: CaseAPI.CaseListInfo) => {
    navigateToPage(`/subpackages/detail/case/index?id=${caseInfo.id}`)
  }

  // 使用外部传入的数据
  useEffect(() => {
    if (list) {
      setCases(list)
      setIsLoading(false)
    }
  }, [list])

  // 监听filterParams变化，重新加载数据（仅在没有传入list时）
  useEffect(() => {
    if (!list) {
      loadCases(filterParams)
    }
  }, [filterParams, list])

  // 显示加载状态
  const showLoading = isLoading

  // 渲染案例列表内容
  const renderCaseList = () => {
    if (cases.length === 0) {
      return (
        <View className='case-list__empty'>
          <Text className='case-list__empty-title'>暂无案例信息</Text>
          <Text className='case-list__empty-desc'>当前筛选条件下没有找到相关案例，请尝试调整搜索条件</Text>
        </View>
      )
    }

    const caseItems = cases.map((caseInfo) => (
      <View
        key={caseInfo.id}
        className='case-item'
        onClick={() => handleCaseClick(caseInfo)}
      >
        <View className='case-item__header'>
          {filterParams?.isExcellent === 2 && (
            <Image src={Yu} mode='aspectFit' className='case-item__excellent-icon' />
          )}
          <Text className='case-item__title'>{caseInfo.title}</Text>
        </View>
        <View className='case-item__meta'>
          <Text className='case-item__category'>{caseInfo.categoryName}</Text>
          <Image src={ClockIcon} mode='aspectFit' className='case-item__clock-icon' />
          <Text className='case-item__time'>{caseInfo.createdAt}</Text>
        </View>
      </View>
    ))

    // 如果启用滚动加载，使用 ScrollView
    if (enableScrollLoad) {
      return (
        <ScrollView
          className='case-list__scroll-container'
          scrollY
          onScrollToLower={handleScrollToLower}
          lowerThreshold={100}
        >
          <View className='case-list__container'>
            {caseItems}
          </View>
          {/* 滚动加载状态 */}
          {scrollLoading && (
            <View className='case-list__scroll-loading'>
              <Text>加载更多...</Text>
            </View>
          )}
          {/* 没有更多数据提示 */}
          {!hasMore && cases.length > 0 && (
            <View className='case-list__no-more'>
              <Text>没有更多数据了</Text>
            </View>
          )}
        </ScrollView>
      )
    }

    // 普通模式，不使用滚动加载
    return (
      <View className='case-list__container'>
        {caseItems}
      </View>
    )
  }

  return (
    <View className={`case-list ${className}`}>
      {showLoading ? (
        <View className='case-list__loading'>
          <Text>加载中...</Text>
        </View>
      ) : (
        renderCaseList()
      )}
    </View>
  )
}

// 导出组件
export default CaseList

// 导出类型
export type { CaseListProps } from './types'
