/**
 * 案例详情页面
 */
import React, { useState, useEffect } from 'react'
import { View, Text, Image } from '@tarojs/components'
import BookMark from '@/assets/images/common-icon/book_mark_color.png'
import Taro, { useRouter } from '@tarojs/taro'
import { caseApi, commonApi } from '@/utils/request/apis'
import UserCacheManager from '@/utils/cache/userCache'
import { STATUS_CODE } from '@/utils/request/config'
import { formatTime } from '@/utils'
import { LawyerBaseInfo, FollowLawyerButton } from '@/components'
import PageLayout, { PageContent } from '@/components/PageLayout'
import clockIcon from '@/assets/images/common-icon/clock.png'
import eyeIcon from '@/assets/images/common-icon/eye.png'
import HorizontalCaseList from '@/components/HorizontalCaseList'

import './index.scss'

interface CaseDetailInfo {
  id: number
  title: string
  categoryId: number
  categoryName: string
  content: string
  viewCount: number
  creator: string
  creatorId: number
  createdAt: string
}

const CaseDetail: React.FC = () => {
  const router = useRouter()
  const { id } = router.params
  const userId = UserCacheManager.getUserId()
  const [caseInfo, setCaseInfo] = useState<CaseDetailInfo | null>(null)
  const [isFollowed, setIsFollowed] = useState(false)
  const [lawyerInfo, setLawyerInfo] = useState<LawyerAPI.LawyerInfo | null>(null)
  // 推荐案例列表
  const [recommendCases, setRecommendCases] = useState<CaseAPI.CaseListInfo[]>([])

  // 加载推荐案例列表
  const loadRecommendCases = async () => {
    try {
      const response = await caseApi.getRecommendCaseList(Number(id), {
        page: 1,
        pageSize: 5,
        caseId: Number(id)
      })
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setRecommendCases(response.data.list || [])
      }
    } catch (error) {
      console.error('加载推荐案例列表失败:', error)
    }
  }



  // 检查关注状态
  const checkFollowStatus = async (lawyerId: number) => {
    if (!userId) {
      return
    }
    try {
      const response = await commonApi.checkUserBusinessStatus({
        bizId: lawyerId,
        userId,
        bizType: ['follow']
      })
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setIsFollowed(response.data.follow)
      }
    } catch (error) {
      console.error('检查关注状态失败:', error)
    }
  }

  // 处理关注状态变化
  const handleFollowChange = (followed: boolean) => {
    setIsFollowed(followed)
  }

  // 加载案例详情
  const loadCaseDetail = async () => {
    try {
      const response = await caseApi.getCaseDetail(Number(id))
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setCaseInfo(response.data.detail)
        setLawyerInfo(response.data.lawyerInfo)
        // 检查关注状态
        checkFollowStatus(response.data.lawyerInfo.id)
      }
    } catch (error) {
      console.error('加载案例详情失败:', error)
    } finally {
    }
  }
  const onClickMoreCase = () => {
    Taro.switchTab({
      url: '/pages/case/index'
    })
  }

  useEffect(() => {
    if (id) {
      loadCaseDetail()
      loadRecommendCases()
    }
  }, [id])

  return (
    <PageLayout
      title='律师案例详情'
      backgroundColor='#fff'
      navBarTextColor='#BD8A4F'
      showBack
    >
      <PageContent padded='b'>
        <View className='case-detail' >
          {caseInfo && (
            <View className='case-detail__content'>
              {lawyerInfo && <LawyerBaseInfo
                lawyerInfo={lawyerInfo}
              />}
              {/* 案例信息卡片 */}
              <View className='case-info-card'>
                {/* 标题 */}
                <Text className='case-info-card__title'>{caseInfo.title}</Text>

                {/* 信息模块 */}
                <View className='case-info-card__meta'>
                  <Text className='case-info-card__category'>{caseInfo.categoryName}</Text>
                  <View className='case-info-card__time'>
                    <Image
                      className='case-info-card__time-icon'
                      src={clockIcon}
                      mode='aspectFit'
                    />
                    <Text className='case-info-card__time-text'>
                      {formatTime(caseInfo.createdAt)}
                    </Text>
                  </View>
                  <View className='case-info-card__view'>
                    <Image
                      className='case-info-card__view-icon'
                      src={eyeIcon}
                      mode='aspectFit'
                    />
                    <Text className='case-info-card__view-text'>{caseInfo.viewCount}</Text>
                  </View>
                </View>
              </View>
              {/* 案例内容 */}
              <View className='case-content'>
                <Text className='case-content__title'>案例详情</Text>
                <Text className='case-content__text'>
                  {caseInfo.content || '暂无详细内容'}
                </Text>
              </View>
              <View className='case-detail-list'>
                <View className='case-detail-list__header'>
                  <View className='case-detail-list__title'>
                    <Image className='case-detail-list__title-icon' src={BookMark} mode='aspectFit' />
                    <Text className='case-detail-list__title-text'>相关案例</Text>
                  </View>
                </View>
                <HorizontalCaseList list={recommendCases} />
              </View>
            </View>
          )}
        </View>
        {/* 关注律师按钮 */}
        {lawyerInfo && <FollowLawyerButton
          lawyerId={lawyerInfo.id}
          isFollowed={isFollowed}
          onFollowChange={handleFollowChange}
        />}
      </PageContent>
    </PageLayout>
  )
}

export default CaseDetail
