/**
 * 公共模块类型声明
 */

// 引用通用类型
/// <reference path="../common-types.d.ts" />

declare namespace CommonAPI {
  // 案例分类信息
  interface LawCaseCategoryDto {
    id: number
    name: string
  }

  // 案例分类列表响应
  interface CaseCategoryListResponse {
    list: LawCaseCategoryDto[]
  }

  // 动态分类信息
  interface LawDynamicsCategoryDto {
    id: number
    name: string
  }

  // 动态分类列表响应
  interface DynamicsCategoryListResponse {
    list: LawDynamicsCategoryDto[]
  }

  // 案件阶段信息（基于 CaseStageDto）
  interface CaseStageDto {
    id: number
    stageName: string
  }

  // 案件阶段列表响应
  interface CaseStageListResponse {
    list: CaseStageDto[]
  }

  // 账号登录请求
  interface AccountLoginRequest {
    userName: string
    password: string
  }

  // 账号登录响应
  interface AccountLoginResponse {
    id: number
    userName: string
    type: number
    token: string
    expires: number
  }

  // 手机号登录请求
  interface PhoneLoginRequest {
    phone: string
    code: string
  }

  // 手机号登录响应
  interface PhoneLoginResponse {
    id: number
    userName: string
    type: number
    token: string
    expires: number
  }

  // 律师注册请求
  interface UserRegisterRequest {
    userName: string
    password: string
    password2: string
    phone: string
    code: string
    type: 1 | 2
    province?: string
    city?: string
    district?: string
    idCard?: string
    licenseNum?: string
    lawFirm?: string
  }

  // 律师注册响应
  interface UserRegisterResponse { }

  // 验证码请求
  interface VerificationCodeRequest {
    phone: string
    bizCode: 'miniLogin' | 'miniRegister'
    bizType: 'mini' | 'web'
  }

  // 验证码响应
  interface VerificationCodeResponse {
    code: string
  }

  // 手机验证码请求
  interface PhoneVerificationCodeRequest {
    phone: string
    bizCode: 'miniOrder' | 'miniLogin' | 'miniRegister'  // Enum: "miniLogin" "miniRegister" "miniOrder"
    bizType: 'mini' | 'web'
  }

  // 手机验证码响应
  interface PhoneVerificationCodeResponse {
    success: boolean
    message?: string
  }

  // 微信一键登录请求
  interface WechatLoginRequest {
    phoneCode: string   // "getPhoneNumber 返回的 code 与 wx.login 返回的 code 作用是不一样的，不能混用。"getPhoneNumber返回的code
    loginCode: string  //通过 wx.login 获得的插件用户标志凭证 code，有效时间为5分钟"wx.login 返回的code
  }

  // 微信一键登录响应
  interface WechatLoginResponse {
    id: number
    userName: string
    type: number
    token: string
    expires: number
  }

  // 验证用户业务状态是否(点赞、收藏、关注)
  interface CheckUserBusinessStatusRequest {
    bizId: number
    userId: number
    bizType: string[] // 业务类型：nice=>文章点赞,collect=>文章收藏,follow=>律师关注
  }

  // 验证用户业务状态是否(点赞、收藏、关注)响应
  interface CheckUserBusinessStatusResponse {
    nice: boolean
    collect: boolean
    follow: boolean
  }

  // 小程序配置信息响应
  interface GetMiniConfigResponse {
    configDataStr: string
  }

  // 小程序配置信息类型
  interface MiniConfig {
    margin: string
    servicePhone: string
    hotlinePhone: string
  }

  // 案件订单阶段记录信息（通用类型）
  interface CaseOrderStageRecordsInfo {
    id: number
    orderId: number
    stageId: number
    stageName: string
    status: number  // 状态：1-删除，2-正常，3-完结
    createdAt: string
    progressData: CaseOrderStageProgressData[]
  }

  // 案件订单阶段进度数据
  interface CaseOrderStageProgressData {
    id: number
    orderStageId: number
    orderProgressName: string
    caseProgressId: number
    done: number  // 是否已经完成，1否，2是。默认1
    stageFollowRecords: CaseOrderStageRecordData[]
  }

  // 案件订单阶段记录数据
  interface CaseOrderStageRecordData {
    id: number
    orderStageId: number
    orderProgressName: string
    orderProgressId: number
    content: string
    createdAt: string
    imageUrls: string
  }

  // 获取案件订单阶段记录响应（通用类型）
  interface GetCaseOrderStageRecordsResponse extends CaseOrderStageRecordsInfo { }
}
