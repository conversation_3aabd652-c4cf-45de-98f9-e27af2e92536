/**
 * 律师相关接口
 * 基于 api.json 接口文档定义
 */
import request from '../../index'
import { RequestConfig } from '../../types'

// 律师相关接口
export const lawyerApi = {
  // 获取律师列表（基于 /mini/find-lawyer/list）
  getLawyerList: (params?: LawyerAPI.GetLawyerListRequest) =>
    request.get<LawyerAPI.GetLawyerListResponse>('/mini/find-lawyer/list', params),

  // 获取律师详情信息（基于 /mini/find-lawyer/:lawyerId/detail-info）
  getLawyerDetailInfo: (lawyerId: number) =>
    request.get<LawyerAPI.GetLawyerDetailInfoResponse>(`/mini/find-lawyer/${lawyerId}/detail-info`, { lawyerId }),

  // 获取律师统计数据（基于 /mini/find-lawyer/:lawyerId/detail-lawyer-count）
  getLawyerCountData: (lawyerId: number) =>
    request.get<LawyerAPI.GetLawyerCountDataResponse>(`/mini/find-lawyer/${lawyerId}/detail-lawyer-count`, { lawyerId }),

  // 获取律师内容统计（基于 /mini/find-lawyer/:lawyerId/detail-content-count）
  getLawyerContentNum: (lawyerId: number) =>
    request.get<LawyerAPI.GetLawyerContentNumResponse>(`/mini/find-lawyer/${lawyerId}/detail-content-count`, { lawyerId }),

  // 关注律师（基于 /mini/find-lawyer/:lawyerId/follow）
  followLawyer: (lawyerId: number) =>
    request.post<LawyerAPI.FollowLawyerResponse>(`/mini/find-lawyer/${lawyerId}/follow`, { lawyerId }),

  // 取消关注律师（基于 /mini/find-lawyer/:lawyerId/cancel-follow）
  cancelFollowLawyer: (lawyerId: number) =>
    request.post<LawyerAPI.CancelFollowLawyerResponse>(`/mini/find-lawyer/${lawyerId}/cancel-follow`, { lawyerId }),

  // 获取律师信息（律师个人中心）
  getOwnerLawyerInfo: (config?: Omit<RequestConfig, 'url'>) =>
    request.get<LawyerAPI.GetOwnerLawyerInfoResponse>('/mini/owner-center-lawyer/lawyer-info', {}, config),

  // 更新律师名片（律师个人中心）
  updateBusinessCard: (data: LawyerAPI.UpdateBusinessCardRequest) =>
    request.post<LawyerAPI.UpdateBusinessCardResponse>('/mini/owner-center-lawyer/business-card/update', data),

  // 律师认证提交（律师个人中心）
  submitAuthentication: (data: LawyerAPI.SubmitAuthenticationRequest) =>
    request.post<LawyerAPI.SubmitAuthenticationResponse>('/mini/owner-center-lawyer/lawyer-authentication/commit', data),

  // 获取律师关注列表（律师个人中心）
  getLawyerFollowList: (params?: LawyerAPI.GetLawyerFollowListRequest) =>
    request.get<LawyerAPI.GetLawyerFollowListResponse>('/mini/owner-center-lawyer/lawyer-follow/list', params),

  // 获取我的案件订单列表（律师个人中心）
  getMyCaseOrderList: (params?: LawyerAPI.GetMyCaseOrderListRequest) =>
    request.get<LawyerAPI.GetMyCaseOrderListResponse>('/mini/owner-center-lawyer/my-case-order/list', params),

  // 案源订单详情（律师用）
  getMyCaseOrderDetail: (orderId: number) =>
    request.get<LawyerAPI.GetMyCaseOrderDetailResponse>(`/mini/owner-center-lawyer/my-case-order/${orderId}/detail`),

  // 案源订单阶段记录（律师用）
  getMyCaseOrderStageRecords: (orderId: number) =>
    request.get<LawyerAPI.GetMyCaseOrderStageRecordsResponse>(`/mini/owner-center-lawyer/my-case-order/${orderId}/stage-records`),

  // 案件阶段进度完成
  completeCaseOrderProgress: (orderStageId: number, orderProgressId: number) =>
    request.post(`/mini/owner-center-lawyer/my-case-order/${orderStageId}/${orderProgressId}/done`),

  // 案件阶段记录跟进
  followCaseOrderProgress: (orderStageId: number, data: LawyerAPI.FollowCaseOrderProgressRequest) =>
    request.post(`/mini/owner-center-lawyer/my-case-order/${orderStageId}/record-add`, data)
}
