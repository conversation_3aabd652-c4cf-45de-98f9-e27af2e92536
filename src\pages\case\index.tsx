import React, { useEffect, useState } from 'react'
import { View, Input, Image, Text, PageContainer } from '@tarojs/components'
import PageLayout, { PageContent } from '@/components/PageLayout'
import { useNavBarHeight } from '@/hooks/useNavBarHeight'
import CategoryCacheManager from '@/utils/cache/categoryCache'
import CaseList from '@/components/CaseList'
import SearchIcon from '@/assets/images/common-icon/search.png'
import MenuIcon from '@/assets/images/common-icon/menu.png'
import '@/styles/index.scss'
import './index.scss'

const Case: React.FC = () => {
  const { navBarHeight } = useNavBarHeight()
  // 搜索关键词
  const [searchKeyword, setSearchKeyword] = useState('')
  // 案例分类列表
  const [caseCategories, setCaseCategories] = useState<CommonAPI.LawCaseCategoryDto[]>([])
  // 选中的分类ID
  const [selectedCategoryId, setSelectedCategoryId] = useState<number>(0)
  // 分类加载状态
  const [categoriesLoading, setCategoriesLoading] = useState(false)
  // 筛选弹窗显示状态
  const [showFilterModal, setShowFilterModal] = useState(false)
  // 筛选参数
  const [filterParams, setFilterParams] = useState<CaseAPI.GetCaseListRequest>({})
  // 高度
  const [listHeight, setListHeight] = useState('')

  // 获取案例分类数据（使用缓存）
  const loadCaseCategories = async () => {
    try {
      setCategoriesLoading(true)
      const categories = await CategoryCacheManager.getCaseCategoryList()
      setCaseCategories([{ id: 0, name: '全部' }, ...categories])
    } catch (error) {
      console.error('获取案例分类失败', error)
      setCaseCategories([])
    } finally {
      setCategoriesLoading(false)
    }
  }

  // 处理搜索输入（只更新输入框状态，不触发搜索）
  const handleSearchInput = (e: any) => {
    const value = e.detail.value
    setSearchKeyword(value)
  }

  // 执行搜索（点击搜索图标或软键盘搜索时触发）
  const handleSearch = () => {
    updateFilterParams({ title: searchKeyword })
  }
  // 处理筛选按钮点击
  const handleFilterClick = () => {
    setShowFilterModal(true)
  }
  // 关闭筛选弹窗
  const handleCloseFilter = () => {
    setSelectedCategoryId(filterParams.categoryId || 0)
    setShowFilterModal(false)
  }

  // 处理软键盘搜索确认
  const handleSearchConfirm = (e: any) => {
    const value = e.detail.value
    setSearchKeyword(value)
    updateFilterParams({ title: value })
  }

  // 处理分类选择
  const handleCategorySelect = (categoryId?: number) => {
    setSelectedCategoryId(categoryId || 0)
  }

  // 更新筛选参数
  const updateFilterParams = (params: Partial<CaseAPI.GetCaseListRequest>) => {
    setFilterParams(prev => ({ ...prev, ...params }))
  }

  // 获取列表容器高度
  const getListHeight = () => {
    const searchHeight = '88rpx'
    setListHeight(`calc(90vh - ${navBarHeight}px - ${searchHeight} - 130rpx)`)
  }

  // 重置筛选条件
  const handleResetFilter = () => {
    setSelectedCategoryId(0)
  }

  // 确认筛选
  const handleConfirmFilter = () => {
    setFilterParams(prev => ({
      ...prev,
      categoryId: selectedCategoryId || 0,
    }))
    setShowFilterModal(false)
  }

  // 页面生命周期
  useEffect(() => {
    loadCaseCategories()
    getListHeight()
  }, [])

  return (
    <PageLayout
      title='案例'
      showBack={false}
      scrollable={false}
    >
      <PageContent padded='b' className='case-page'>
        <View className='case-content'>
          {/* 搜索框 */}
          <View className='search-bar'>
            <Input
              className='search-bar__input'
              placeholder='搜索案例'
              value={searchKeyword}
              onInput={handleSearchInput}
              onConfirm={handleSearchConfirm}
              confirmType='search'
            />
            <View className='search-bar__left' onClick={handleSearch}>
              <Image className='search-bar__icon' src={SearchIcon} mode='aspectFit' />
            </View>
            <View className='search-bar__right' onClick={handleFilterClick}>
              <Text className='search-bar__text'>筛选</Text>
              <Image className='search-bar__icon' src={MenuIcon} mode='aspectFit' />
            </View>
          </View>
          <View id='case-page-list' className='case-content__list'>
            <CaseList enableScrollLoad filterParams={filterParams} scrollHeight={listHeight} />
          </View>
        </View>
        <PageContainer
          show={showFilterModal}
          position='bottom'
          round
          overlay
          onClickOverlay={handleCloseFilter}
          onAfterLeave={handleCloseFilter}
        >

          <View className='filter-container'>
            {/* 弹窗头部 */}
            <View className='filter-container__header'>
              <Text className='filter-container__title'>筛选</Text>
              <Text className='filter-container__close' onClick={handleCloseFilter}>×</Text>
            </View>

            {/* 弹窗内容 */}
            <View className='filter-container__body'>
              {/* 案件类型筛选 */}
              <View className='filter-section'>
                <Text className='filter-section__title'>案件类型</Text>
                <View className='filter-section__options'>
                  {categoriesLoading ? (
                    <Text className='filter-loading'>加载中...</Text>
                  ) : caseCategories.length > 0 ? (
                    caseCategories.map((category) => (
                      <Text
                        key={category.id}
                        className={`filter-option ${selectedCategoryId === category.id ? 'filter-option--selected' : ''}`}
                        onClick={() => handleCategorySelect(category.id)}
                      >
                        {category.name}
                      </Text>
                    ))
                  ) : (
                    <Text className='filter-empty'>暂无案件类型</Text>
                  )}
                </View>
              </View>
            </View>

            {/* 弹窗底部操作按钮 */}
            <View className='filter-container__footer'>
              <View className='filter-container__buttons'>
                <Text className='filter-container__reset' onClick={handleResetFilter}>
                  重置
                </Text>
                <Text className='filter-container__confirm' onClick={handleConfirmFilter}>
                  确定
                </Text>
              </View>
            </View>
          </View>
        </PageContainer>
      </PageContent>
    </PageLayout>
  )
}

export default Case
