import React, { useEffect, useState } from 'react'
import { View, Input, Image, Text } from '@tarojs/components'
import PageLayout, { PageContent } from '@/components/PageLayout'
import CategoryCacheManager from '@/utils/cache/categoryCache'
import CaseList from '@/components/CaseList'
import SearchIcon from '@/assets/images/common-icon/search.png'
import ClickMoreIcon from '@/assets/images/common-icon/click_more.png'

import './index.scss'

const Case: React.FC = () => {
  // 搜索关键词
  const [searchKeyword, setSearchKeyword] = useState('')
  // 案例分类列表
  const [caseCategories, setCaseCategories] = useState<CommonAPI.LawCaseCategoryDto[]>([])
  // 选中的分类ID
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>(undefined)
  // 分类加载状态
  const [categoriesLoading, setCategoriesLoading] = useState(false)
  // 分类折叠状态（默认折叠，显示两行半）
  const [categoriesCollapsed, setCategoriesCollapsed] = useState(true)
  // 筛选参数
  const [filterParams, setFilterParams] = useState<CaseAPI.GetCaseListRequest>({})

  // 获取案例分类数据（使用缓存）
  const loadCaseCategories = async () => {
    try {
      setCategoriesLoading(true)
      const categories = await CategoryCacheManager.getCaseCategoryList()
      setCaseCategories(categories)
    } catch (error) {
      console.error('获取案例分类失败', error)
      setCaseCategories([])
    } finally {
      setCategoriesLoading(false)
    }
  }

  // 处理搜索输入（只更新输入框状态，不触发搜索）
  const handleSearchInput = (e: any) => {
    const value = e.detail.value
    setSearchKeyword(value)
  }

  // 执行搜索（点击搜索图标或软键盘搜索时触发）
  const handleSearch = () => {
    updateFilterParams({ title: searchKeyword })
  }

  // 处理软键盘搜索确认
  const handleSearchConfirm = (e: any) => {
    const value = e.detail.value
    setSearchKeyword(value)
    updateFilterParams({ title: value })
  }

  // 处理分类选择
  const handleCategorySelect = (categoryId?: number) => {
    setSelectedCategoryId(categoryId)
    updateFilterParams({ categoryId })
  }

  // 切换分类折叠状态
  const toggleCategoriesCollapse = () => {
    setCategoriesCollapsed(prev => !prev)
  }

  // 更新筛选参数
  const updateFilterParams = (params: Partial<CaseAPI.GetCaseListRequest>) => {
    setFilterParams(prev => ({ ...prev, ...params }))
  }

  // 页面生命周期
  useEffect(() => {
    loadCaseCategories()
  }, [])

  return (
    <PageLayout
      title='案例'
      showBack={false}
    >
      <PageContent padded='b'>
        <View className='case-content'>
          <View className='case-content__header'>
            {/* 搜索框 */}
            <View className='search-container'>
              <View className='search-box'>
                <Image
                  src={SearchIcon}
                  className='search-icon'
                  mode='aspectFit'
                  onClick={handleSearch}
                />
                <Input
                  className='search-input'
                  placeholder='搜索案例'
                  value={searchKeyword}
                  onInput={handleSearchInput}
                  onConfirm={handleSearchConfirm}
                  confirmType='search'
                />
              </View>
            </View>

            {/* 案例类型选择 */}
            <View className='category-section'>
              <View className={`category-container ${categoriesCollapsed ? 'category-container--collapsed' : ''}`}>
                <View
                  className={`category-item ${selectedCategoryId === undefined ? 'category-item--active' : ''}`}
                  onClick={() => handleCategorySelect(undefined)}
                >
                  <Text className='category-text'>全部</Text>
                </View>
                {categoriesLoading ? (
                  <View className='category-loading'>
                    <Text className='category-loading-text'>加载分类中...</Text>
                  </View>
                ) : (
                  caseCategories.map((category) => (
                    <View
                      key={category.id}
                      className={`category-item ${selectedCategoryId === category.id ? 'category-item--active' : ''}`}
                      onClick={() => handleCategorySelect(category.id)}
                    >
                      <Text className='category-text'>{category.name}</Text>
                    </View>
                  ))
                )}
              </View>

              {/* 展开/折叠按钮 */}
              {(caseCategories.length > 0 || categoriesLoading) && (
                <View className='category-toggle-container'>
                  <View className='category-toggle' onClick={toggleCategoriesCollapse}>
                    <Image
                      src={ClickMoreIcon}
                      className={`toggle-icon ${categoriesCollapsed ? '' : 'toggle-icon--expanded'}`}
                      mode='aspectFit'
                    />
                  </View>
                </View>
              )}
            </View>
          </View>
          <View className='case-content__list'>
            <CaseList filterParams={filterParams} />
          </View>
        </View>
      </PageContent>
    </PageLayout>
  )
}

export default Case
