/**
 * 实用工具函数
 */
import Taro from '@tarojs/taro'
import { BASE_URL } from '@/constant/image'

/**
 * 检查是否为开发环境
 */
export const isDev = (): boolean => {
  return process.env.NODE_ENV === 'development'
}

/**
 * 检查是否为生产环境
 */
export const isProd = (): boolean => {
  return process.env.NODE_ENV === 'production'
}

/**
 * 获取小程序版本信息
 */
export const getAppVersion = (): string => {
  try {
    const accountInfo = Taro.getAccountInfoSync()
    return accountInfo.miniProgram.version || '1.0.0'
  } catch (error) {
    console.error('获取版本信息失败:', error)
    return '1.0.0'
  }
}

/**
 * 检查小程序更新
 */
export const checkForUpdate = (): void => {
  if (Taro.canIUse('getUpdateManager')) {
    const updateManager = Taro.getUpdateManager()

    updateManager.onCheckForUpdate((res) => {
      console.log('检查更新结果:', res.hasUpdate)
    })

    updateManager.onUpdateReady(() => {
      Taro.showModal({
        title: '更新提示',
        content: '新版本已经准备好，是否重启应用？',
        success: (res) => {
          if (res.confirm) {
            updateManager.applyUpdate()
          }
        }
      })
    })

    updateManager.onUpdateFailed(() => {
      console.error('新版本下载失败')
    })
  }
}

/**
 * 转换json图片url为数组
 */
export const parseImageUrls = (val: string): string[] => {
  if (!val) return []
  try {
    // 如果没有BASE_URL前缀，添加上
    const imgs = JSON.parse(val) as string[]
    return imgs.map(img => img.includes(BASE_URL) ? img : `${BASE_URL}${img}`)
  } catch (error) {
    console.error('解析图片URL失败:', error)
    return []
  }
}

/**
 * 添加图片前缀
 */

export const parseImageUrl = (val: string): string => {
  if (!val) return ''
  try {
    // 如果没有BASE_URL前缀，添加上
    return val.includes(BASE_URL) ? val : `${BASE_URL}${val}`
  } catch (error) {
    console.error('解析图片URL失败:', error)
    return ''
  }
}

/**
 * 完整url去除BASE_URL前缀
 */
export const removeImagesPrefix = (url: string[]): string => {
  if (!url) return ''
  const urlString = JSON.stringify(url)
  const reg = new RegExp(BASE_URL, 'g')
  return urlString.includes(BASE_URL) ? urlString.replace(reg, '') : urlString
}

export const removeImagePrefix = (url: string): string => {
  if (!url) return ''
  const reg = new RegExp(BASE_URL, 'g')
  return url.includes(BASE_URL) ? url.replace(reg, '') : url
}
