/**
 * 实用工具函数
 */
import Taro from '@tarojs/taro'

/**
 * 检查是否为开发环境
 */
export const isDev = (): boolean => {
  return process.env.NODE_ENV === 'development'
}

/**
 * 检查是否为生产环境
 */
export const isProd = (): boolean => {
  return process.env.NODE_ENV === 'production'
}

/**
 * 获取小程序版本信息
 */
export const getAppVersion = (): string => {
  try {
    const accountInfo = Taro.getAccountInfoSync()
    return accountInfo.miniProgram.version || '1.0.0'
  } catch (error) {
    console.error('获取版本信息失败:', error)
    return '1.0.0'
  }
}

/**
 * 检查小程序更新
 */
export const checkForUpdate = (): void => {
  if (Taro.canIUse('getUpdateManager')) {
    const updateManager = Taro.getUpdateManager()

    updateManager.onCheckForUpdate((res) => {
      console.log('检查更新结果:', res.hasUpdate)
    })

    updateManager.onUpdateReady(() => {
      Taro.showModal({
        title: '更新提示',
        content: '新版本已经准备好，是否重启应用？',
        success: (res) => {
          if (res.confirm) {
            updateManager.applyUpdate()
          }
        }
      })
    })

    updateManager.onUpdateFailed(() => {
      console.error('新版本下载失败')
    })
  }
}
