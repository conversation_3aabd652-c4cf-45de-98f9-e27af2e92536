/**
 * 动态分类选择组件
 */
import React from 'react'
import { View, Text } from '@tarojs/components'

// 组件属性
export interface DynamicsCategoriesProps {
  // 分类列表
  categories: DynamicsAPI.DynamicsCategoryInfo[]
  // 选中的分类ID
  selectedCategoryId: number | null
  // 分类选择回调
  onCategorySelect: (categoryId: number) => void
  // 自定义样式类名
  className?: string
}

const DynamicsCategories: React.FC<DynamicsCategoriesProps> = ({
  categories,
  selectedCategoryId,
  onCategorySelect,
  className = ''
}) => {
  // 获取显示的分类（折叠时只显示前6个）
  const getDisplayCategories = () => {
    if (categories.length <= 6) {
      return categories
    }
    return categories.slice(0, 6)
  }

  return (
    <View className={`dynamics-categories ${className}`}>
      <View className='dynamics-categories__list'>
        {getDisplayCategories().map((category) => (
          <View
            key={category.id}
            className={`dynamics-categories__item ${
              selectedCategoryId === category.id ? 'dynamics-categories__item--active' : ''
            }`}
            onClick={() => onCategorySelect(category.id)}
          >
            <Text className='dynamics-categories__item-text'>
              {category.name.length > 4 ? `${category.name.slice(0, 4)}...` : category.name}
            </Text>
          </View>
        ))}
      </View>
    </View>
  )
}

export default DynamicsCategories
