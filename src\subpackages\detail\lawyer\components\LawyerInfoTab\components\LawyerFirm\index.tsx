/**
 * 律所信息组件
 * 
 * 展示律师的执业律所和律所地址信息
 */
import React from 'react'
import { View, Text } from '@tarojs/components'
import './index.scss'

// 律所信息项配置
interface FirmInfoItem {
  label: string
  value: string
  key: string
}

// 组件属性
export interface LawyerFirmProps {
  // 律师信息
  lawyerInfo: LawyerAPI.LawyerInfo
  // 自定义标题
  title?: string
  // 自定义信息项配置
  infoItems?: FirmInfoItem[]
  // 自定义样式类名
  className?: string
}

const LawyerFirm: React.FC<LawyerFirmProps> = ({
  lawyerInfo,
  title = '律所信息',
  infoItems,
  className = ''
}) => {
  // 默认信息项配置
  const defaultInfoItems: FirmInfoItem[] = [
    {
      label: '执业律所',
      value: lawyerInfo.lawFirm,
      key: 'lawFirm'
    },
    {
      label: '律所地址',
      value: lawyerInfo.lawFirmAddress,
      key: 'lawFirmAddress'
    }
  ]

  const displayItems = infoItems || defaultInfoItems

  return (
    <View className={`lawyer-firm ${className}`}>
      <Text className='lawyer-firm__title'>{title}</Text>
      {displayItems.map((item) => (
        <View key={item.key} className='lawyer-firm__item'>
          <Text className='lawyer-firm__label'>{item.label}</Text>
          <Text className='lawyer-firm__value'>{item.value}</Text>
        </View>
      ))}
    </View>
  )
}

export default LawyerFirm
