/**
 * 选择器字段组件
 */
import React from 'react'
import { View, Text, Picker } from '@tarojs/components'
import type { SelectFieldProps } from '../../types'
import './index.scss'

const SelectField: React.FC<SelectFieldProps> = ({
  label,
  options,
  value,
  onChange,
  placeholder = '请选择',
  required = false,
  loading = false,
  disabled = false,
  className = ''
}) => {
  // 获取选中项的索引
  const getSelectedIndex = (): number => {
    if (!value) return -1
    return options.findIndex(option => option.id === value)
  }

  // 获取显示文本
  const getDisplayText = (): string => {
    if (loading) return '加载中...'
    if (!value) return placeholder
    
    const selectedOption = options.find(option => option.id === value)
    return selectedOption ? selectedOption.name : placeholder
  }

  // 处理选择变化
  const handleChange = (e: any) => {
    const index = e.detail.value
    if (index >= 0 && index < options.length) {
      onChange(options[index].id)
    }
  }

  // 构建选择器的选项数组
  const pickerOptions = options.map(option => option.name)

  return (
    <View className={`select-field ${className}`}>
      <View className='field-label'>
        {required && <Text className='required-mark'>*</Text>}
        <Text className='label-text'>{label}</Text>
      </View>
      
      <View className='field-content'>
        <Picker
          mode='selector'
          range={pickerOptions}
          value={getSelectedIndex()}
          onChange={handleChange}
          disabled={disabled || loading || options.length === 0}
        >
          <View className={`select-trigger ${!value ? 'placeholder' : ''} ${loading || disabled ? 'loading' : ''}`}>
            <Text className='select-text'>{getDisplayText()}</Text>
            <View className='arrow-icon'>
              <Text className='arrow'>›</Text>
            </View>
          </View>
        </Picker>
      </View>
    </View>
  )
}

export default SelectField
