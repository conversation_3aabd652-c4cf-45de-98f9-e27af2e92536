/**
 * 关于我们页面
 */
import React, { useEffect, useState } from 'react'
import { View, Text } from '@tarojs/components'
import Taro from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'
import CacheManager, { CACHE_KEYS } from '@/utils/cache'
import { makePhoneCall } from '@/utils'
import './index.scss'

const AboutUs: React.FC = () => {
  const [phone, setPhone] = useState('')
  // 拨打电话
  const handleCallPhone = () => {
    try {
      makePhoneCall(phone)
    } catch (error) {
      console.error('拨打电话失败:', error)
      Taro.showToast({
        title: '拨打失败',
        icon: 'none'
      })
    }
  }
  useEffect(() => {
    const hotlinePhone = CacheManager.getCache<CommonAPI.MiniConfig>(CACHE_KEYS.MINI_CONFIG)?.hotlinePhone || ''
    setPhone(hotlinePhone)
  }, [])

  return (
    <View className='about-us-page'>
      <PageLayout
        title='关于我们'
        showBack
        backgroundColor='#f8f9fa'
        showFloatingMenu={false}
      >
        <PageContent>
          <View className='about-us-content'>
            {/* 头部标语 */}
            <View className='hero-section'>
              <View className='hero-slogan'>
                <Text className='slogan-main'>无辩胜有辩，刑辩在庭前！</Text>
                <Text className='slogan-sub'>—————刑事司法改革后的刑辩业务重构</Text>
              </View>
            </View>

            {/* 公司介绍 */}
            <View className='intro-section'>
              <Text className='intro-text'>
                在刑事辩护领域，&quot;选择困境&quot;&quot;信任危机&quot;&quot;评价真空&quot;三大行业顽疾困扰法律界数十年。中世无辩（武汉）信息技术有限公司以&quot;无辩胜有辩，刑辩在庭前&quot;为核心理念，用科技力量重构刑辩服务生态链。
              </Text>
            </View>

            {/* 三重革新 */}
            <View className='innovation-section'>
              <View className='section-header'>
                <Text className='section-title'>破局行业痼疾的三重革新</Text>
              </View>

              <View className='innovation-list'>
                <View className='innovation-item'>
                  <View className='innovation-header'>
                    <View className='innovation-icon'></View>
                    <Text className='innovation-title'>「无辩胜有辩」——以专业积淀破解选择困局</Text>
                  </View>
                  <Text className='innovation-desc'>
                    首创「刑辩律师能力矩阵评估系统」，基于20万+刑事案件数据库，通过36维度算法精准匹配&quot;五对应&quot;（地域对应、罪名对应、审级对应、程序对应、效果对应）专业律师，让当事人一键触达最适合案件的&quot;关键先生&quot;。
                  </Text>
                </View>

                <View className='innovation-item'>
                  <View className='innovation-header'>
                    <View className='innovation-icon'></View>
                    <Text className='innovation-title'>「信任可视化」——构建双向履约保障体系</Text>
                  </View>
                  <Text className='innovation-desc'>
                    研发「刑案全流程监管平台」，通过里程碑节点验收、第三方资金存管、双向评价系统，彻底解决&quot;百万律师费打水漂&quot;&quot;律师劳动无保障&quot;的信任困局，让每份投入都有数字见证。
                  </Text>
                </View>

                <View className='innovation-item'>
                  <View className='innovation-header'>
                    <View className='innovation-icon'></View>
                    <Text className='innovation-title'>「实效论英雄」——建立刑辩服务国家标准</Text>
                  </View>
                  <Text className='innovation-desc'>
                    打造「刑辩实效评价标尺」，依据无罪率、改判率、量刑优化率等12项硬核指标，建立动态更新的&quot;中国刑辩律师红榜&quot;，以数字化考评终结&quot;网红律师&quot;与&quot;实务专家&quot;的认知混乱。
                  </Text>
                </View>
              </View>
            </View>

            {/* 平台优势 */}
            <View className='advantage-section'>
              <View className='section-header'>
                <Text className='section-title'>与传统平台的本质分野</Text>
                <Text className='section-subtitle'>当其他平台仍在做&quot;律师黄页&quot;时，我们已构建刑辩领域的&quot;智慧大脑&quot;</Text>
              </View>

              <View className='advantage-list'>
                <View className='advantage-item'>
                  <View className='advantage-label'>精准度革命</View>
                  <Text className='advantage-desc'>告别&quot;万金油律师库&quot;，建立覆盖436个刑事罪名的专家池</Text>
                </View>

                <View className='advantage-item'>
                  <View className='advantage-label'>信任链重构</View>
                  <Text className='advantage-desc'>引入区块链存证技术，实现委托过程全链可溯</Text>
                </View>

                <View className='advantage-item'>
                  <View className='advantage-label'>价值再定义</View>
                  <Text className='advantage-desc'>首创&quot;辩护方案预演系统&quot;，让庭前准备可视化、可量化</Text>
                </View>
              </View>
            </View>

            {/* 结语 */}
            <View className='conclusion-section'>
              <Text className='conclusion-text'>
                胜张仪·职经辩律师服务平台——不止于连接，更致力于赋能。我们相信：真正的刑辩高手，功夫在庭前；卓越的法律服务，胜负在谋略。让正义提前发声，让辩护回归专业，这就是&quot;无辩胜有辩，刑辩在庭前&quot;的科技诠释。
              </Text>
            </View>

            {/* 公司信息与联系方式 */}
            <View className='company-info-section'>
              <View className='section-header'>
                <Text className='section-title'>联系我们</Text>
              </View>

              <View className='company-info-card'>
                <View className='company-info-item'>
                  <View className='info-icon company-icon'></View>
                  <View className='info-content'>
                    <Text className='info-label'>公司名称</Text>
                    <Text className='info-value'>中世无辩（武汉）信息技术有限公司</Text>
                  </View>
                </View>

                <View className='company-info-item'>
                  <View className='info-icon address-icon'></View>
                  <View className='info-content'>
                    <Text className='info-label'>公司地址</Text>
                    <Text className='info-value'>湖北省武汉市洪山区文化大道555号融创智谷B2栋1006室</Text>
                  </View>
                </View>

                <View className='company-info-item phone-item' onClick={handleCallPhone}>
                  <View className='info-icon phone-icon'></View>
                  <View className='info-content'>
                    <Text className='info-label'>联系电话</Text>
                    <Text className='info-value'>{phone}</Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </PageContent>
      </PageLayout>
    </View>
  )
}

export default AboutUs
