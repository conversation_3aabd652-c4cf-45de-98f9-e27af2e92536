/**
 * 律师动态列表组件
 * 
 * 展示最多5个动态卡片，支持横向滑动，支持外部传入筛选条件
 */
import React, { useState, useEffect } from 'react'
import { View, ScrollView, Text } from '@tarojs/components'
import { dynamicsApi } from '@/utils/request/apis'
import { STATUS_CODE } from '@/utils/request/config'
import { navigateToPage , formatTime } from '@/utils'
import { DynamicsListProps } from './types'
import './index.scss'

const DynamicsList: React.FC<DynamicsListProps> = ({
  filterParams,
  list,
  className = ''
}) => {
  // 动态列表数据
  const [dynamics, setDynamics] = useState<DynamicsAPI.DynamicsListInfo[]>([])
  // 加载状态
  const [isLoading, setIsLoading] = useState(false)



  // 加载动态列表数据
  const loadDynamics = async (val?: DynamicsAPI.GetDynamicsListRequest) => {
    try {
      setIsLoading(true)
      const params = {
        page: 1,
        pageSize: 5, // 最多展示5个动态
        ...val
      }

      const response = await dynamicsApi.getDynamicsList(params)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setDynamics(response.data.list || [])
      }
    } catch (error) {
      console.error('加载动态列表失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理动态卡片点击 - 跳转到动态详情页
  const handleDynamicsClick = (dynamicsInfo: DynamicsAPI.DynamicsListInfo) => {
    navigateToPage(`/subpackages/detail/dynamics/index?id=${dynamicsInfo.id}`)
  }

  // 使用外部传入的数据
  useEffect(() => {
    if (list) {
      setDynamics(list)
      setIsLoading(false)
    }
  }, [list])

  // 监听filterParams变化，重新加载数据（仅在没有传入list时）
  useEffect(() => {
    if (!list) {
      loadDynamics(filterParams)
    }
  }, [filterParams, list])

  // 显示加载状态
  const showLoading = isLoading

  return (
    <View className={`dynamics-list ${className}`}>
      {showLoading ? (
        <View className='dynamics-list__loading'>
          <Text>加载中...</Text>
        </View>
      ) : (
        <>
          {dynamics.length > 0 ? (
            <ScrollView
              className='dynamics-list__scroll-view'
              scrollX
              showScrollbar={false}
              enhanced
              bounces={false}
            >
              <View className='dynamics-list__container'>
                {dynamics.map((dynamicsInfo) => (
                  <View
                    key={dynamicsInfo.id}
                    className='dynamics-card'
                    onClick={() => handleDynamicsClick(dynamicsInfo)}
                  >
                    <Text className='dynamics-card__title'>{dynamicsInfo.title}</Text>
                    <View className='dynamics-card__meta'>
                      <Text className='dynamics-card__category'>{dynamicsInfo.categoryName}</Text>
                      <View className='dynamics-card__info'>
                        <Text className='dynamics-card__time'>{formatTime(dynamicsInfo.createdAt)}</Text>
                        <Text className='dynamics-card__view-count'>浏览 {dynamicsInfo.viewCount}</Text>
                      </View>
                    </View>
                  </View>
                ))}
              </View>
            </ScrollView>
          ) : (
            <View className='dynamics-list__empty'>
              <Text className='dynamics-list__empty-title'>暂无动态信息</Text>
              <Text className='dynamics-list__empty-desc'>当前没有相关动态，请稍后再试</Text>
            </View>
          )}
        </>
      )}
    </View>
  )
}

// 导出组件
export default DynamicsList

// 导出类型
export type { DynamicsListProps } from './types'
