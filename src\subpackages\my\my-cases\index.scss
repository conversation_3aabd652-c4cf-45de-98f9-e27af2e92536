.my-cases {
  height: calc(100vh - 200rpx);
  display: flex;
  flex-direction: column;

  .my-cases-list-container {
    flex: 1;
    overflow: hidden;
    min-height: 0; // 添加这行，确保flex子元素可以收缩
  }

  .my-cases-list {
    height: 100%; // 添加明确高度
    background: #f8f9fa;

    .my-cases-loading-more,
    .my-cases-no-more,
    .my-cases-empty-state {
      text-align: center;
      padding: 60rpx 30rpx;
      color: #999;
      font-size: 28rpx;
    }

    .my-cases-empty-state {
      padding: 120rpx 30rpx;
    }
  }
}