/**
 * 浮动操作菜单组件
 * 
 * 提供客服和申请委托等快捷操作
 */
import React from 'react'
import Taro from '@tarojs/taro'
import { View, Text, Image } from '@tarojs/components'
// 导入浮动按键图标
import menuIcon from '@/assets/images/common-icon/menu.png'
import clickRightIcon from '@/assets/images/common-icon/click_right.png'
import headsetOutlineIcon from '@/assets/images/common-icon/headset_outline.png'
import editIcon from '@/assets/images/common-icon/edit.png'
import { makePhoneCall } from '@/utils/helpers/wechat'
import CacheManager, { CACHE_KEYS } from '@/utils/cache'

import './index.scss'

// 菜单项配置
interface MenuItem {
  key: string
  label: string
  icon: string
  onClick: () => void
}

// 组件属性
export interface FloatingMenuProps {
  // 是否展开
  isExpanded: boolean
  // 是否正在动画中
  isAnimating: boolean
  // 菜单切换回调
  onToggle: () => void
  // 自定义菜单项
  menuItems?: MenuItem[]
  // 自定义样式类名
  className?: string
}

const FloatingMenu: React.FC<FloatingMenuProps> = ({
  isExpanded,
  isAnimating,
  onToggle,
  menuItems,
  className = ''
}) => {
  // 默认菜单项
  const defaultMenuItems: MenuItem[] = [
    {
      key: 'customer-service',
      label: '平台客服',
      icon: headsetOutlineIcon,
      onClick: () => {
        const hotlinePhone = CacheManager.getCache<CommonAPI.MiniConfig>(CACHE_KEYS.MINI_CONFIG)?.hotlinePhone || ''
        makePhoneCall(hotlinePhone)
        onToggle() // 点击后收起菜单
      }
    },
    {
      key: 'apply-delegate',
      label: '申请委托',
      icon: editIcon,
      onClick: () => {
        Taro.navigateTo({
          url: '/subpackages/common/golden-rescue/index?type=2'
        })
        onToggle() // 点击后收起菜单
      }
    }
  ]

  const displayMenuItems = menuItems || defaultMenuItems

  return (
    <View className={`floating-menu ${className}`}>
      {(isExpanded || isAnimating) && (
        <View className={`${isAnimating ? 'floating-menu__collapsed' : 'floating-menu__expanded'}`}>
          <View className='floating-menu__item floating-menu__item--close' onClick={onToggle}>
            <Image
              className='floating-menu__close'
              src={clickRightIcon}
              mode='aspectFit'
            />
          </View>
          {displayMenuItems.map((item) => (
            <View key={item.key} className='floating-menu__item' onClick={item.onClick}>
              <Image
                className='floating-menu__icon'
                src={item.icon}
                mode='aspectFit'
              />
              <Text className='floating-menu__text'>{item.label}</Text>
            </View>
          ))}
        </View>
      )}

      {!isExpanded && !isAnimating && (
        <View
          className='floating-menu__button'
          onClick={onToggle}
        >
          <Image
            className='floating-menu__button-icon'
            src={menuIcon}
            mode='aspectFit'
          />
        </View>
      )}
    </View>
  )
}

export default FloatingMenu
