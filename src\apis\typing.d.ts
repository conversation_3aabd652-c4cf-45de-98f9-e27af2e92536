/**
 * 请求库全局类型声明文件
 *
 * 此文件统一导入所有类型声明，避免重复引用
 */

// 导入通用类型（必须最先导入）
/// <reference path="../common-types.d.ts" />

// 导入核心请求类型
/// <reference path="./types.ts" />

// 导入各API模块的类型声明
/// <reference path="./user/typing.d.ts" />
/// <reference path="./lawyer/typing.d.ts" />
/// <reference path="./case/typing.d.ts" />
/// <reference path="./article/typing.d.ts" />
/// <reference path="./dynamics/typing.d.ts" />
/// <reference path="./upload/typing.d.ts" />
/// <reference path="./common/typing.d.ts" />

// 导出空对象以使此文件成为模块
export {}
