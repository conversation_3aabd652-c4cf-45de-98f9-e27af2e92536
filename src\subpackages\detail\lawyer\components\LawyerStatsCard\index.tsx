/**
 * 律师数据统计卡片组件
 * 
 * 基于 LawyerCountDataDto 类型设计，展示律师的办案统计数据
 */
import React from 'react'
import { View, Text } from '@tarojs/components'
import './index.scss'

// 组件属性
export interface LawyerStatsCardProps {
  // 律师统计数据
  lawyerCountData: LawyerAPI.LawyerCountDataDto | null
  // 自定义标题
  title?: string
  // 自定义样式类名
  className?: string
  // 是否显示加载状态
  loading?: boolean
  // 自定义主要数据标签
  mainLabels?: {
    caseCount?: string
    caseCategoryGroup?: string
  }
  // 自定义案件类型区域标签
  categoryLabel?: string
}

const LawyerStatsCard: React.FC<LawyerStatsCardProps> = ({
  lawyerCountData,
  title = '律师数据',
  className = '',
  loading = false,
  mainLabels = {
    caseCount: '办案案件',
    caseCategoryGroup: '办案经验涵盖罪名数量'
  },
  categoryLabel = '办理最多案件类型'
}) => {
  
  // 格式化数字显示
  const formatNumber = (num: number): string => {
    if (num >= 10000) {
      return `${(num / 10000).toFixed(1)}万`
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`
    }
    return num.toString()
  }
  const topCategoryStr = (lawyerCountData?.topCategory || []).join('、')
  
  return (
    <View className={`lawyer-stats-card ${className}`}>
      <Text className='lawyer-stats-card__title'>{title}</Text>

      {loading ? (
        <View className='lawyer-stats-card__loading'>
          <View className='lawyer-stats-card__loading-main'>
            <View className='lawyer-stats-card__loading-item' />
            <View className='lawyer-stats-card__loading-item' />
          </View>
          <View className='lawyer-stats-card__loading-category' />
        </View>
      ) : (
        <>
          {/* 主要数据 */}
          <View className='lawyer-stats-card__main'>
            <View className='lawyer-stats-card__main-item'>
              <View className='lawyer-stats-card__main-number'>
                <Text className='lawyer-stats-card__main-number-text'>
                  {formatNumber(lawyerCountData?.caseCount || 0)}
                </Text>
                <Text className='lawyer-stats-card__main-number-unit'>件</Text>
              </View>
              <Text className='lawyer-stats-card__main-label'>
                {mainLabels.caseCount}
              </Text>
            </View>
            <View className='lawyer-stats-card__main-item'>
              <View className='lawyer-stats-card__main-number'>
                <Text className='lawyer-stats-card__main-number-text'>
                  {formatNumber(lawyerCountData?.caseCategoryGroup || 0)}
                </Text>
                <Text className='lawyer-stats-card__main-number-unit'>项</Text>
              </View>
              <Text className='lawyer-stats-card__main-label'>
                {mainLabels.caseCategoryGroup}
              </Text>
            </View>
          </View>

          {/* 办理最多案件类型 */}
          {lawyerCountData?.topCategory && lawyerCountData.topCategory.length > 0 && (
            <View className='lawyer-stats-card__category'>
              <Text className='lawyer-stats-card__category-label'>{categoryLabel}</Text>
              <View className='lawyer-stats-card__category-tags'>
                <Text className='lawyer-stats-card__category-tag'                  >
                  {topCategoryStr}
                </Text>
              </View>
            </View>
          )}
        </>
      )}
    </View>
  )
}

export default LawyerStatsCard
