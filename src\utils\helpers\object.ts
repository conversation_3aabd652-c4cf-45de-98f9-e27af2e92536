/**
 * 对象相关工具函数
 */

/**
 * 对象深度合并
 * @param target 目标对象
 * @param sources 源对象
 */
export const deepMerge = <T extends Record<string, any>>(
  target: T,
  ...sources: Partial<T>[]
): T => {
  if (!sources.length) return target

  const source = sources.shift()
  if (!source) return target

  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      if (!target[key] || typeof target[key] !== 'object') {
        target[key] = {} as any
      }
      deepMerge(target[key], source[key])
    } else {
      target[key] = source[key] as any
    }
  }

  return deepMerge(target, ...sources)
}

/**
 * 获取对象深层属性
 * @param obj 对象
 * @param path 属性路径
 * @param defaultValue 默认值
 */
export const getDeepProperty = <T = any>(
  obj: any,
  path: string,
  defaultValue?: T
): T => {
  const keys = path.split('.')
  let result = obj

  for (const key of keys) {
    if (result === null || result === undefined || !(key in result)) {
      return defaultValue as T
    }
    result = result[key]
  }

  return result as T
}

/**
 * 设置对象深层属性
 * @param obj 对象
 * @param path 属性路径
 * @param value 值
 */
export const setDeepProperty = (obj: any, path: string, value: any): void => {
  const keys = path.split('.')
  const lastKey = keys.pop()!
  let current = obj

  for (const key of keys) {
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {}
    }
    current = current[key]
  }

  current[lastKey] = value
}

/**
 * 过滤对象属性
 * @param obj 对象
 * @param predicate 过滤函数
 */
export const filterObject = <T extends Record<string, any>>(
  obj: T,
  predicate: (key: string, value: any) => boolean
): Partial<T> => {
  const result: Partial<T> = {}

  for (const [key, value] of Object.entries(obj)) {
    if (predicate(key, value)) {
      result[key as keyof T] = value
    }
  }

  return result
}
