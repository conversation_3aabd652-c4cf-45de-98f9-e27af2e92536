/**
 * 案例相关接口
 * 基于 api.json 接口文档定义
 */
import request from '@/utils/request/index'

// 案例相关接口
export const caseApi = {
  // 获取案例列表（基于 /mini/find-law-case/list）
  getCaseList: (params?: CaseAPI.GetCaseListRequest) =>
    request.get<CaseAPI.GetCaseListResponse>('/mini/find-law-case/list', params),

  // 获取案例详情（基于 /mini/find-law-case/:caseId/detail）
  getCaseDetail: (caseId: number) =>
    request.get<CaseAPI.GetCaseDetailResponse>(`/mini/find-law-case/${caseId}/detail`),

  // 获取推荐案例列表（基于 /mini/find-law-case/:caseId/recommend-list）
  getRecommendCaseList: (caseId: number, params?: CaseAPI.GetRecommendCaseListRequest) =>
    request.get<CaseAPI.GetRecommendCaseListResponse>(`/mini/find-law-case/${caseId}/recommend-list`, { ...params, caseId }),

  // 获取我的案例列表（律师个人中心）
  getMyCaseList: (params?: CaseAPI.GetMyCaseListRequest) =>
    request.get<CaseAPI.GetMyCaseListResponse>('/mini/owner-center-lawyer/law-case/list', params),

  // 获取我的案例详情（律师个人中心）
  getMyCaseDetail: (caseId: number) =>
    request.get<CaseAPI.GetMyCaseDetailResponse>(`/mini/owner-center-lawyer/law-case/${caseId}/detail`),

  // 发布案例（律师个人中心）
  publishCase: (data: CaseAPI.PublishCaseRequest) =>
    request.post<CaseAPI.PublishCaseResponse>('/mini/owner-center-lawyer/law-case/add', data),

  // 修改案例（律师个人中心）
  updateMyCase: (caseId: number, data: CaseAPI.UpdateMyCaseRequest) =>
    request.post<CaseAPI.UpdateMyCaseResponse>(`/mini/owner-center-lawyer/law-case/${caseId}/save`, data),
}
