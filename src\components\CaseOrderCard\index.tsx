/**
 * 通用案件订单卡片组件
 * 
 * 支持委托人和律师两种视角的案件订单展示
 */
import React from 'react'
import { View, Text } from '@tarojs/components'
import {
  getOrderStatusText,
  getOrderStatusClass,
  getPaymentStatusText,
  getPaymentStatusClass,
  formatAmount
} from '@/constant'
import './index.scss'

// 通用案件订单信息接口
export interface CaseOrderInfo {
  id: number
  orderNo?: string
  clientName?: string
  caseTypeName?: string
  caseStage?: string
  handlingAgency?: string
  province?: string
  city?: string
  district?: string
  orderStatus?: number
  amountInvolvedOfCase?: number
  paymentStatus?: number
  paymentAmount?: number
  lawyerId?: number
  lawyerName?: string
  lawyerRequirements?: string
  createdAt: string
  [key: string]: any
}

// 组件属性
export interface CaseOrderCardProps {
  // 案件订单信息
  caseOrder: CaseOrderInfo
  // 视角类型：member(委托人) 或 lawyer(律师)
  viewType?: 'member' | 'lawyer'
  // 点击回调
  onClick?: (caseOrder: CaseOrderInfo) => void
  // 自定义样式类名
  className?: string
}

const CaseOrderCard: React.FC<CaseOrderCardProps> = ({
  caseOrder,
  viewType = 'member',
  onClick,
  className = ''
}) => {

  // 处理点击事件
  const handleClick = () => {
    onClick?.(caseOrder)
  }

  return (
    <View
      className={`case-order-card ${className}`}
      onClick={handleClick}
    >
      {/* 订单号和状态 */}
      <View className='case-order-card__header'>
        <View className='case-order-card__order-info'>
          <Text className='case-order-card__order-no'>
            订单号：{caseOrder.orderNo || caseOrder.id}
          </Text>
        </View>
        <Text className={`case-order-card__order-status ${getOrderStatusClass(caseOrder.orderStatus || 1)}`}>
          {getOrderStatusText(caseOrder.orderStatus || 1)}
        </Text>
      </View>

      {/* 案件详细信息 */}
      <View className='case-order-card__content'>
          <View className='case-order-card__info-row'>
            <Text className='case-order-card__info-label'>委托人</Text>
            <Text className='case-order-card__info-value'>{caseOrder.clientName || '未知'}</Text>
          </View>

        {/* 案件类型 */}
        {caseOrder.caseTypeName && (
          <View className='case-order-card__info-row'>
            <Text className='case-order-card__info-label'>案件类型</Text>
            <Text className='case-order-card__info-value'>{caseOrder.caseTypeName}</Text>
          </View>
        )}

        {/* 案件阶段 */}
        <View className='case-order-card__info-row'>
          <Text className='case-order-card__info-label'>案件阶段</Text>
          <Text className='case-order-card__info-value'>{caseOrder.caseStage || '未知阶段'}</Text>
        </View>

        {/* 办理机构 */}
        {caseOrder.handlingAgency && (
          <View className='case-order-card__info-row'>
            <Text className='case-order-card__info-label'>办理机构</Text>
            <Text className='case-order-card__info-value'>{caseOrder.handlingAgency}</Text>
          </View>
        )}

        {/* 地区信息 */}
        {(caseOrder.province || caseOrder.city || caseOrder.district) && (
          <View className='case-order-card__info-row'>
            <Text className='case-order-card__info-label'>所在地区</Text>
            <Text className='case-order-card__info-value'>
              {`${caseOrder.province || ''}${caseOrder.city || ''}${caseOrder.district || ''}`}
            </Text>
          </View>
        )}

        {/* 负责律师 - 仅委托人视角显示 */}
        {viewType === 'member' && caseOrder.lawyerName && (
          <View className='case-order-card__info-row'>
            <Text className='case-order-card__info-label'>负责律师</Text>
            <Text className='case-order-card__info-value'>{caseOrder.lawyerName}</Text>
          </View>
        )}

        {/* 案件涉及金额 */}
        {caseOrder.amountInvolvedOfCase !== undefined && (
          <View className='case-order-card__info-row'>
            <Text className='case-order-card__info-label'>涉及金额</Text>
            <Text className='case-order-card__info-value'>{formatAmount(caseOrder.amountInvolvedOfCase)}</Text>
          </View>
        )}

        {/* 支付金额和支付状态 - 仅委托人视角显示 */}
        {viewType === 'member' && caseOrder.paymentAmount !== undefined && (
          <View className='case-order-card__info-row'>
            <Text className='case-order-card__info-label'>支付金额</Text>
            <View className='case-order-card__payment-info'>
              <Text className='case-order-card__info-value'>{formatAmount(caseOrder.paymentAmount)}</Text>
              <Text className={`case-order-card__payment-status ${getPaymentStatusClass(caseOrder.paymentStatus || 1)}`}>
                {getPaymentStatusText(caseOrder.paymentStatus || 1)}
              </Text>
            </View>
          </View>
        )}

        {/* 律师要求 */}
        {caseOrder.lawyerRequirements && (
          <View className='case-order-card__info-row'>
            <Text className='case-order-card__info-label'>律师要求</Text>
            <Text className='case-order-card__info-value case-order-card__requirements'>
              {caseOrder.lawyerRequirements}
            </Text>
          </View>
        )}

        {/* 创建时间 */}
        <View className='case-order-card__info-row'>
          <Text className='case-order-card__info-label'>创建时间</Text>
          <Text className='case-order-card__info-value'>{caseOrder.createdAt}</Text>
        </View>
      </View>
    </View>
  )
}

export default CaseOrderCard
