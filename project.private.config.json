{"libVersion": "3.8.9", "setting": {"urlCheck": true, "preloadBackgroundData": false}, "condition": {"miniprogram": {"list": [{"name": "律师案件列表", "pathName": "subpackages/lawyer/lawyer-case-orders/index", "query": "", "scene": null, "launchMode": "default"}, {"name": "律师案件详情", "pathName": "subpackages/lawyer/lawyer-case-detail/index", "query": "id=15", "launchMode": "default", "scene": null}, {"name": "律师案件列表", "pathName": "subpackages/lawyer/lawyer-case-orders/index", "query": "", "launchMode": "default", "scene": null}, {"name": "公司简介", "pathName": "subpackages/common/company-profile/index", "query": "", "launchMode": "default", "scene": null}, {"name": "律师详情", "pathName": "subpackages/detail/lawyer/index", "query": "lawyerId=20", "launchMode": "default", "scene": null}, {"name": "关于我们", "pathName": "subpackages/common/about-us/index", "query": "", "launchMode": "default", "scene": null}, {"name": "会员案件详情", "pathName": "subpackages/my/my-case-detail/index", "query": "id=14", "launchMode": "default", "scene": null}, {"name": "黄金救援", "pathName": "subpackages/common/golden-rescue/index", "query": "type=1", "launchMode": "default", "scene": null}, {"name": "文章详情", "pathName": "subpackages/detail/article/index", "query": "id=9", "launchMode": "default", "scene": null}, {"name": "动态详情", "pathName": "subpackages/detail/dynamics/index", "query": "id=10", "launchMode": "default", "scene": null}, {"name": "我的案件", "pathName": "subpackages/my/my-cases/index", "query": "", "launchMode": "default", "scene": null}, {"name": "律师案例管理", "pathName": "subpackages/lawyer/lawyer-case-manage/index", "query": "lawyerId=1", "launchMode": "default", "scene": null}, {"name": "案例详情", "pathName": "subpackages/detail/case/index", "query": "id=1", "launchMode": "default", "scene": null}, {"name": "优秀案例", "pathName": "subpackages/common/excellent-cases/index", "query": "", "launchMode": "default", "scene": null}, {"name": "我的收藏", "pathName": "subpackages/my/my-follow/index", "query": "", "launchMode": "default", "scene": null}, {"name": "律师", "pathName": "pages/lawyer/index", "query": "userId=4", "launchMode": "default", "scene": null}, {"name": "案例", "pathName": "pages/case/index", "query": "userId=4", "launchMode": "default", "scene": null}, {"name": "我的", "pathName": "pages/mine/index", "query": "userId=4", "launchMode": "default", "scene": null}, {"name": "律师名片", "pathName": "subpackages/detail/lawyer/index", "query": "userId=4", "launchMode": "default", "scene": null}, {"name": "登录", "pathName": "subpackages/login/index", "query": "lawyerId=1", "launchMode": "default", "scene": null}, {"name": "律师动态管理", "pathName": "subpackages/lawyer/lawyer-dynamics-manage/index", "query": "lawyerId=1", "launchMode": "default", "scene": null}, {"name": "律师文章管理", "pathName": "subpackages/lawyer/lawyer-article-manage/index", "query": "lawyerId=1", "launchMode": "default", "scene": null}, {"name": "文章收藏", "pathName": "subpackages/my/article-collection/index", "query": "", "launchMode": "default", "scene": null}, {"name": "律师入驻", "pathName": "subpackages/lawyer/lawyer-entry/index", "query": "", "launchMode": "default", "scene": null}]}}}