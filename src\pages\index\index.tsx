import React, { useState } from 'react'
import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'
import Banner, { BannerItem } from '@/components/Banner'
import LawyerList from '@/components/LawyerList'
import HorizontalCaseList from '@/components/HorizontalCaseList'
import Service from '@/components/Service'
import { userApi } from '@/utils/request/apis/user'
import { STATUS_CODE } from '@/utils/request/config'

import { Logo, HomeBg } from '@/constant/image'
import BookMark from '@/assets/images/common-icon/book_mark_color.png'
import VerifyIcon from '@/assets/images/common-icon/verify.png'

import HomeNav from './components/HomeNav'
import './index.scss'

const Index: React.FC = () => {
  // 登录状态管理
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [isLawyer, setIsLawyer] = useState(false) // 是否为律师用户
  const banners: BannerItem[] = [
    {
      id: 'home-banner-1',
      imageUrl: HomeBg,
      linkUrl: '/subpackages/common/company-profile/index'
    }
  ]

  const getUserData = async () => {
    try {
      // 先获取会员基础信息
      const memberResponse = await userApi.getMemberInfo({ showError: false })
      if (memberResponse.code === STATUS_CODE.SUCCESS && memberResponse.data) {
        // 接口成功，设置为已登录状态
        setIsLoggedIn(true)
        // 根据用户类型判断是否需要获取律师信息
        if (memberResponse.data.type === 2) {
          setIsLawyer(true)
        } else {
          setIsLawyer(false)
        }
      } else {
        // 用户类型为普通用户
        setIsLawyer(false)
      }
    } catch (error) {
      console.log('获取用户数据失败，判断为未登录:', error)
      // 请求失败，判断为未登录
      setIsLoggedIn(false)
      setIsLawyer(false)
    }
  }
  const onClickMoreLawyer = () => {
    Taro.switchTab({
      url: '/pages/lawyer/index'
    })
  }

  const onClickMoreCase = () => {
    Taro.switchTab({
      url: '/pages/case/index'
    })
  }

  // 页面生命周期
  Taro.useDidShow(() => {
    getUserData()
  })

  return (
    <PageLayout
      showLogo
      showBack={false}
      logoSrc={Logo}
      backgroundColor='#f8f9fa'
      showFloatingMenu={false}
    >
      <PageContent padded='b'>
        <Banner
          banners={banners}
          autoplay={false}
          showIndicators={false}
          showDots={false}
          circular={false}
          className='home-banner'
        />
        <HomeNav isLoggedIn={isLoggedIn} isLawyer={isLawyer} />
        <View className='home-content-padded home-lawyer-list'>
          <View className='home-lawyer-list__header'>
            <View className='home-lawyer-list__title'>
              <Image className='home-lawyer-list__title-icon' src={VerifyIcon} mode='aspectFit' />
              <Text className='home-lawyer-list__title-text'>品牌律师</Text>
            </View>
            <View className='home-lawyer-list__more' onClick={onClickMoreLawyer}>
              <Text>更多</Text>
            </View>
          </View>
          <LawyerList filterParams={{ lawyerLevel: [3,4,5] }} />
        </View>
        <View className='home-case-list'>
          <View className='home-case-list__header'>
            <View className='home-case-list__title'>
              <Image className='home-case-list__title-icon' src={BookMark} mode='aspectFit' />
              <Text className='home-case-list__title-text'>最新亲办案例</Text>
            </View>
            <View className='home-case-list__more' onClick={onClickMoreCase}>
              <Text>更多</Text>
            </View>
          </View>
          <HorizontalCaseList />
        </View>
        <Service />
      </PageContent>
    </PageLayout>
  )
}

export default Index
