/**
 * Tab导航相关样式
 */

.lawyer-detail {
  // Tab导航
  &__tabs {
    display: flex;
    background-color: #ffffff;
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    padding: 10rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  }

  &__tab {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    border-radius: 15rpx;
    transition: all 0.3s ease;

    &--active {
      background-color: #BD8A4F;
    }
  }

  &__tab-text {
    font-size: 28rpx;
    font-weight: 500;
    color: #666666;

    .lawyer-detail__tab--active & {
      color: #ffffff;
      font-weight: 600;
    }
  }
}
