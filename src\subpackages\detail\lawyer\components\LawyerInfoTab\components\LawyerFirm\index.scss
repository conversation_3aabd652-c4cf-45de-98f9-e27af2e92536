/**
 * 律所信息组件样式
 */

.lawyer-firm {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

  &__title {
    font-size: 32rpx;
    font-weight: 600;
    color: #000000;
    display: block;
  }

  &__item {
    margin-top: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    transition: all 0.2s ease;

    &:last-child {
      padding-top: 20rpx;
      border-top: 1rpx solid #eee;
      margin-bottom: 0;
    }

    &--clickable {
      
      padding: 10rpx;
      margin: -10rpx -10rpx 10rpx -10rpx;
      border-radius: 10rpx;

      &:hover {
        background-color: #F5F5F5;
      }

      &:active {
        background-color: #E8E8E8;
      }

      &:last-child {
        margin-bottom: -10rpx;
      }
    }
  }

  &__label {
    font-size: 24rpx;
    color: #666666;
    font-weight: 400;
    display: block;
  }

  &__value {
    font-size: 26rpx;
    color: #333333;
    font-weight: 500;
    text-align: right;
    flex: 1;
    margin-left: 20rpx;
    display: block;
  }
}
