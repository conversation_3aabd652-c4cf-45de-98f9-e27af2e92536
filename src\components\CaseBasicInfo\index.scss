/**
 * 案件基本信息组件样式
 */

.case-basic-info {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  margin-bottom: 24rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);

  // 添加悬浮动画效果
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 30rpx;
    background: linear-gradient(90deg, rgba(189, 138, 79, 0.05) 0%, rgba(189, 138, 79, 0.02) 100%);
    border-bottom: 1rpx solid rgba(240, 240, 240, 0.6);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 30rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 6rpx;
      height: 32rpx;
      background: linear-gradient(180deg, #BD8A4F 0%, #d4a574 100%);
      border-radius: 3rpx;
    }
  }

  &__title {
    font-size: 34rpx;
    font-weight: 700;
    color: #2c3e50;
    margin-left: 20rpx;
    letter-spacing: 0.5rpx;
  }

  &__status-group {
    display: flex;
    align-items: center;
    gap: 12rpx;
  }

  &__order-status,
  &__payment-status {
    font-size: 22rpx;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-weight: 600;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.3);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      transition: left 0.5s;
    }

    &:hover::before {
      left: 100%;
    }
  }

  // 订单状态样式
  &__order-status {
    &.status-created {
      color: #ffffff;
      background: linear-gradient(135deg, #f39c12 0%, #f7c52d 100%);
      box-shadow: 0 4rpx 15rpx rgba(243, 156, 18, 0.3);
    }

    &.status-pending {
      color: #ffffff;
      background: linear-gradient(135deg, #9b59b6 0%, #bb8fce 100%);
      box-shadow: 0 4rpx 15rpx rgba(155, 89, 182, 0.3);
    }

    &.status-processing {
      color: #ffffff;
      background: linear-gradient(135deg, #3498db 0%, #5dade2 100%);
      box-shadow: 0 4rpx 15rpx rgba(52, 152, 219, 0.3);
    }

    &.status-completed {
      color: #ffffff;
      background: linear-gradient(135deg, #27ae60 0%, #58d68d 100%);
      box-shadow: 0 4rpx 15rpx rgba(39, 174, 96, 0.3);
    }

    &.status-cancelled {
      color: #ffffff;
      background: linear-gradient(135deg, #e74c3c 0%, #ec7063 100%);
      box-shadow: 0 4rpx 15rpx rgba(231, 76, 60, 0.3);
    }

    &.status-unknown {
      color: #ffffff;
      background: linear-gradient(135deg, #95a5a6 0%, #aab7b8 100%);
      box-shadow: 0 4rpx 15rpx rgba(149, 165, 166, 0.3);
    }
  }

  // 支付状态样式
  &__payment-status {
    &.payment-pending {
      color: #ffffff;
      background: linear-gradient(135deg, #e67e22 0%, #f4a261 100%);
      box-shadow: 0 4rpx 15rpx rgba(230, 126, 34, 0.3);
    }

    &.payment-completed {
      color: #ffffff;
      background: linear-gradient(135deg, #27ae60 0%, #58d68d 100%);
      box-shadow: 0 4rpx 15rpx rgba(39, 174, 96, 0.3);
    }

    &.payment-failed {
      color: #ffffff;
      background: linear-gradient(135deg, #e74c3c 0%, #ec7063 100%);
      box-shadow: 0 4rpx 15rpx rgba(231, 76, 60, 0.3);
    }

    &.payment-unknown {
      color: #ffffff;
      background: linear-gradient(135deg, #95a5a6 0%, #aab7b8 100%);
      box-shadow: 0 4rpx 15rpx rgba(149, 165, 166, 0.3);
    }
  }

  &__content {
    padding: 24rpx 30rpx;
    background: rgba(255, 255, 255, 0.5);
  }

  &__row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16rpx;
    min-height: 36rpx;
    padding: 8rpx 0;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__label {
    font-size: 26rpx;
    color: #666666;
    min-width: 140rpx;
    flex-shrink: 0;
    line-height: 36rpx;
    font-weight: 400;
  }

  &__value {
    font-size: 26rpx;
    color: #333333;
    flex: 1;
    line-height: 36rpx;
    word-break: break-all;
    font-weight: 400;
    margin-left: 16rpx;
  }

  &__requirements {
    margin-top: 20rpx;
    padding-top: 20rpx;
    border-top: 1rpx solid #c5c5c5;

    .case-basic-info__label {
      margin-bottom: 12rpx;
      line-height: 36rpx;
      font-weight: 400;
    }
  }

  &__requirements-text {
    font-size: 26rpx;
    color: #333333;
    line-height: 38rpx;
    word-break: break-all;
    font-weight: 400;
  }
}