/**
 * 数组相关工具函数
 */

/**
 * 数组去重
 * @param arr 数组
 * @param key 对象数组的去重键
 */
export const uniqueArray = <T,>(arr: T[], key?: keyof T): T[] => {
  if (!key) {
    return [...new Set(arr)]
  }

  const seen = new Set()
  return arr.filter(item => {
    const value = item[key]
    if (seen.has(value)) {
      return false
    }
    seen.add(value)
    return true
  })
}

/**
 * 数组分组
 * @param arr 数组
 * @param key 分组键
 */
export const groupBy = <T,>(arr: T[], key: keyof T): Record<string, T[]> => {
  return arr.reduce((groups, item) => {
    const group = String(item[key])
    if (!groups[group]) {
      groups[group] = []
    }
    groups[group].push(item)
    return groups
  }, {} as Record<string, T[]>)
}

/**
 * 数组排序
 * @param arr 数组
 * @param key 排序键
 * @param order 排序方向
 */
export const sortArray = <T,>(
  arr: T[],
  key: keyof T,
  order: 'asc' | 'desc' = 'asc'
): T[] => {
  return [...arr].sort((a, b) => {
    const aVal = a[key]
    const bVal = b[key]

    if (aVal < bVal) return order === 'asc' ? -1 : 1
    if (aVal > bVal) return order === 'asc' ? 1 : -1
    return 0
  })
}

/**
 * 数组分页
 * @param arr 数组
 * @param page 页码
 * @param pageSize 每页大小
 */
export const paginateArray = <T,>(
  arr: T[],
  page: number,
  pageSize: number
): {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
} => {
  const total = arr.length
  const totalPages = Math.ceil(total / pageSize)
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const data = arr.slice(start, end)

  return {
    data,
    total,
    page,
    pageSize,
    totalPages
  }
}
