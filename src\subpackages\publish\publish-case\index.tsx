/**
 * 案例发布/编辑页面
 */
import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { View, Textarea, Picker, Button, TextareaProps, CommonEventFunction } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import PageLayout from '@/components/PageLayout'
import ImageUploadField from '@/components/ImageUploadField'
import { caseApi } from '@/apis'
import CategoryCacheManager from '@/utils/cache/categoryCache'
import { STATUS_CODE } from '@/utils/request/config'
import { debounceForMiniProgram } from '@/utils/helpers/common'
import { removeImagesPrefix, parseImageUrls } from '@/utils'
import './index.scss'

interface FormDataType {
  title: string
  categoryId?: number
  content: string
  imageUrls?: string[]
}

const PublishCase: React.FC = () => {
  const router = useRouter()
  const { id } = router.params // 案例ID，编辑时传入

  // 页面状态
  const [isEdit, setIsEdit] = useState(false) // 是否为编辑模式
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)

  // 表单数据
  const [formData, setFormData] = useState<FormDataType>({
    title: '', // 案例标题
    categoryId: undefined, // 分类ID
    content: '', // 案例内容
    imageUrls: []
  })
  // 图片上传状态
  const [uploading, setUploading] = useState(false)
  // 分类数据
  const [categories, setCategories] = useState<CommonAPI.LawCaseCategoryDto[]>([])
  const [selectedCategoryIndex, setSelectedCategoryIndex] = useState(0)

  // 字数统计
  const [titleCount, setTitleCount] = useState(0)
  const [contentCount, setContentCount] = useState(0)
  const maxTitleWords = 100
  const maxContentWords = 5000

  // 计算按钮是否可用
  const isButtonDisabled = submitting || uploading || !formData.title.trim() || !formData.content.trim() || !formData.categoryId

  // 获取案例分类列表（使用缓存）
  const loadCategories = async () => {
    try {
      const categorieList = await CategoryCacheManager.getCaseCategoryList()
      setCategories(categorieList)
      if (categorieList.length > 0 && !isEdit) {
        setFormData(prev => ({
          ...prev,
          categoryId: categorieList[0].id
        }))
      }
    } catch (error) {
      console.error('获取分类列表失败:', error)
    }
  }

  // 获取案例详情（编辑模式）
  const loadCaseDetail = async (caseId: number) => {
    try {
      setLoading(true)
      const response = await caseApi.getMyCaseDetail(caseId)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        const detail = response.data
        setFormData({
          title: detail.title,
          categoryId: detail.categoryId,
          content: detail.content,
          imageUrls: detail.imagesContexts ?  parseImageUrls(detail.imagesContexts) : []
        })
        setTitleCount(detail.title.length)
        setContentCount(detail.content.length)

        // 设置分类选择器的索引
        const categoryIndex = categories.findIndex(cat => cat.id === detail.categoryId)
        if (categoryIndex !== -1) {
          setSelectedCategoryIndex(categoryIndex)
        }
      }
    } catch (error) {
      console.error('获取案例详情失败:', error)
      Taro.showToast({
        title: '获取案例失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  // 处理标题输入
  const handleTitleChange: CommonEventFunction<TextareaProps.onInputEventDetail> = (e) => {
    const title = e.detail.value
    if (title.length <= maxTitleWords) {
      setFormData(prev => ({
        ...prev,
        title: title
      }))
      setTitleCount(title.length)
    }
  }

  // 处理内容输入
  const handleContentChange: CommonEventFunction<TextareaProps.onInputEventDetail> = (e) => {
    const content = e.detail.value
    if (content.length <= maxContentWords) {
      setFormData(prev => ({
        ...prev,
        content: content
      }))
      setContentCount(content.length)
    }
  }

  // 处理图片变化
  const handleImageUrlsChange = (urls: string[]) => {
    setFormData(prev => ({
      ...prev,
      imageUrls: urls
    }))
  }

  // 处理分类选择
  const handleCategoryChange: CommonEventFunction<TextareaProps.onInputEventDetail> = (e) => {
    const index = parseInt(e.detail.value)
    setSelectedCategoryIndex(index)
    if (categories[index]) {
      setFormData(prev => ({
        ...prev,
        categoryId: categories[index].id
      }))
    }
  }

  // 表单验证
  const validateForm = (val: FormDataType): boolean => {
    if (!val.title.trim()) {
      Taro.showToast({
        title: '请输入案例标题',
        icon: 'none'
      })
      return false
    }

    if (!val.content.trim()) {
      Taro.showToast({
        title: '请输入案例内容',
        icon: 'none'
      })
      return false
    }

    if (!val.categoryId) {
      Taro.showToast({
        title: '请选择案例分类',
        icon: 'none'
      })
      return false
    }

    return true
  }

  // 提交发布/更新的原始函数
  const handleSubmitOriginal = useCallback(async () => {
    // 表单验证
    if (!validateForm(formData) || !formData.categoryId) {
      return
    }

    try {
      setSubmitting(true)
      const submitData = {
        title: formData.title.trim(),
        categoryId: formData.categoryId,
        content: formData.content.trim(),
        imagesContexts: formData.imageUrls && formData.imageUrls.length > 0 ? removeImagesPrefix(formData.imageUrls) : undefined
      }

      if (isEdit && id) {
        // 更新案例
        const response = await caseApi.updateMyCase(parseInt(id), {
          caseId: Number(id),
          ...submitData
        })

        if (response.code === STATUS_CODE.SUCCESS) {
          Taro.showToast({
            title: '更新成功',
            icon: 'success'
          })
        } else {
          throw new Error(response.message || '更新失败')
        }
      } else {
        // 创建案例
        const response = await caseApi.publishCase(submitData)

        if (response.code === STATUS_CODE.SUCCESS) {
          Taro.showToast({
            title: '发布成功',
            icon: 'success'
          })
        } else {
          throw new Error(response.message || '发布失败')
        }
      }

      // 返回上一页
      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)

    } catch (error) {
      console.error('提交失败:', error)
      Taro.showToast({
        title: isEdit ? '更新失败' : '发布失败',
        icon: 'none'
      })
    } finally {
      setSubmitting(false)
    }
  }, [formData, isEdit, id])

  // 使用防抖函数包装提交处理函数
  const handleSubmit = useMemo(
    () => debounceForMiniProgram(handleSubmitOriginal, 500),
    [handleSubmitOriginal]
  )

  // 页面初始化
  useEffect(() => {
    if (id) {
      setIsEdit(true)
    }
    loadCategories()
  }, [])

  // 编辑模式下加载案例详情
  useEffect(() => {
    if (isEdit && id && categories.length > 0) {
      loadCaseDetail(parseInt(id))
    }
  }, [isEdit, id, categories])

  return (
    <PageLayout
      title={isEdit ? '编辑案例' : '发布案例'}
      showBack
      backgroundColor='#f8f9fa'
      showFloatingMenu={false}
    >
      <View className='publish-case'>
        {loading ? (
          <View className='loading-container'>
            <View className='loading-text'>加载中...</View>
          </View>
        ) : (
          <>
            <View className='content-area'>
              {/* 分类选择 */}
              <View className='form-section'>
                <View className='form-label'>案例分类</View>
                <Picker
                  mode='selector'
                  range={categories.map(cat => cat.name)}
                  value={selectedCategoryIndex}
                  onChange={handleCategoryChange}
                >
                  <View className='category-picker'>
                    <View className='picker-text'>
                      {categories[selectedCategoryIndex]?.name || '请选择分类'}
                    </View>
                    <View className='picker-arrow'>›</View>
                  </View>
                </Picker>
              </View>

              {/* 标题输入 */}
              <View className='form-section'>
                <View className='form-label'>
                  案例标题
                  <View className='word-count'>
                    {titleCount}/{maxTitleWords}
                  </View>
                </View>
                <Textarea
                  className='title-textarea'
                  placeholder='请输入案例标题...'
                  value={formData.title}
                  onInput={handleTitleChange}
                  maxlength={maxTitleWords}
                  showConfirmBar={false}
                  adjustPosition={false}
                  autoHeight={false}
                  fixed
                />
              </View>

              {/* 内容输入 */}
              <View className='form-section'>
                <View className='form-label'>
                  案例内容
                  <View className='word-count'>
                    {contentCount}/{maxContentWords}
                  </View>
                </View>
                <Textarea
                  className='content-textarea'
                  placeholder='请详细描述案例背景、争议焦点、处理过程、判决结果等...'
                  value={formData.content}
                  onInput={handleContentChange}
                  maxlength={maxContentWords}
                  showConfirmBar={false}
                  adjustPosition={false}
                  autoHeight={false}
                  fixed
                />
              </View>
              {/* 图片上传 */}
              <View className='form-section'>
                <ImageUploadField
                  imageUrls={formData.imageUrls || []}
                  onChange={handleImageUrlsChange}
                  uploading={uploading}
                  onUploadingChange={setUploading}
                />
              </View>
            </View>

            {/* 底部提交按钮 */}
            <View className='submit-section'>
              <Button
                className='submit-btn'
                onClick={handleSubmit}
                disabled={isButtonDisabled}
                loading={submitting}
                type='primary'
                size='default'
                formType='submit'
                hoverClass={isButtonDisabled ? 'none' : 'button-hover'}
                hoverStayTime={100}
              >
                {submitting ? '提交中...' : (isEdit ? '更新案例' : '发布案例')}
              </Button>
            </View>
          </>
        )}
      </View>
    </PageLayout>
  )
}

export default PublishCase
