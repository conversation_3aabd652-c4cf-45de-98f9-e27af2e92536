/**
 * 律师文章列表组件
 * 
 * 支持滚动加载，展示律师的文章列表
 */
import React, { useState, useEffect } from 'react'
import Taro from '@tarojs/taro'
import { View, Text, Image, ScrollView } from '@tarojs/components'
import { articleApi } from '@/apis/article'
import { STATUS_CODE } from '@/utils/request/config'
import { formatTime } from '@/utils'
// 导入图标
import clockIcon from '@/assets/images/common-icon/clock.png'
import eyeIcon from '@/assets/images/common-icon/eye.png'
import likeOutlineIcon from '@/assets/images/common-icon/like_outlone_g.png'
import './index.scss'

// 组件属性
export interface LawyerArticleListProps {
  // 律师ID
  lawyerId: number
  // 自定义样式类名
  className?: string
}

const LawyerArticleList: React.FC<LawyerArticleListProps> = ({
  lawyerId,
  className = '',
}) => {
  const [articleList, setArticleList] = useState<ArticleAPI.ArticleListInfo[]>([])
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)
  const pageSize = 20

  // 加载文章列表
  const loadArticleList = async (pageNum: number = 1, isRefresh: boolean = false) => {
    if (loading) return

    try {
      setLoading(true)
      const response = await articleApi.getArticleList({
        page: pageNum,
        pageSize,
        lawyerId
      })

      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        const newList = response.data.list || []

        if (isRefresh) {
          setArticleList(newList)
        } else {
          setArticleList(prev => [...prev, ...newList])
        }

        // 判断是否还有更多数据
        setHasMore(newList.length === pageSize)
        setPage(pageNum)
      }
    } catch (error) {
      console.error('加载文章列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 处理滚动到底部
  const handleScrollToLower = () => {
    if (hasMore && !loading) {
      loadArticleList(page + 1, false)
    }
  }

  // 处理文章点击
  const handleArticleClick = (article: ArticleAPI.ArticleListInfo) => {
    Taro.navigateTo({
      url: `/subpackages/detail/article/index?id=${article.id}`
    })
  }

  // 格式化数字显示
  const formatNumber = (num: number): string => {
    if (num >= 10000) {
      return `${(num / 10000).toFixed(1)}万`
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`
    }
    return num.toString()
  }

  // 初始化加载
  useEffect(() => {
    if (lawyerId) {
      loadArticleList(1, true)
    }
  }, [lawyerId])

  return (
    <View className={`lawyer-article-list ${className}`}>
      <ScrollView
        className='lawyer-article-list__scroll'
        scrollY
        onScrollToLower={handleScrollToLower}
        lowerThreshold={100}
      >
        {articleList.map((article, index) => (
          <View
            key={article.id}
            className='lawyer-article-list__item'
            onClick={() => handleArticleClick(article)}
          >
            {/* 文章标题 */}
            <Text className='lawyer-article-list__title'>{article.title}</Text>
            {/* 文章信息 */}
            <View className='lawyer-article-list__info'>
              <View className='lawyer-article-list__info-item'>
                <Image className='lawyer-article-list__info-icon' src={clockIcon} mode='aspectFit' />
                <Text className='lawyer-article-list__info-text'>{formatTime(article.createdAt)}</Text>
              </View>
              <View className='lawyer-article-list__info-item'>
                <Image className='lawyer-article-list__info-icon' src={eyeIcon} mode='aspectFit' />
                <Text className='lawyer-article-list__info-text'>{formatNumber(article.viewCount)}</Text>
              </View>
              <View className='lawyer-article-list__info-item'>
                <Image className='lawyer-article-list__info-icon' src={likeOutlineIcon} mode='aspectFit' />
                <Text className='lawyer-article-list__info-text'>{formatNumber(article.likeCount)}</Text>
              </View>
            </View>

            {/* 分割线 */}
            {index < articleList.length - 1 && (
              <View className='lawyer-article-list__divider' />
            )}
          </View>
        ))}

        {/* 加载状态 */}
        {loading && (
          <View className='lawyer-article-list__loading'>
            <Text className='lawyer-article-list__loading-text'>加载中...</Text>
          </View>
        )}

        {/* 没有更多数据 */}
        {!hasMore && articleList.length > 0 && (
          <View className='lawyer-article-list__no-more'>
            <Text className='lawyer-article-list__no-more-text'>没有更多文章了</Text>
          </View>
        )}

        {/* 空状态 */}
        {!loading && articleList.length === 0 && (
          <View className='lawyer-article-list__empty'>
            <Text className='lawyer-article-list__empty-text'>暂无文章</Text>
          </View>
        )}
      </ScrollView>
    </View>
  )
}

export default LawyerArticleList
