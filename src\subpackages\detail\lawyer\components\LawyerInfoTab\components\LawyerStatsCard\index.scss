/**
 * 律师数据统计卡片组件样式
 */

.lawyer-stats-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

  &__title {
    font-size: 32rpx;
    font-weight: 600;
    color: #000000;
    margin-bottom: 30rpx;
    display: block;
  }

  // 主要数据区域
  &__main {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30rpx;
  }

  &__main-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
  }

  &__main-number {
    position: relative;
    margin-bottom: 10rpx;
    display: flex;
    align-items: flex-start;
    justify-content: center;
  }

  &__main-number-text {
    font-size: 48rpx;
    font-weight: 600;
    color: #BD8A4F;
    line-height: 56rpx;
    display: block;
  }

  &__main-number-unit {
    font-size: 24rpx;
    font-weight: 600;
    color: #BD8A4F;
    line-height: 1;
    margin-left: 4rpx;
    margin-top: 8rpx;
    display: block;
  }

  &__main-label {
    font-size: 26rpx;
    color: #a5a5a5;
    line-height: 30rpx;
    text-align: center;
    display: block;
  }

  // 案件类型区域
  &__category {
    border-top: 1rpx solid #F0F0F0;
    padding-top: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__category-label {
    font-size: 22rpx;
    font-weight: 500;
    color: #a5a5a5;
    display: block;
  }

  &__category-tags {
    display: flex;
    flex-wrap: wrap;
  }

  &__category-tag {
    color: #333;
    font-size: 24rpx;
    display: inline-block;
  }

  // 加载状态
  &__loading {
    &-main {
      display: flex;
      justify-content: space-around;
      margin-bottom: 30rpx;
    }

    &-item {
      width: 120rpx;
      height: 80rpx;
      background: #F5F5F5;
      border-radius: 10rpx;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }

    &-category {
      width: 100%;
      height: 60rpx;
      background: #F5F5F5;
      border-radius: 10rpx;
      border-top: 1rpx solid #F0F0F0;
      padding-top: 30rpx;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }
  }
}

// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}
