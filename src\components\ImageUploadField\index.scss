/**
 * 图片上传字段组件样式
 */

.image-upload-field {
  &__label {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 16rpx;
    display: block;
  }

  &__images {
    display: flex;
    flex-wrap: wrap;
    gap: 22rpx;
    margin-bottom: 12rpx;
  }

  &__image-item {
    position: relative;
    width: 140rpx;
    height: 140rpx;
  }

  &__image {
    width: 100%;
    height: 100%;
    border-radius: 12rpx;
    background: #f5f5f5;
  }

  &__image-delete {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    width: 32rpx;
    height: 32rpx;
    background: #ff4757;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__image-delete-text {
    color: #ffffff;
    font-size: 20rpx;
    font-weight: bold;
    line-height: 7rpx;
  }

  &__image-add {
    width: 140rpx;
    height: 140rpx;
    border: 2rpx dashed #d0d0d0;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:active {
      border-color: #BD8A4F;
      background: rgba(189, 138, 79, 0.05);
    }
  }

  &__image-add-text {
    color: #999999;
    font-weight: 300;
  }

  &__tip {
    font-size: 24rpx;
    color: #999999;
  }
}