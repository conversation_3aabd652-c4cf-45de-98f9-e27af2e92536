/**
 * AreaSelect 省市区选择组件
 * 
 * 基于Taro Picker组件的region模式封装
 * 使用内置的省市区数据，无需手动加载接口
 */
import React, { useState, useEffect, useRef } from 'react'
import { View, Picker } from '@tarojs/components'
import { AreaSelectProps, AreaSelectResult } from './types'
import './index.scss'

const AreaSelect: React.FC<AreaSelectProps> = ({
  children,
  onChange,
  value,
  disabled = false,
  className = '',
}) => {
  // 解析 value prop 为数组格式
  const parseValueToArray = (val: string): string[] => {
    if (val) {
      const areas = val.split('/')
      if (areas.length >= 2) {
        return [areas[0] || '', areas[1] || '', areas[2] || '']
      }
    }
    return ['', '', '']
  }

  // 当前选择的值 [省份, 城市, 区县]
  const [selectedValue, setSelectedValue] = useState<string[]>(() => parseValueToArray(value || ''))

  // 用于跟踪上一次的外部 value，避免不必要的更新
  const prevValueRef = useRef<string>(value || '')

  // 用于跟踪是否是用户主动选择（避免外部 value 变化时的干扰）
  const isUserSelectionRef = useRef<boolean>(false)

  // 只有在外部 value 真正变化且不是用户选择时才同步状态
  useEffect(() => {
    const currentValue = value || ''

    // 如果是用户选择触发的，跳过同步
    if (isUserSelectionRef.current) {
      isUserSelectionRef.current = false
      prevValueRef.current = currentValue
      return
    }

    // 如果外部 value 没有变化，跳过
    if (currentValue === prevValueRef.current) {
      return
    }

    // 更新内部状态
    const newSelectedValue = parseValueToArray(currentValue)
    setSelectedValue(newSelectedValue)
    prevValueRef.current = currentValue
  }, [value])

  // 省名称过滤，只有黑龙江和内蒙古保留前三个字，其他省份只保留前两个字
  const filterProvinceName = (name: string) => {
    if (name.includes('黑龙江') || name.includes('内蒙古')) {
      return name.slice(0, 3)
    }
    return name.slice(0, 2)
  }

  // 处理选择变化
  const handlePickerChange = (e: any) => {
    const { value: selectedAreas } = e.detail
    const [province, city, district] = selectedAreas
    const filteredProvince = filterProvinceName(province)

    // 标记为用户主动选择
    isUserSelectionRef.current = true

    // 更新选择的值
    setSelectedValue(selectedAreas)

    // 构建结果
    const result: AreaSelectResult = {
      areaString: district
        ? `${filteredProvince}/${city}/${district}`
        : `${filteredProvince}/${city}`,
      province: filteredProvince,
      city: city,
      district: district ? district : undefined
    }
    // 触发回调
    onChange?.(result)
  }

  return (
    <View className={`area-select ${className} ${disabled ? 'area-select--disabled' : ''}`}>
      <Picker
        mode='region'
        value={selectedValue}
        onChange={handlePickerChange}
        disabled={disabled}
      >
        <View className='area-select__trigger'>
          {children}
        </View>
      </Picker>
    </View>
  )
}

// 导出主组件
export default AreaSelect

// 导出类型定义
export type {
  AreaSelectProps,
  AreaSelectResult,
  AreaItem
} from './types'

// 导出常量
export { MUNICIPALITIES } from './types'
