/**
 * 手机号验证码输入组件样式
 */

.phone-code-input {
  .input-field {
    margin-bottom: 30rpx;

    .field-label {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .required-mark {
        color: #ff4d4f;
        font-size: 28rpx;
        margin-right: 8rpx;
      }

      .label-text {
        font-size: 28rpx;
        color: #333333;
        font-weight: 500;
      }
    }

    .form-input {
      height: 88rpx;
      background-color: #F7F7F7;
      border-radius: 8rpx;
      padding: 0 24rpx;
      font-size: 28rpx;
      color: #333333;
      border: none;

      &::placeholder {
        color: #999999;
      }

      &:focus {
        background-color: #f0f0f0;
      }
    }

    .code-input-group {
      display: flex;
      align-items: center;
      gap: 20rpx;

      .code-input {
        flex: 1;
      }

      .code-btn {
        width: 200rpx;
        height: 88rpx;
        background-color: #BD8A4F;
        color: #ffffff;
        font-size: 26rpx;
        border-radius: 8rpx;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;

        &:active {
          background-color: #a67a42;
        }

        &.button-disabled {
          background-color: #cccccc;
          color: #999999;
        }

        &.button-loading {
          background-color: #cccccc;
        }
      }
    }
  }
}
