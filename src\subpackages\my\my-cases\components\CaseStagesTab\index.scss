/**
 * 案件阶段标签组件样式
 */

.case-stages-tab {
  background: #ffffff;
  border-bottom: 2rpx solid #f0f0f0;

  &__scroll-view {
    width: 100%;
    white-space: nowrap;
  }

  &__container {
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    min-width: 100%;
  }

  &__item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30rpx 0;
    margin-right: 40rpx;
    flex-shrink: 0;
    transition: all 0.3s ease;

    &:last-child {
      padding-right: 30rpx;
    }

    // 选中状态
    &--active {
      .case-stages-tab__text {
        color: #BD8A4F;
        font-weight: 600;
      }
    }

    // 点击态
    &:active {
      opacity: 0.7;
    }
  }

  &__text {
    font-size: 28rpx;
    color: #666666;
    white-space: nowrap;
    transition: all 0.3s ease;
  }

  &__underline {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40rpx;
    height: 4rpx;
    background-color: #BD8A4F;
    border-radius: 2rpx;
    animation: underline-appear 0.3s ease;
  }
}

// 下划线出现动画
@keyframes underline-appear {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 40rpx;
    opacity: 1;
  }
}
