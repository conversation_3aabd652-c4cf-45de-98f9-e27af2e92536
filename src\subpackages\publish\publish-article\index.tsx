/**
 * 文章发布/编辑页面
 */
import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { View, Textarea, Button, TextareaProps, CommonEventFunction } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import PageLayout from '@/components/PageLayout'
import { articleApi } from '@/utils/request/apis/article'
import { STATUS_CODE } from '@/utils/request/config'
import { debounceForMiniProgram } from '@/utils/helpers/common'
import './index.scss'

interface FormDataType {
  title: string
  content: string
}

const PublishArticle: React.FC = () => {
  const router = useRouter()
  const { id } = router.params // 文章ID，编辑时传入

  // 页面状态
  const [isEdit, setIsEdit] = useState(false) // 是否为编辑模式
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)

  // 表单数据
  const [formData, setFormData] = useState<FormDataType>({
    title: '', // 文章标题
    content: '' // 文章内容
  })

  // 字数统计
  const [titleCount, setTitleCount] = useState(0)
  const [contentCount, setContentCount] = useState(0)
  const maxTitleWords = 100
  const maxContentWords = 10000

  // 计算按钮是否可用
  const isButtonDisabled = submitting || !formData.title.trim() || !formData.content.trim()

  // 获取文章详情（编辑模式）
  const loadArticleDetail = async (articleId: number) => {
    try {
      setLoading(true)
      const response = await articleApi.getMyArticleDetail(articleId)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        const detail = response.data
        setFormData({
          title: detail.title,
          content: detail.content
        })
        setTitleCount(detail.title.length)
        setContentCount(detail.content.length)
      }
    } catch (error) {
      console.error('获取文章详情失败:', error)
      Taro.showToast({
        title: '获取文章失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  // 处理标题输入
  const handleTitleChange: CommonEventFunction<TextareaProps.onInputEventDetail> = (e) => {
    const title = e.detail.value
    if (title.length <= maxTitleWords) {
      setFormData(prev => ({
        ...prev,
        title: title
      }))
      setTitleCount(title.length)
    }
  }

  // 处理内容输入
  const handleContentChange: CommonEventFunction<TextareaProps.onInputEventDetail> = (e) => {
    const content = e.detail.value
    if (content.length <= maxContentWords) {
      setFormData(prev => ({
        ...prev,
        content: content
      }))
      setContentCount(content.length)
    }
  }

  // 表单验证
  const validateForm = (val: FormDataType): boolean => {
    if (!val.title.trim()) {
      Taro.showToast({
        title: '请输入文章标题',
        icon: 'none'
      })
      return false
    }

    if (!val.content.trim()) {
      Taro.showToast({
        title: '请输入文章内容',
        icon: 'none'
      })
      return false
    }

    return true
  }

  // 提交发布/更新的原始函数
  const handleSubmitOriginal = useCallback(async () => {
    // 表单验证
    if (!validateForm(formData)) {
      return
    }

    try {
      setSubmitting(true)

      if (isEdit && id) {
        // 更新文章
        const response = await articleApi.updateMyArticle(parseInt(id), {
          title: formData.title.trim(),
          content: formData.content.trim()
        })

        if (response.code === STATUS_CODE.SUCCESS) {
          Taro.showToast({
            title: '更新成功',
            icon: 'success'
          })
        } else {
          throw new Error(response.message || '更新失败')
        }
      } else {
        // 创建文章
        const response = await articleApi.publishArticle({
          title: formData.title.trim(),
          content: formData.content.trim()
        })

        if (response.code === STATUS_CODE.SUCCESS) {
          Taro.showToast({
            title: '发布成功',
            icon: 'success'
          })
        } else {
          throw new Error(response.message || '发布失败')
        }
      }

      // 返回上一页
      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)

    } catch (error) {
      console.error('提交失败:', error)
      Taro.showToast({
        title: isEdit ? '更新失败' : '发布失败',
        icon: 'none'
      })
    } finally {
      setSubmitting(false)
    }
  }, [formData, isEdit, id])

  // 使用防抖函数包装提交处理函数
  const handleSubmit = useMemo(
    () => debounceForMiniProgram(handleSubmitOriginal, 500),
    [handleSubmitOriginal]
  )

  useEffect(() => {
    if (id) {
      setIsEdit(true)
    }
  }, [])

  // 编辑模式下加载文章详情
  useEffect(() => {
    if (isEdit && id) {
      loadArticleDetail(parseInt(id))
    }
  }, [isEdit, id])

  return (
    <PageLayout
      title={isEdit ? '编辑文章' : '发布文章'}
      showBack
      backgroundColor='#f8f9fa'
      showFloatingMenu={false}
    >
      <View className='publish-article'>
        {loading ? (
          <View className='loading-container'>
            <View className='loading-text'>加载中...</View>
          </View>
        ) : (
          <>
            <View className='content-area'>
              {/* 标题输入 */}
              <View className='form-section'>
                <View className='form-label'>
                  文章标题
                  <View className='word-count'>
                    {titleCount}/{maxTitleWords}
                  </View>
                </View>
                <Textarea
                  className='title-textarea'
                  placeholder='请输入文章标题...'
                  value={formData.title}
                  onInput={handleTitleChange}
                  maxlength={maxTitleWords}
                  showConfirmBar={false}
                  adjustPosition={false}
                  autoHeight={false}
                  fixed
                />
              </View>

              {/* 内容输入 */}
              <View className='form-section'>
                <View className='form-label'>
                  文章内容
                  <View className='word-count'>
                    {contentCount}/{maxContentWords}
                  </View>
                </View>
                <Textarea
                  className='content-textarea'
                  placeholder='请详细撰写文章内容，包括观点阐述、案例分析、法律条文解读等...'
                  value={formData.content}
                  onInput={handleContentChange}
                  maxlength={maxContentWords}
                  showConfirmBar={false}
                  adjustPosition={false}
                  autoHeight={false}
                  fixed
                />
              </View>
            </View>

            {/* 底部提交按钮 */}
            <View className='submit-section'>
              <Button
                className='submit-btn'
                onClick={handleSubmit}
                disabled={isButtonDisabled}
                loading={submitting}
                type='primary'
                size='default'
                formType='submit'
                hoverClass={isButtonDisabled ? 'none' : 'button-hover'}
                hoverStayTime={100}
              >
                {submitting ? '提交中...' : (isEdit ? '更新文章' : '发布文章')}
              </Button>
            </View>
          </>
        )}
      </View>
    </PageLayout>
  )
}

export default PublishArticle
