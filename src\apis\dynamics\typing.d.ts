/**
 * 律师动态模块类型声明
 * 基于 api.json 接口文档定义
 */

// 引用通用类型
/// <reference path="../common-types.d.ts" />

declare namespace DynamicsAPI {
  // 律师动态详情信息类型（基于 LawDynamicsDetailDto）
  interface DynamicsDetailInfo extends CommonTypes.BaseEntity {
    title: string
    content: string  // HTML/富文本
    categoryId: number
    categoryName: string
    viewCount: number
    creatorId: number
    createdAt: string
  }

  // 律师动态列表信息类型（基于 LawDynamicsListDto）
  interface DynamicsListInfo extends CommonTypes.BaseEntity {
    title: string
    categoryId: number
    categoryName: string
    viewCount: number
    createdAt: string
  }

  // 律师动态分类类型（基于 LawDynamicsCategoryDto）
  interface DynamicsCategoryInfo extends CommonTypes.CategoryDto { }

  // 律师动态分类统计类型（基于 LawyerDynamicsCategoryCountDto）
  interface LawyerDynamicsCategoryCountDto {
    id: number
    name: string
    num: number
  }

  // 获取律师动态详情请求参数（基于 FindDynamicsDetailReq）
  interface GetDynamicsDetailRequest {
    dynamicsId: number
  }

  // 获取律师动态详情响应（基于 FindDynamicsDetailRes）
  interface GetDynamicsDetailResponse {
    detail: DynamicsDetailInfo
    lawyerInfo: LawyerAPI.LawyerInfo
  }

  // 获取动态列表请求参数（基于 FindLawDynamicsListReq）
  interface GetDynamicsListRequest extends CommonTypes.BasePaginationRequest {
    lawyerId?: number
    title?: string  // 动态标题模糊搜索
    categoryId?: number  // 分类ID
  }

  // 获取动态列表响应（基于 FindLawDynamicsListRes）
  interface GetDynamicsListResponse extends CommonTypes.PaginationResponse<DynamicsListInfo> { }

  // 获取律师动态列表请求参数（基于 DynamicsListReq）
  interface GetLawyerDynamicsListRequest extends CommonTypes.BasePaginationRequest {
    title?: string  // 动态标题模糊搜索
    categoryId?: number  // 分类ID
  }

  // 获取律师动态列表响应（基于 DynamicsListRes）
  interface GetLawyerDynamicsListResponse extends CommonTypes.PaginationResponse<DynamicsListInfo> { }

  // 获取推荐动态列表请求参数（基于 RecommendListReq）
  interface GetRecommendDynamicsListRequest extends CommonTypes.BasePaginationRequest {
    dynamicsId: number
  }

  // 获取推荐动态列表响应（基于 RecommendListRes）
  interface GetRecommendDynamicsListResponse extends CommonTypes.PaginationResponse<DynamicsListInfo> { }

  // 获取我的动态列表请求参数
  interface GetMyDynamicsListRequest extends CommonTypes.BasePaginationRequest {
    title?: string
    categoryId?: number
  }

  // 获取我的动态列表响应
  interface GetMyDynamicsListResponse extends CommonTypes.PaginationResponse<MyDynamicsInfo> { }

  // 获取我的动态详情响应
  interface GetMyDynamicsDetailResponse {
    detail: DynamicsDetailInfo
  }

  // 我的动态信息
  interface MyDynamicsInfo {
    id: number
    title: string
    categoryId: number
    categoryName: string
    content: string
    viewCount: number
    status: number  // 状态：0删除,1待审核,2审核通过,3审核不通过
    rejectReason?: string
    creator: string
    creatorId: number
    reviewer?: string
    reviewerId?: number
    modifier?: string
    reviewTime?: string
    createdAt: string
    updatedAt: string
  }

  // 获取我的动态列表请求参数（基于 LawyerDynamicsListReq）
  interface GetMyDynamicsListRequest extends CommonTypes.BasePaginationRequest {
    title?: string  // 动态标题模糊搜索
    categoryId?: number  // 分类ID
  }

  // 获取我的动态列表响应（基于 LawyerDynamicsListRes）
  interface GetMyDynamicsListResponse extends CommonTypes.PaginationResponse<MyDynamicsInfo> { }

  // 获取我的动态详情响应（基于 OwnerDynamicsDetailRes）
  interface GetMyDynamicsDetailResponse extends MyDynamicsInfo { }

  // 发布动态请求参数（基于 OwnerIssueDynamicsReq）
  interface PublishDynamicsRequest {
    title: string
    categoryId: number
    content: string
  }

  // 发布动态响应（基于 OwnerIssueDynamicsRes）
  interface PublishDynamicsResponse { }

  // 修改动态请求参数（基于 OwnerDynamicsSaveReq）
  interface UpdateMyDynamicsRequest {
    title: string
    categoryId: number
    content: string
  }

  // 修改动态响应（基于 OwnerDynamicsSaveRes）
  interface UpdateMyDynamicsResponse { }

  // 使用通用类型别名
  type PaginationResponse<T> = CommonTypes.PaginationResponse<T>
  type ListResponse<T> = CommonTypes.ListResponse<T>
}
