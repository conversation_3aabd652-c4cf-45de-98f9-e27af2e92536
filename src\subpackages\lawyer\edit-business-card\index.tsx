/**
 * 编辑电子名片页面
 */
import React, { useState, useEffect } from 'react'
import { View, Input, Textarea, Button, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import PageLayout from '@/components/PageLayout'
import AreaSelect from '@/components/AreaSelect'
import { lawyer<PERSON><PERSON> } from '@/apis/lawyer'
import { uploadApi } from '@/apis/upload'
import CategoryCacheManager from '@/utils/cache/categoryCache'
import { STATUS_CODE } from '@/utils/request/config'
import { parseImageUrl, removeImagePrefix } from '@/utils'
import './index.scss'

const EditBusinessCard: React.FC = () => {
  // 页面状态
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [areaSelectFocused, setAreaSelectFocused] = useState(false)

  // 表单数据
  const [formData, setFormData] = useState({
    personalProfile: '', // 个人简介
    figurePhotoUrl: '',  // 形象照地址
    lawFirm: '',         // 律所名称
    lawFirmAddress: '',  // 律所地址
    fieldIdArr: [] as number[] // 律师擅长的案件领域
  })

  // 地区数据
  const [areaData, setAreaData] = useState({
    province: '',
    city: '',
    district: ''
  })

  // 分类数据
  const [categories, setCategories] = useState<{ id: number; name: string }[]>([])
  const [selectedFields, setSelectedFields] = useState<number[]>([])

  // 字数统计
  const [profileCount, setProfileCount] = useState(0)
  const maxProfileLength = 500

  // 计算按钮是否可用
  const isButtonDisabled = submitting || !formData.personalProfile.trim() || !formData.lawFirm.trim()

  // 解析 fieldIdStr 格式: "||23||22||21||18||19||20||17||16||15||"
  const parseFieldIds = (fieldIdStr: string): number[] => {
    if (!fieldIdStr) return []

    // 移除首尾的 || 并按 || 分割，过滤空字符串并转换为数字
    const result = fieldIdStr
      .replace(/^\|\||\|\|$/g, '') // 移除开头和结尾的 ||
      .split('||')
      .filter(id => id.trim() !== '') // 过滤空字符串
      .map(id => parseInt(id.trim(), 10))
      .filter(id => !Number.isNaN(id)) // 过滤无效数字
    return result
  }
  // 获取律师详情信息
  const loadLawyerInfo = async () => {
    try {
      setLoading(true)
      const response = await lawyerApi.getOwnerLawyerInfo()

      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        const info = response.data
        const fieldIds = parseFieldIds(info.fieldIdStr || '')
        setFormData({
          personalProfile: info.personalProfile || '',
          figurePhotoUrl: parseImageUrl(info.figurePhotoUrl) || '',
          lawFirm: info.lawFirm || '',
          lawFirmAddress: info.lawFirmAddress || '',
          fieldIdArr: fieldIds
        })
        setAreaData({
          province: info.province || '',
          city: info.city || '',
          district: info.district || ''
        })
        setSelectedFields(fieldIds)
        setProfileCount(info.personalProfile?.length || 0)
      } else {
        throw new Error(response.message || '获取律师信息失败')
      }
    } catch (error) {
      console.error('获取律师信息失败:', error)
      Taro.showToast({
        title: '获取律师信息失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  // 加载案件分类数据
  const loadCategories = async () => {
    try {
      const categoryList = await CategoryCacheManager.getCaseCategoryList()
      setCategories(categoryList)
    } catch (error) {
      console.error('加载分类数据失败:', error)
    }
  }

  // 处理个人简介输入
  const handleProfileChange = (e: any) => {
    const profile = e.detail.value
    if (profile.length <= maxProfileLength) {
      setFormData(prev => ({
        ...prev,
        personalProfile: profile
      }))
      setProfileCount(profile.length)
    }
  }

  // 处理律所名称输入
  const handleLawFirmChange = (e: any) => {
    setFormData(prev => ({
      ...prev,
      lawFirm: e.detail.value
    }))
  }

  // 处理律所地址输入
  const handleLawFirmAddressChange = (e: any) => {
    setFormData(prev => ({
      ...prev,
      lawFirmAddress: e.detail.value
    }))
  }

  // 处理地区选择
  const handleAreaChange = (result: { areaString: string; province?: string; city?: string; district?: string }) => {
    setAreaData({
      province: result.province || '',
      city: result.city || '',
      district: result.district || ''
    })
  }

  // 处理擅长领域选择
  const handleFieldToggle = (fieldId: number) => {
    setSelectedFields(prev => {
      const newFields = prev.includes(fieldId)
        ? prev.filter(id => id !== fieldId)
        : [...prev, fieldId]

      setFormData(prevForm => ({
        ...prevForm,
        fieldIdArr: newFields
      }))

      return newFields
    })
  }

  // 选择形象照
  const handleChooseImage = () => {
    Taro.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        const tempFilePath = res.tempFilePaths[0]

        try {
          Taro.showLoading({ title: '上传中...' })

          const uploadResponse = await uploadApi.uploadImage(tempFilePath)
          if (uploadResponse.code === STATUS_CODE.SUCCESS && uploadResponse.data) {
            setFormData(prev => ({
              ...prev,
              figurePhotoUrl: uploadResponse.data.url
            }))
            Taro.showToast({
              title: '上传成功',
              icon: 'success'
            })
          } else {
            throw new Error(uploadResponse.message || '上传失败')
          }
        } catch (error: any) {
          console.error('上传图片失败:', error)
          Taro.showToast({
            title: error.message || '上传失败',
            icon: 'none'
          })
        } finally {
          Taro.hideLoading()
        }
      }
    })
  }

  // 表单验证
  const validateForm = (): boolean => {
    if (!formData.personalProfile.trim()) {
      Taro.showToast({
        title: '请输入个人简介',
        icon: 'none'
      })
      return false
    }

    if (!formData.lawFirm.trim()) {
      Taro.showToast({
        title: '请输入律所名称',
        icon: 'none'
      })
      return false
    }

    return true
  }

  // 提交更新
  const handleSubmit = async () => {
    // 表单验证
    if (!validateForm()) {
      return
    }

    try {
      setSubmitting(true)

      // 更新电子名片
      const response = await lawyerApi.updateBusinessCard({ ...formData, figurePhotoUrl: removeImagePrefix(formData.figurePhotoUrl) })

      if (response.code === STATUS_CODE.SUCCESS) {
        Taro.showToast({
          title: '更新成功',
          icon: 'success'
        })

        // 返回上一页
        setTimeout(() => {
          Taro.navigateBack()
        }, 1500)
      } else {
        throw new Error(response.message || '更新失败')
      }
    } catch (error: any) {
      console.error('更新电子名片失败:', error)
      Taro.showToast({
        title: error.message || '更新失败',
        icon: 'none'
      })
    } finally {
      setSubmitting(false)
    }
  }

  // 初始化页面
  useEffect(() => {
    loadLawyerInfo()
    loadCategories()
  }, [])

  return (
    <PageLayout
      title='编辑电子名片'
      showBack
      showFloatingMenu={false}
    >
      <View className='edit-business-card-container'>
        {!loading && (
          <>
            {/* 表单内容 */}
            <View className='form-container'>
              {/* 形象照上传 */}
              <View className='form-section'>
                <View className='form-label'>形象照</View>
                <View className='image-upload' onClick={handleChooseImage}>
                  {formData.figurePhotoUrl ? (
                    <Image
                      className='uploaded-image'
                      src={formData.figurePhotoUrl}
                      mode='aspectFill'
                    />
                  ) : (
                    <View className='upload-placeholder'>
                      <View className='upload-icon'>+</View>
                      <View className='upload-text'>点击上传形象照</View>
                    </View>
                  )}
                </View>
              </View>

              {/* 个人简介 */}
              <View className='form-section'>
                <View className='form-label'>
                  个人简介
                  <View className='word-count'>
                    {profileCount}/{maxProfileLength}
                  </View>
                </View>
                <Textarea
                  className='profile-textarea'
                  placeholder='请输入个人简介，展示您的专业背景和经验...'
                  value={formData.personalProfile}
                  onInput={handleProfileChange}
                  maxlength={maxProfileLength}
                  showConfirmBar={false}
                  adjustPosition={false}
                  autoHeight={false}
                />
              </View>

              {/* 律所名称 */}
              <View className='form-section'>
                <View className='form-label'>律所名称</View>
                <Input
                  className='form-input'
                  placeholder='请输入律所名称'
                  value={formData.lawFirm}
                  onInput={handleLawFirmChange}
                />
              </View>

              {/* 所在地区 */}
              <View className='form-section'>
                <View className='form-label'>所在地区</View>
                <AreaSelect
                  value={areaData.province && areaData.city ? `${areaData.province}/${areaData.city}${areaData.district ? '/' + areaData.district : ''}` : ''}
                  onChange={handleAreaChange}
                  className='area-select-input'
                >
                  <View
                    className={`area-select-display ${areaSelectFocused ? 'focused' : ''}`}
                    onTouchStart={() => setAreaSelectFocused(true)}
                    onTouchEnd={() => setTimeout(() => setAreaSelectFocused(false), 150)}
                  >
                    {areaData.province && areaData.city ? (
                      <View className='area-text'>
                        {areaData.province} {areaData.city} {areaData.district}
                      </View>
                    ) : (
                      <View className='area-placeholder'>请选择所在地区</View>
                    )}
                    <View className='area-arrow'>›</View>
                  </View>
                </AreaSelect>
              </View>

              {/* 律所地址 */}
              <View className='form-section'>
                <View className='form-label'>律所地址</View>
                <Input
                  className='form-input'
                  placeholder='请输入详细地址'
                  value={formData.lawFirmAddress}
                  onInput={handleLawFirmAddressChange}
                />
              </View>

              {/* 擅长领域 */}
              <View className='form-section'>
                <View className='form-label'>擅长领域</View>
                <View className='field-tags'>
                  {categories.map((category) => (
                    <View
                      key={category.id}
                      className={`field-tag ${selectedFields.includes(category.id) ? 'selected' : ''}`}
                      onClick={() => handleFieldToggle(category.id)}
                    >
                      <View className='tag-text'>{category.name}</View>
                    </View>
                  ))}
                </View>
              </View>
            </View>

            {/* 底部提交按钮 */}
            <View className='submit-section'>
              <Button
                className='submit-btn'
                onClick={handleSubmit}
                disabled={isButtonDisabled}
                loading={submitting}
                type='primary'
                size='default'
                formType='submit'
                hoverClass={isButtonDisabled ? 'none' : 'button-hover'}
                hoverStayTime={100}
              >
                {submitting ? '保存中...' : '保存名片'}
              </Button>
            </View>
          </>
        )}
      </View>
    </PageLayout>
  )
}

export default EditBusinessCard
