import React, { useState, useEffect } from 'react'
import { View, 
  // Input, Image,
   Text, ScrollView } from '@tarojs/components'
import Taro from '@tarojs/taro'
import PageLayout, { PageContent } from '@/components/PageLayout'
import { articleApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'
import { formatTime } from '@/utils'
// import SearchIcon from '@/assets/images/common-icon/search.png'
import './index.scss'

// 分页参数
const PAGE_SIZE = 20

const ArticleCollection: React.FC = () => {
  // 状态管理
  // const [searchKeyword, setSearchKeyword] = useState('')
  const [collectedArticles, setCollectedArticles] = useState<ArticleAPI.MyCollectedArticleInfo[]>([])
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)

  // 分页参数
  const [filterParams, setFilterParams] = useState<ArticleAPI.GetMyCollectedArticlesRequest>({
    page: 1,
    pageSize: PAGE_SIZE
  })

  // 加载收藏文章列表
  const loadCollectedArticles = async (val?: ArticleAPI.GetMyCollectedArticlesRequest) => {
    if (loading) return
    try {
      setLoading(true)
      const response = await articleApi.getMyCollectedArticles({ ...val })
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        const newList = response.data.list || []
        if (response.data.page === 1) {
          setCollectedArticles(newList)
        } else {
          setCollectedArticles(prev => [...prev, ...newList])
        }

        // 判断是否还有更多数据
        setHasMore(response.data.page < response.data.pageCount)
      } else {
        console.log('接口无数据返回')
      }
    } catch (error) {
      console.error('加载收藏文章列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 搜索处理
  // const handleSearch = () => {
  //   const newFilters = {
  //     ...filterParams,
  //     page: 1
  //   }
  //   setFilterParams(newFilters)
  //   setHasMore(true)
  // }

  // 搜索输入处理
  // const handleSearchInput = (e: any) => {
  //   setSearchKeyword(e.detail.value)
  // }

  // 搜索确认处理
  // const handleSearchConfirm = (e: any) => {
  //   const value = e.detail.value
  //   setSearchKeyword(value)
  //   const newFilters = {
  //     ...filterParams,
  //     page: 1
  //   }
  //   setFilterParams(newFilters)
  //   setHasMore(true)
  // }

  // 加载更多
  const handleLoadMore = () => {
    if (!loading && hasMore) {
      const nextPage = (filterParams.page || 1) + 1
      const newFilters = {
        ...filterParams,
        page: nextPage
      }
      setFilterParams(newFilters)
    }
  }

  // 跳转到文章详情页面
  const handleArticleClick = (article: ArticleAPI.MyCollectedArticleInfo) => {
    Taro.navigateTo({
      url: `/subpackages/detail/article/index?id=${article.articleId}`
    })
  }

  // 下拉刷新
  const handleRefresh = async () => {
    const newFilters = {
      ...filterParams,
      page: 1
    }
    setFilterParams(newFilters)
    setHasMore(true)
  }



  // 监听过滤参数变化
  useEffect(() => {
    loadCollectedArticles(filterParams)
  }, [filterParams])


  return (
    <PageLayout
      title='个人收藏文章'
      showBack
    >
      <PageContent>
        <View className='collection-article-manage'>
          {/* 搜索栏 */}
          {/* <View className='collection-search-header'>
            <View className='collection-search-box'>
              <Image
                src={SearchIcon}
                className='collection-search-icon'
                mode='aspectFit'
                onClick={handleSearch}
              />
              <Input
                className='collection-search-input'
                placeholder='搜索收藏的文章'
                value={searchKeyword}
                onInput={handleSearchInput}
                onConfirm={handleSearchConfirm}
                confirmType='search'
              />
            </View>
          </View> */}

          {/* 文章列表 */}
          <View className='collection-article-list-container'>
            <ScrollView
              className='collection-article-list'
              scrollY
              onScrollToLower={handleLoadMore}
              lowerThreshold={100}
              refresherEnabled
              refresherTriggered={loading && filterParams.page === 1}
              onRefresherRefresh={handleRefresh}
            >
              {collectedArticles.map((article) => (
                <View
                  key={article.id}
                  className='collection-article-card'
                  onClick={() => handleArticleClick(article)}
                >
                  <View className='collection-article-header'>
                    <Text className='collection-article-title'>{article.articleTitle}</Text>
                  </View>
                  <View className='collection-article-footer'>
                    <Text className='collection-article-time'>收藏于 {formatTime(article.createdAt)}</Text>
                  </View>
                </View>
              ))}

              {/* 加载状态 */}
              {loading && (
                <View className='collection-loading-more'>
                  <Text>加载中...</Text>
                </View>
              )}

              {/* 没有更多数据 */}
              {!hasMore && collectedArticles.length > 0 && (
                <View className='collection-no-more'>
                  <Text>没有更多收藏了</Text>
                </View>
              )}

              {/* 空状态 */}
              {!loading && collectedArticles.length === 0 && (
                <View className='collection-empty-state'>
                  <Text>暂无收藏文章</Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </PageContent>
    </PageLayout>
  )
}

export default ArticleCollection
