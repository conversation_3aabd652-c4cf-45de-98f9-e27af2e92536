/**
 * 小程序相关工具函数
 */
import Taro from '@tarojs/taro'

/**
 * 获取系统信息
 */
export const getSystemInfo = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    try {
      // 使用新的API组合获取系统信息
      const deviceInfo = Taro.getDeviceInfo()
      const windowInfo = Taro.getWindowInfo()
      const appBaseInfo = Taro.getAppBaseInfo()

      // 组合成类似原来getSystemInfo的格式
      const systemInfo = {
        ...deviceInfo,
        ...windowInfo,
        ...appBaseInfo
      }

      resolve(systemInfo)
    } catch (error) {
      reject(error)
    }
  })
}

/**
 * 显示Toast
 * @param title 标题
 * @param options 选项
 */
export const showToast = (
  title: string,
  options: {
    icon?: 'success' | 'error' | 'loading' | 'none'
    duration?: number
    mask?: boolean
  } = {}
): void => {
  const { icon = 'none', duration = 2000, mask = false } = options

  Taro.showToast({
    title,
    icon,
    duration,
    mask
  })
}

/**
 * 显示加载中
 * @param title 标题
 * @param mask 是否显示透明蒙层
 */
export const showLoading = (title: string = '加载中...', mask: boolean = true): void => {
  Taro.showLoading({
    title,
    mask
  })
}

/**
 * 隐藏加载中
 */
export const hideLoading = (): void => {
  Taro.hideLoading()
}

/**
 * 显示模态对话框
 * @param options 选项
 */
export const showModal = (options: {
  title?: string
  content: string
  showCancel?: boolean
  cancelText?: string
  confirmText?: string
}): Promise<any> => {
  const {
    title = '提示',
    content,
    showCancel = true,
    cancelText = '取消',
    confirmText = '确定'
  } = options

  return new Promise((resolve, reject) => {
    Taro.showModal({
      title,
      content,
      showCancel,
      cancelText,
      confirmText,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 选择图片
 * @param options 选项
 */
export const chooseImage = (options: {
  count?: number
  sizeType?: ('original' | 'compressed')[]
  sourceType?: ('album' | 'camera')[]
} = {}): Promise<any> => {
  const {
    count = 1,
    sizeType = ['original', 'compressed'],
    sourceType = ['album', 'camera']
  } = options

  return new Promise((resolve, reject) => {
    Taro.chooseImage({
      count,
      sizeType,
      sourceType,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 预览图片
 * @param urls 图片链接数组
 * @param current 当前显示图片链接
 */
export const previewImage = (urls: string[], current?: string): void => {
  Taro.previewImage({
    urls,
    current: current || urls[0]
  })
}

/**
 * 复制到剪贴板
 * @param data 要复制的内容
 * @param showToast 是否显示提示
 */
export const setClipboardData = async (
  data: string,
  showToastFlag: boolean = true
): Promise<void> => {
  try {
    await Taro.setClipboardData({ data })
    if (showToastFlag) {
      Taro.showToast({
        title: '复制成功',
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('复制失败:', error)
    if (showToastFlag) {
      Taro.showToast({
        title: '复制失败',
        icon: 'error'
      })
    }
    throw error
  }
}

/**
 * 获取设备信息
 */
export const getDeviceInfo = (): any => {
  try {
    return Taro.getDeviceInfo()
  } catch (error) {
    console.error('获取设备信息失败:', error)
    return {}
  }
}

/**
 * 获取窗口信息
 */
export const getWindowInfo = (): any => {
  try {
    return Taro.getWindowInfo()
  } catch (error) {
    console.error('获取窗口信息失败:', error)
    return {}
  }
}

/**
 * 振动反馈
 * @param type 振动类型
 */
export const vibrateShort = (type: 'heavy' | 'medium' | 'light' = 'medium'): void => {
  Taro.vibrateShort({ type })
}

/**
 * 长振动
 */
export const vibrateLong = (): void => {
  Taro.vibrateLong()
}

/**
 * 获取网络类型
 */
export const getNetworkType = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    Taro.getNetworkType({
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 监听网络状态变化
 * @param callback 回调函数
 */
export const onNetworkStatusChange = (callback: (res: any) => void): void => {
  Taro.onNetworkStatusChange(callback)
}
