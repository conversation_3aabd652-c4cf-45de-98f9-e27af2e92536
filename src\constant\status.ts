/**
 * 状态相关常量和工具函数
 */

// 订单状态枚举
export enum OrderStatus {
  DELETED = 0,    // 已删除
  CREATED = 1,    // 已创建
  PENDING = 2,    // 待签约
  PROCESSING = 3, // 跟进中
  COMPLETED = 4,  // 已完结
  CANCELLED = 5   // 已作废
}

// 支付状态枚举
export enum PaymentStatus {
  PENDING = 1,    // 未支付
  COMPLETED = 2,  // 支付完成
  FAILED = 3      // 支付失败
}

// 案件阶段状态枚举
export enum CaseStageStatus {
  DELETED = 1,    // 已删除
  PROCESSING = 2, // 进行中
  COMPLETED = 3   // 已完结
}

// 订单状态配置
export const ORDER_STATUS_CONFIG = {
  [OrderStatus.DELETED]: {
    text: '已删除',
    className: 'status-deleted'
  },
  [OrderStatus.CREATED]: {
    text: '待处理',
    className: 'status-created'
  },
  [OrderStatus.PENDING]: {
    text: '待签约',
    className: 'status-pending'
  },
  [OrderStatus.PROCESSING]: {
    text: '跟进中',
    className: 'status-processing'
  },
  [OrderStatus.COMPLETED]: {
    text: '已完结',
    className: 'status-completed'
  },
  [OrderStatus.CANCELLED]: {
    text: '已作废',
    className: 'status-cancelled'
  }
}

// 支付状态配置
export const PAYMENT_STATUS_CONFIG = {
  [PaymentStatus.PENDING]: {
    text: '未支付',
    className: 'payment-pending'
  },
  [PaymentStatus.COMPLETED]: {
    text: '支付完成',
    className: 'payment-completed'
  },
  [PaymentStatus.FAILED]: {
    text: '支付失败',
    className: 'payment-failed'
  }
}

// 案件阶段状态配置
export const CASE_STAGE_STATUS_CONFIG = {
  [CaseStageStatus.DELETED]: {
    text: '已删除',
    className: 'stage-deleted'
  },
  [CaseStageStatus.PROCESSING]: {
    text: '进行中',
    className: 'stage-processing'
  },
  [CaseStageStatus.COMPLETED]: {
    text: '已完结',
    className: 'stage-completed'
  }
}

// 订单来源配置
export const ORDER_SOURCE_CONFIG = {
  rescue: '黄金救援',
  optimal: '优配律师',
  customer: '客服'
}

/**
 * 获取订单状态文本
 */
export const getOrderStatusText = (status: number): string => {
  return ORDER_STATUS_CONFIG[status as OrderStatus]?.text || '未知状态'
}

/**
 * 获取订单状态样式类名
 */
export const getOrderStatusClass = (status: number): string => {
  return ORDER_STATUS_CONFIG[status as OrderStatus]?.className || 'status-unknown'
}

/**
 * 获取支付状态文本
 */
export const getPaymentStatusText = (status: number): string => {
  return PAYMENT_STATUS_CONFIG[status as PaymentStatus]?.text || '未知状态'
}

/**
 * 获取支付状态样式类名
 */
export const getPaymentStatusClass = (status: number): string => {
  return PAYMENT_STATUS_CONFIG[status as PaymentStatus]?.className || 'payment-unknown'
}

/**
 * 获取案件阶段状态文本
 */
export const getCaseStageStatusText = (status: number): string => {
  return CASE_STAGE_STATUS_CONFIG[status as CaseStageStatus]?.text || '未知状态'
}

/**
 * 获取案件阶段状态样式类名
 */
export const getCaseStageStatusClass = (status: number): string => {
  return CASE_STAGE_STATUS_CONFIG[status as CaseStageStatus]?.className || 'stage-unknown'
}

/**
 * 获取订单来源文本
 */
export const getOrderSourceText = (source: string): string => {
  return ORDER_SOURCE_CONFIG[source as keyof typeof ORDER_SOURCE_CONFIG] || source || '-'
}

/**
 * 格式化金额
 */
export const formatAmount = (amount?: number): string => {
  if (amount === undefined || amount === null) return '未知'
  if (amount === 0) return '面议'
  return `¥${amount.toLocaleString()}`
}


