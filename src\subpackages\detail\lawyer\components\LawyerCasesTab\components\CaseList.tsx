/**
 * 案例列表组件
 */
import React, { useMemo } from 'react'
import { View, Text, Image } from '@tarojs/components'
import clockIcon from '@/assets/images/common-icon/clock.png'
import eyeIcon from '@/assets/images/common-icon/eye.png'

// 组件属性
export interface CaseListProps {
  // 案例列表数据
  cases: CaseAPI.CaseListInfo[]
  // 是否加载中
  isLoading: boolean
  // 案例点击回调
  onCaseClick: (caseInfo: CaseAPI.CaseListInfo) => void
  // 自定义样式类名
  className?: string
}

const CaseList: React.FC<CaseListProps> = ({
  cases,
  isLoading,
  onCaseClick,
  className = ''
}) => {
  // 使用 useMemo 优化案例列表渲染，避免不必要的重新渲染
  const caseItems = useMemo(() => {
    return cases.map((caseInfo) => (
      <View
        key={caseInfo.id}
        className='lawyer-case-detail-item'
        onClick={() => onCaseClick(caseInfo)}
      >
        <Text className='lawyer-case-detail-item__title'>{caseInfo.title}</Text>
        <View className='lawyer-case-detail-item__meta'>
          <Text className='lawyer-case-detail-item__category'>{caseInfo.categoryName}</Text>
          <Image className='lawyer-case-detail-item__info-icon' src={clockIcon} mode='aspectFit' />
          <Text className='lawyer-case-detail-item__info-text'>{caseInfo.createdAt}</Text>
          <Image className='lawyer-case-detail-item__info-icon' src={eyeIcon} mode='aspectFit' />
          <Text className='lawyer-case-detail-item__info-text'>{caseInfo.viewCount}</Text>
        </View>
      </View>
    ))
  }, [cases, onCaseClick])

  // 渲染内容区域
  const renderContent = () => {
    // 如果有数据，显示列表（即使在加载中也显示，避免闪烁）
    if (cases.length > 0) {
      return (
        <View className={`lawyer-case-detail-list__content ${isLoading ? 'lawyer-case-detail-list__content--loading' : ''}`}>
          {caseItems}
          {/* 加载中时显示半透明遮罩 */}
          {isLoading && (
            <View className='lawyer-case-detail-list__loading-overlay'>
              <Text className='lawyer-case-detail-list__loading-text'>更新中...</Text>
            </View>
          )}
        </View>
      )
    }

    // 如果正在加载且没有数据，显示加载状态
    if (isLoading) {
      return (
        <View className='lawyer-case-detail-list__loading'>
          <Text>加载中...</Text>
        </View>
      )
    }

    // 没有数据且不在加载中，显示空状态
    return (
      <View className='lawyer-case-detail-list__empty'>
        <Text>暂无案例</Text>
      </View>
    )
  }

  return (
    <View className={`lawyer-case-detail-list ${className}`}>
      {renderContent()}
    </View>
  )
}

export default CaseList
