/**
 * 进度编辑弹窗组件样式
 */

.progress-edit-modal {
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;

  &__header {
    padding: 40rpx 32rpx 24rpx;
    border-bottom: 1rpx solid #f0f0f0;
    text-align: center;
  }

  &__title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 8rpx;
    display: block;
  }

  &__subtitle {
    font-size: 26rpx;
    color: #666666;
  }

  &__content {
    flex: 1;
    padding: 32rpx;
    overflow-y: auto;
  }

  &__field {
    margin-bottom: 32rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__label {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 16rpx;
    display: block;
  }

  &__textarea {
    width: 100%;
    min-height: 200rpx;
    padding: 20rpx;
    border: 1rpx solid #e0e0e0;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    background: #fafafa;
    box-sizing: border-box;

    &:focus {
      border-color: #BD8A4F;
      background: #ffffff;
    }
  }

  &__count {
    font-size: 24rpx;
    color: #999999;
    text-align: right;
    margin-top: 8rpx;
    display: block;
  }

  &__actions {
    padding: 24rpx 32rpx 40rpx;
    display: flex;
    gap: 24rpx;
    border-top: 1rpx solid #f0f0f0;
  }

  &__button {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 28rpx;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    transition: all 0.2s ease;

    &--cancel {
      background: #f5f5f5;
      color: #666666;

      &:active {
        background: #e8e8e8;
      }
    }

    &--submit {
      background: #BD8A4F;
      color: #ffffff;

      &:active {
        background: #a67a45;
      }

      &:disabled {
        background: #d0d0d0;
        color: #999999;
      }
    }
  }
}
