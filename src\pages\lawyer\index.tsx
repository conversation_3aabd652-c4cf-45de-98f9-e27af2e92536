import React, { useEffect, useState } from 'react'
import { View, Text, Input, Image, PageContainer } from '@tarojs/components'
import PageLayout, { PageContent } from '@/components/PageLayout'
import LawyerList from '@/components/LawyerList'
import AreaSelect from '@/components/AreaSelect'
import CategoryCacheManager from '@/utils/cache/categoryCache'
import Banner, { BannerItem } from '@/components/Banner'
import { CaseBanner } from '@/constant/image'
import SearchIcon from '@/assets/images/common-icon/search.png'
import MenuIcon from '@/assets/images/common-icon/menu.png'
import ClickMoreIcon from '@/assets/images/common-icon/click_more.png'
import { lawyerLevels } from '@/constant'
import './index.scss'

const Lawyer: React.FC = () => {
  // filterParams
  const [filterParams, setFilterParams] = useState<LawyerAPI.GetLawyerListRequest>({})
  // 搜索关键词状态
  const [searchKeyword, setSearchKeyword] = useState('')
  // 筛选弹窗显示状态
  const [showFilterModal, setShowFilterModal] = useState(false)
  // 筛选条件状态
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null)
  const [selectedLawyerLevel, setSelectedLawyerLevel] = useState<number | null>(null)
  // 案件类型数据状态
  const [caseCategories, setCaseCategories] = useState<Array<{ id: number, name: string }>>([])
  // 案件类型加载状态
  const [categoriesLoading, setCategoriesLoading] = useState(false)
  // 选中的地区
  const [selectedArea, setSelectedArea] = useState('')

  // 获取案件类型数据（使用缓存）
  const loadCaseCategories = async () => {
    try {
      setCategoriesLoading(true)
      const categories = await CategoryCacheManager.getCaseCategoryList()
      setCaseCategories(categories)
    } catch (error) {
      console.error('获取案件类型失败:', error)
      setCaseCategories([])
    } finally {
      setCategoriesLoading(false)
    }
  }

  // 处理搜索输入
  const handleSearchInput = (e: any) => {
    setSearchKeyword(e.detail.value)
  }
  const handleSearchInputConfirm = (e: any) => {
    setSearchKeyword(e.detail.value)
    setFilterParams(prev => ({
      ...prev,
      lawyerName: e.detail.value
    }))
  }

  // 处理搜索图标点击
  const handleSearchIconClick = () => {
    setFilterParams(prev => ({
      ...prev,
      lawyerName: searchKeyword
    }))
  }

  // 处理筛选按钮点击
  const handleFilterClick = () => {
    setShowFilterModal(true)
  }

  // 关闭筛选弹窗
  const handleCloseFilter = () => {
    setSelectedCategoryId(filterParams.categoryId || null)
    setSelectedLawyerLevel(filterParams?.lawyerLevel?.[0] || null)
    setShowFilterModal(false)
  }

  // 处理案件类型选择
  const handleCategorySelect = (categoryId: number) => {
    setSelectedCategoryId(selectedCategoryId === categoryId ? null : categoryId)
  }

  // 处理律师等级选择
  const handleLawyerLevelSelect = (level: number) => {
    setSelectedLawyerLevel(selectedLawyerLevel === level ? null : level)
  }

  // 重置筛选条件
  const handleResetFilter = () => {
    setSelectedCategoryId(null)
    setSelectedLawyerLevel(null)
  }

  // 确认筛选
  const handleConfirmFilter = () => {
    // 这里可以将筛选条件传递给LawyerList组件，保留现有的搜索关键词和地区信息
    setFilterParams(prev => ({
      ...prev,
      categoryId: selectedCategoryId || undefined,
      lawyerLevel: selectedLawyerLevel ? [selectedLawyerLevel as LawyerAPI.LawyerLevel] : undefined
    }))
    setShowFilterModal(false)
  }
  const handleAreaChange = (result: any) => {
    // 只显示最后一级
    const areas = result.areaString.split('/')
    const lastArea = areas[areas.length - 1]
    const nextAreaObj = {
      province: result.province,
      city: result.city,
      district: result?.district
    }
    setSelectedArea(lastArea)
    setFilterParams(prev => ({
      ...prev,
      ...nextAreaObj
    }))
  }
  // 页面生命周期
  useEffect(() => {
    // 加载案件类型数据
    loadCaseCategories()
  }, [])

  const banners: BannerItem[] = [
    {
      id: 'home-banner-1',
      imageUrl: CaseBanner,
      linkUrl: '/subpackages/common/excellent-cases/index'
    }
  ]
  const LeftSolt = () => {
    return (
      <AreaSelect onChange={handleAreaChange}>
        <View className='area-select-trigger'>
          <Text className={`area-select-trigger__text ${!selectedArea ? 'area-select-trigger__text--placeholder' : ''}`}>
            {selectedArea || '全国'}
          </Text>
          <Image className='area-select-trigger__arrow' src={ClickMoreIcon} mode='aspectFit'></Image>
        </View>
      </AreaSelect>
    )
  }
  return (
    <PageLayout
      title='找律师'
      showBack={false}
      backgroundColor='#f8f9fa'
      navBarLeft={LeftSolt()}
    >
      <PageContent padded='b'>
        <Banner
          banners={banners}
          autoplay={false}
          showIndicators={false}
          showDots={false}
          circular={false}
          className='lawyer-banner'
        />
        <View className='lawyer-content'>
          <View className='search-bar'>
            {/* 左侧搜索输入框 */}
            <Input
              className='search-bar__input'
              placeholder='请输入律师姓名'
              value={searchKeyword}
              onInput={handleSearchInput}
              confirmType='search'
              confirmHold
              onConfirm={handleSearchInputConfirm}
              placeholderClass='search-bar__placeholder'
            />
            {/* 中间搜索图标 */}
            <View className='search-bar__left' onClick={handleSearchIconClick}>
              <Image className='search-bar__icon' src={SearchIcon} mode='aspectFit' />
            </View>
            {/* 右侧筛选文字和图标 */}
            <View className='search-bar__right' onClick={handleFilterClick}>
              <Text className='search-bar__text'>筛选</Text>
              <Image className='search-bar__icon' src={MenuIcon} mode='aspectFit' />
            </View>
          </View>

          <View className='lawyer-list-container'>
            <LawyerList filterParams={filterParams} />
          </View>
        </View>

        {/* 筛选弹窗 - 使用PageContainer */}
        <PageContainer
          show={showFilterModal}
          position='bottom'
          round
          overlay
          onClickOverlay={handleCloseFilter}
          onAfterLeave={handleCloseFilter}
        >
          <View className='filter-container'>
            {/* 弹窗头部 */}
            <View className='filter-container__header'>
              <Text className='filter-container__title'>筛选</Text>
              <Text className='filter-container__close' onClick={handleCloseFilter}>×</Text>
            </View>

            {/* 弹窗内容 */}
            <View className='filter-container__body'>
              {/* 案件类型筛选 */}
              <View className='filter-section'>
                <Text className='filter-section__title'>案件类型</Text>
                <View className='filter-section__options'>
                  {categoriesLoading ? (
                    <Text className='filter-loading'>加载中...</Text>
                  ) : caseCategories.length > 0 ? (
                    caseCategories.map((category) => (
                      <Text
                        key={category.id}
                        className={`filter-option ${selectedCategoryId === category.id ? 'filter-option--selected' : ''}`}
                        onClick={() => handleCategorySelect(category.id)}
                      >
                        {category.name}
                      </Text>
                    ))
                  ) : (
                    <Text className='filter-empty'>暂无案件类型</Text>
                  )}
                </View>
              </View>

              {/* 律师等级筛选 */}
              <View className='filter-section'>
                <Text className='filter-section__title'>律师等级</Text>
                <View className='filter-section__options'>
                  {lawyerLevels.map((level) => (
                    <Text
                      key={level.id}
                      className={`filter-option ${selectedLawyerLevel === level.id ? 'filter-option--selected' : ''}`}
                      onClick={() => handleLawyerLevelSelect(level.id)}
                    >
                      {level.name}
                    </Text>
                  ))}
                </View>
              </View>
            </View>

            {/* 弹窗底部操作按钮 */}
            <View className='filter-container__footer'>
              <View className='filter-container__buttons'>
                <Text className='filter-container__reset' onClick={handleResetFilter}>
                  重置
                </Text>
                <Text className='filter-container__confirm' onClick={handleConfirmFilter}>
                  确定
                </Text>
              </View>
            </View>
          </View>
        </PageContainer>
      </PageContent>
    </PageLayout>
  )
}

export default Lawyer
