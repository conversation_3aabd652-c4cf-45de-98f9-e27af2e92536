/**
 * GoldenRescue页面类型定义
 */
import React from 'react'

// 过滤选项类型
export interface FilterOption {
  id: number
  name: string
}

// 表单组件Props类型
export interface GoldenRescueFormProps {
  // 表单数据
  formData: UserAPI.CreateLawEntrustOrderRequest
  // 案件分类列表
  caseCategories: FilterOption[]
  // 案件阶段列表
  caseStages: FilterOption[]
  // 律师列表（优配律师场景使用）
  lawyerList: FilterOption[]
  // 分类加载状态
  categoriesLoading: boolean
  // 阶段加载状态
  stagesLoading: boolean
  // 律师列表加载状态
  lawyersLoading: boolean
  // 提交状态
  submitting: boolean
  // 表单数据变化回调
  onFormDataChange: (data: Partial<UserAPI.CreateLawEntrustOrderRequest>) => void
  // 地区变化回调（用于刷新律师列表）
  onAreaChange: (province: string, city: string, district: string) => void
  // 提交回调
  onSubmit: () => void
  // 页面类型
  pageType: string
}

// 地区选择组件Props类型
export interface AreaSelectFieldProps {
  // 标签文本
  label: string
  // 当前选中的省市区
  province?: string
  city?: string
  district?: string
  // 选择变化回调
  onChange: (province: string, city: string, district: string) => void
  // 是否必填
  required?: boolean
  // 占位符
  placeholder?: string
  // 自定义样式类名
  className?: string
}

// 手机号验证码输入组件Props类型
export interface PhoneCodeInputProps {
  // 手机号值
  phoneValue: string
  // 验证码值
  codeValue: string
  // 手机号变化回调
  onPhoneChange: (value: string) => void
  // 验证码变化回调
  onCodeChange: (value: string) => void
  // 自定义样式类名
  className?: string
}

// 表单字段组件Props类型
export interface FormFieldProps {
  // 标签文本
  label: string
  // 是否必填
  required?: boolean
  // 子组件
  children: React.ReactNode
  // 自定义样式类名
  className?: string
}

// 选择器字段组件Props类型
export interface SelectFieldProps {
  // 标签文本
  label: string
  // 选项列表
  options: FilterOption[]
  // 当前选中值
  value?: number
  // 选择变化回调
  onChange: (value: number) => void
  // 占位符
  placeholder?: string
  // 是否必填
  required?: boolean
  // 加载状态
  loading?: boolean
  // 是否禁用
  disabled?: boolean
  // 自定义样式类名
  className?: string
}
