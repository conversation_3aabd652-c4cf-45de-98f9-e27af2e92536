# LawyerBaseInfo 律师基础信息组件

## 功能描述

展示律师的基础信息，包括头像、姓名、等级、地址、律所和认证信息。

## 组件特性

- 支持律师头像展示（166*166rpx圆形头像）
- 显示律师姓名（黑色粗体，32rpx）
- 展示律师等级图标（1-5级）
- 显示地址信息（省市区）
- 显示律所名称
- 显示平台认证/担保信息
- 响应式布局设计

## 使用方法

```tsx
import React from 'react'
import { LawyerBaseInfo } from '@/components'

const ExamplePage: React.FC = () => {
  const lawyerInfo: LawyerAPI.LawyerInfo = {
    id: 1,
    userId: 123,
    name: '张律师',
    province: '广东省',
    city: '深圳市',
    district: '南山区',
    figurePhotoUrl: 'https://example.com/avatar.jpg',
    lawFirm: '某某律师事务所',
    lawyerLevel: 3,
    personalProfile: '专业律师',
    lawFirmAddress: '深圳市南山区某某大厦',
    lawyerField: ['刑事辩护', '民事诉讼'],
    createdAt: '2024-01-01'
  }

  return (
    <LawyerBaseInfo 
      lawyerInfo={lawyerInfo}
      className="custom-lawyer-info"
    />
  )
}
```

## Props

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| lawyerInfo | LawyerAPI.LawyerInfo | 是 | - | 律师信息对象 |
| className | string | 否 | '' | 自定义样式类名 |

## LawyerAPI.LawyerInfo 类型说明

```typescript
interface LawyerInfo {
  id: number
  userId: number
  name: string              // 律师姓名
  province: string          // 省份
  city: string             // 城市
  district: string         // 区域
  figurePhotoUrl: string   // 头像URL
  lawFirm: string          // 律所名称
  lawyerLevel: LawyerLevel // 律师等级 (0-5)
  personalProfile: string  // 个人简介
  lawFirmAddress: string   // 律所地址
  lawyerField: string[]    // 擅长领域
  createdAt: string        // 创建时间
}
```

## 样式定制

组件提供了完整的CSS类名，可以通过覆盖样式进行定制：

```scss
.lawyer-base-info {
  // 整体容器
  &__avatar {
    // 头像容器
  }
  
  &__avatar-img {
    // 头像图片
  }
  
  &__content {
    // 内容区域
  }
  
  &__name {
    // 律师姓名
  }
  
  &__level-icon {
    // 等级图标
  }
  
  &__address {
    // 地址信息
  }
  
  &__firm {
    // 律所信息
  }
  
  &__verify-text {
    // 认证文字
  }
}
```

## 注意事项

1. 组件会自动处理头像为空的情况，使用默认头像
2. 律师等级为0时不显示等级图标
3. 地址信息会自动过滤空值并用空格连接
4. 组件使用固定的padding（40rpx 20rpx）
5. 认证图标使用固定的verify.png图标
