/**
 * CaseList 组件使用示例
 * 展示如何使用滚动加载功能
 */
import React, { useState, useCallback } from 'react'
import { View } from '@tarojs/components'
import CaseList from './index'
import { caseApi } from '@/apis'
import { STATUS_CODE } from '@/utils/request/config'

const CaseListExample: React.FC = () => {
  // 案例列表数据
  const [cases, setCases] = useState<CaseAPI.CaseListInfo[]>([])
  // 是否还有更多数据
  const [hasMore, setHasMore] = useState(true)
  // 滚动加载状态
  const [scrollLoading, setScrollLoading] = useState(false)
  // 筛选条件
  const [filterParams] = useState<CaseAPI.GetCaseListRequest>({
    categoryId: 1,
    isExcellent: 1
  })

  // 处理滚动加载
  const handleScrollLoad = useCallback(async (page: number) => {
    if (scrollLoading) return

    try {
      setScrollLoading(true)

      const params = {
        page,
        pageSize: 10,
        ...filterParams
      }

      const response = await caseApi.getCaseList(params)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        const newCases = response.data.list || []
        
        if (newCases.length === 0) {
          // 没有更多数据
          setHasMore(false)
        } else {
          // 追加新数据
          setCases(prev => [...prev, ...newCases])
          
          // 如果返回的数据少于请求的数量，说明没有更多数据了
          if (newCases.length < params.pageSize) {
            setHasMore(false)
          }
        }
      }
    } catch (error) {
      console.error('滚动加载失败:', error)
    } finally {
      setScrollLoading(false)
    }
  }, [filterParams, scrollLoading])

  return (
    <View>
      {/* 基础用法 - 不启用滚动加载 */}
      <CaseList
        filterParams={filterParams}
        className='basic-case-list'
      />

      {/* 启用滚动加载功能 */}
      <CaseList
        list={cases}
        enableScrollLoad={true}
        onScrollLoad={handleScrollLoad}
        hasMore={hasMore}
        scrollLoading={scrollLoading}
        className='scroll-case-list'
      />
    </View>
  )
}

export default CaseListExample
