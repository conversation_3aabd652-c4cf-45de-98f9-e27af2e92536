/**
 * CaseList 组件使用示例
 * 展示如何使用滚动加载功能
 */
import React, { useState } from 'react'
import { View } from '@tarojs/components'
import CaseList from './index'

const CaseListExample: React.FC = () => {
  // 外部案例列表数据（用于演示外部传入数据的情况）
  const [externalCases] = useState<CaseAPI.CaseListInfo[]>([])

  // 筛选条件
  const [filterParams] = useState<CaseAPI.GetCaseListRequest>({
    categoryId: 1,
    isExcellent: 1
  })

  return (
    <View>
      {/* 基础用法 - 不启用滚动加载 */}
      <CaseList
        filterParams={filterParams}
        className='basic-case-list'
      />

      {/* 启用滚动加载功能 - 内部自动处理 */}
      <CaseList
        filterParams={filterParams}
        enableScrollLoad={true}
        className='scroll-case-list'
      />

      {/* 外部传入数据 - 不支持滚动加载 */}
      <CaseList
        list={externalCases}
        className='external-case-list'
      />
    </View>
  )
}

export default CaseListExample
