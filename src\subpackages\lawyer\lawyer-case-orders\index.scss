/**
 * 律师案件订单列表页面样式
 */

.lawyer-case-orders {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 200rpx);
  background: #f8f9fa;

  // 搜索框
  .lawyer-case-orders-search {
    padding: 20rpx 30rpx;
    background: #ffffff;
    border-bottom: 1rpx solid #f0f0f0;

    .lawyer-case-orders-search-container {
      display: flex;
      align-items: center;
      background: #f8f9fa;
      border-radius: 50rpx;
      padding: 0 30rpx;
      height: 80rpx;

      .lawyer-case-orders-search-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 20rpx;
        flex-shrink: 0;
      }

      .lawyer-case-orders-search-input {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
        height: 100%;
      }

      .lawyer-case-orders-search-placeholder {
        color: #999999;
        font-size: 28rpx;
      }
    }
  }

  // 列表容器
  .lawyer-case-orders-list-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;

    .lawyer-case-orders-list {
      flex: 1;
      height: 100%;
      padding-top: 0;
    }
  }

  // 加载更多状态
  .lawyer-case-orders-loading-more {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80rpx;
    margin: 20rpx 0;

    text {
      font-size: 28rpx;
      color: #999999;
    }
  }

  // 没有更多数据
  .lawyer-case-orders-no-more {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80rpx;
    margin: 20rpx 0;

    text {
      font-size: 28rpx;
      color: #999999;
    }
  }

  // 空状态
  .lawyer-case-orders-empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400rpx;
    flex-direction: column;

    text {
      font-size: 32rpx;
      color: #999999;
      margin-top: 24rpx;
    }

    // 可以添加空状态图标
    &::before {
      content: '';
      width: 120rpx;
      height: 120rpx;
      background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMyIDhDMTguNzQ1IDggOCAyMC43NDUgOCAzNEM4IDQ3LjI1NSAxOC43NDUgNjAgMzIgNjBDNDUuMjU1IDYwIDU2IDQ3LjI1NSA1NiAzNEM1NiAyMC43NDUgNDUuMjU1IDggMzIgOFpNMzIgNTJDMjMuMTYzIDUyIDE2IDQ0LjgzNyAxNiAzNkMxNiAyNy4xNjMgMjMuMTYzIDIwIDMyIDIwQzQwLjgzNyAyMCA0OCAyNy4xNjMgNDggMzZDNDggNDQuODM3IDQwLjgzNyA1MiAzMiA1MloiIGZpbGw9IiNEOUQ5RDkiLz4KPHBhdGggZD0iTTI4IDI4SDM2VjM2SDI4VjI4WiIgZmlsbD0iI0Q5RDlEOSIvPgo8L3N2Zz4K') no-repeat center;
      background-size: contain;
      opacity: 0.3;
    }
  }
}

