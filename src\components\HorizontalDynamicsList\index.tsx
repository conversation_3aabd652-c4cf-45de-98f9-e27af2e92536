/**
 * 横向动态列表组件
 * 
 * 展示最多5个动态卡片，支持横向滑动，支持外部传入筛选条件
 */
import React, { useState, useEffect } from 'react'
import { View, ScrollView, Text, Image } from '@tarojs/components'
import { dynamicsApi } from '@/utils/request/apis'
import { STATUS_CODE } from '@/utils/request/config'
import { navigateToPage } from '@/utils'
import { formatTime } from '@/utils'
import ClockIcon from '@/assets/images/common-icon/clock.png'
import EyeIcon from '@/assets/images/common-icon/eye.png'
import { HorizontalDynamicsListProps } from './types'
import './index.scss'

const HorizontalDynamicsList: React.FC<HorizontalDynamicsListProps> = ({
  filterParams,
  list,
  className = ''
}) => {
  // 动态列表数据
  const [dynamics, setDynamics] = useState<DynamicsAPI.DynamicsListInfo[]>([])
  // 加载状态
  const [isLoading, setIsLoading] = useState(false)



  // 加载动态列表数据
  const loadDynamics = async (val?: DynamicsAPI.GetDynamicsListRequest) => {
    try {
      setIsLoading(true)
      const params = {
        page: 1,
        pageSize: 5,
        ...val
      }
      const response = await dynamicsApi.getDynamicsList(params)
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        setDynamics(response.data.list || [])
      }
    } catch (error) {
      console.error('加载动态列表失败:', error)
    } finally {
      setIsLoading(false)
    }
  }
  // 处理动态卡片点击 - 跳转到动态详情页
  const handleDynamicsClick = (dynamicsInfo: DynamicsAPI.DynamicsListInfo) => {
    navigateToPage(`/subpackages/detail/dynamics/index?id=${dynamicsInfo.id}`)
  }

  // 使用外部传入的数据
  useEffect(() => {
    if (list) {
      setDynamics(list)
      setIsLoading(false)
    }
  }, [list])

  // 监听filterParams变化，重新加载数据（仅在没有传入list时）
  useEffect(() => {
    if (!list) {
      loadDynamics(filterParams)
    }
  }, [list, filterParams])

  // 显示加载状态
  const showLoading = isLoading

  return (
    <View className={`horizontal-dynamics-list ${className}`}>
      {showLoading ? (
        <View className='horizontal-dynamics-list__loading'>
          <Text>加载中...</Text>
        </View>
      ) : (
        <>
          {dynamics.length > 0 ? (
            <ScrollView
              className='horizontal-dynamics-list__scroll-view'
              scrollX
              showScrollbar={false}
              enhanced
              bounces={false}
            >
              <View className='horizontal-dynamics-list__container'>
                {dynamics.map((dynamicsInfo) => (
                  <View
                    key={dynamicsInfo.id}
                    className='horizontal-dynamics-card'
                    onClick={() => handleDynamicsClick(dynamicsInfo)}
                  >
                    <Text className='horizontal-dynamics-card__title'>{dynamicsInfo.title}</Text>
                    <View className='horizontal-dynamics-card__meta'>
                      <Text className='horizontal-dynamics-card__category'>{dynamicsInfo.categoryName}</Text>
                      <Image src={ClockIcon} mode='aspectFit' className='horizontal-dynamics-card__info-icon' />
                      <Text className='horizontal-dynamics-card__info-text'>{formatTime(dynamicsInfo.createdAt)}</Text>
                      <Image src={EyeIcon} mode='aspectFit' className='horizontal-dynamics-card__info-icon' />
                      <Text className='horizontal-dynamics-card__info-text'>{dynamicsInfo.viewCount}</Text>
                    </View>
                  </View>
                ))}
              </View>
            </ScrollView>
          ) : (
            <View className='horizontal-dynamics-list__empty'>
              <Text className='horizontal-dynamics-list__empty-title'>暂无动态信息</Text>
              <Text className='horizontal-dynamics-list__empty-desc'>当前没有相关动态，请稍后再试</Text>
            </View>
          )}
        </>
      )}
    </View>
  )
}

// 导出组件
export default HorizontalDynamicsList

export type { HorizontalDynamicsListProps } from './types'
