/**
 * 进入下一阶段按钮组件
 */
import React from 'react'
import { View, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import './index.scss'

interface NextStageButtonProps {
  // 当前阶段记录
  stageRecords: LawyerAPI.GetMyCaseOrderStageRecordsResponse
  // 是否可以进入下一阶段
  canProceedToNext: boolean
  // 进入下一阶段回调
  onProceedToNext: () => void
  // 是否正在处理
  loading?: boolean
}

const NextStageButton: React.FC<NextStageButtonProps> = ({
  stageRecords,
  canProceedToNext,
  onProceedToNext,
  loading = false
}) => {

  const currentProgress = stageRecords.progressData?.find(progress => progress.done === 1)

  // 处理进入下一阶段
  const handleProceedToNext = () => {
    Taro.showModal({
      title: '确认提交进度',
      content: `确定要将案件从"${currentProgress?.orderProgressName}"推进到下一进度吗？此操作不可撤销。`,
      confirmText: '确认推进',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          onProceedToNext()
        }
      }
    })
  }

  // 如果当前阶段已完结，不显示按钮
  if (stageRecords.progressData.every(item => item.done === 2)) {
    return null
  }

  return (
    <View className='next-stage-button'>
      <View className='next-stage-button__info'>
        <View className='next-stage-button__stage-name'>
          当前进度：{currentProgress?.orderProgressName}
        </View>
      </View>

      <Button
        className={`next-stage-button__btn ${!canProceedToNext ? 'disabled' : ''}`}
        onClick={handleProceedToNext}
        disabled={!canProceedToNext || loading}
        loading={loading}
      >
        {loading ? '处理中...' : '提交进度'}
      </Button>
    </View>
  )
}

export default NextStageButton
