/**
 * 请求配置文件
 */

// 环境配置
const ENV_CONFIG = {
  development: {
    baseURL: 'https://api.shengzhangyi.com',
    timeout: 10000,
  },
  production: {
    baseURL: 'https://api.shengzhangyi.com',
    timeout: 10000,
  }
}

// 获取当前环境
const getCurrentEnv = () => {
  // 可以根据实际情况调整环境判断逻辑
  return process.env.NODE_ENV === 'production' ? 'production' : 'development'
}

// 当前环境配置
export const REQUEST_CONFIG = ENV_CONFIG[getCurrentEnv()]

// 默认请求头
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
}

// 状态码配置
export const STATUS_CODE = {
  SUCCESS: 200,
  UNAUTHORIZED: 40001,
  FORBIDDEN: 40003,
  NOT_FOUND: 40004,
  SERVER_ERROR: 50000,
  NETWORK_ERROR: -1,
  TIMEOUT: -2,
}

// 错误消息配置
export const ERROR_MESSAGES = {
  [STATUS_CODE.UNAUTHORIZED]: '登录已过期，请重新登录',
  [STATUS_CODE.FORBIDDEN]: '没有权限访问',
  [STATUS_CODE.NOT_FOUND]: '请求的资源不存在',
  [STATUS_CODE.SERVER_ERROR]: '服务器错误，请稍后重试',
  [STATUS_CODE.NETWORK_ERROR]: '网络连接失败，请检查网络',
  [STATUS_CODE.TIMEOUT]: '请求超时，请稍后重试',
  default: '请求失败，请稍后重试'
}

// 需要显示loading的请求方法
export const LOADING_METHODS = ['POST', 'PUT', 'DELETE', 'PATCH']

// 不需要token的接口
export const NO_AUTH_URLS = []
