/**
 * 案例标题和搜索框组件
 */
import React from 'react'
import { View, Text, Input, Image } from '@tarojs/components'
import searchIcon from '@/assets/images/common-icon/search_g.png'

// 组件属性
export interface CasesHeaderProps {
  // 搜索关键词
  searchKeyword: string
  // 搜索输入回调
  onSearchInput: (e: any) => void
  // 搜索确认回调
  onSearchConfirm: (e: any) => void
  // 搜索图标点击回调
  onSearchIconClick: () => void
  // 自定义样式类名
  className?: string
}

const CasesHeader: React.FC<CasesHeaderProps> = ({
  searchKeyword,
  onSearchInput,
  onSearchConfirm,
  onSearchIconClick,
  className = ''
}) => {
  return (
    <View className={`cases-header ${className}`}>
      <Text className='cases-header__title'>亲办案例</Text>
      <View className='cases-header__search'>
        <Image
          className='cases-header__search-icon'
          src={searchIcon}
          mode='aspectFit'
          onClick={onSearchIconClick}
        />
        <Input
          className='cases-header__search-input'
          placeholder='搜索案例'
          value={searchKeyword}
          onInput={onSearchInput}
          onConfirm={onSearchConfirm}
          confirmType='search'
          placeholderClass='cases-header__search-placeholder'
        />
      </View>
    </View>
  )
}

export default CasesHeader
