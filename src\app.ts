import { PropsWithChildren, useEffect } from 'react'
import { useDidShow, useDidHide } from '@tarojs/taro'
import CategoryCacheManager from '@/utils/cache/categoryCache'
import UserCacheManager from '@/utils/cache/userCache'
import './app.scss'

function App({ children }: PropsWithChildren) {
  useEffect(() => {
    // 同步用户数据到缓存
    UserCacheManager.syncUserDataFromStorage()
    // 小程序启动时预加载分类数据
    CategoryCacheManager.preloadAllCategories()
  }, [])

  useDidShow(() => {
    // componentDidShow 逻辑
  })

  useDidHide(() => {
    // componentDidHide 逻辑
  })

  // children 是将要会渲染的页面
  return children
}

export default App

