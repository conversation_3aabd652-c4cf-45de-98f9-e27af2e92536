/**
 * 关注卡片组件
 * 
 * 展示关注的律师信息
 */
import React from 'react'
import { View, Text, Image } from '@tarojs/components'
import { DefaultAvatar } from '@/constant/image'
import { formatTime } from '@/utils'

import './index.scss'

// 组件属性
export interface FollowCardProps {
  // 关注信息
  followInfo: LawyerAPI.OwnerLawyerFollowListInfo
  // 点击回调
  onClick?: (followInfo: LawyerAPI.OwnerLawyerFollowListInfo) => void
  // 自定义样式类名
  className?: string
}

const FollowCard: React.FC<FollowCardProps> = ({
  followInfo,
  onClick,
  className = ''
}) => {
  // 处理点击事件
  const handleClick = () => {
    onClick?.(followInfo)
  }

  return (
    <View
      className={`follow-card ${className}`}
      onClick={handleClick}
    >
      {/* 律师头像 */}
      <View className='follow-card__avatar'>
        <Image
          src={followInfo.lawyerPhotoUrl || DefaultAvatar}
          className='follow-card__avatar-img'
          mode='aspectFill'
        />
      </View>

      {/* 律师信息 */}
      <View className='follow-card__info'>
        <Text className='follow-card__name'>{followInfo.lawyerName}</Text>
        <Text className='follow-card__time'>关注时间：{formatTime(followInfo.createdAt)}</Text>
      </View>
    </View>
  )
}

export default FollowCard
