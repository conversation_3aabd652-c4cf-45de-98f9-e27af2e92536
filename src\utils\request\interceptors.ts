/**
 * 请求拦截器
 */
import Taro from '@tarojs/taro'
import UserCacheManager from '@/utils/cache/userCache'
import { RequestConfig, BaseResponse, RequestError } from '@/apis/types'
import { STATUS_CODE, ERROR_MESSAGES, NO_AUTH_URLS } from './config'

/**
 * 请求拦截器
 */
export const requestInterceptor = (config: RequestConfig): RequestConfig => {
  // 添加token
  if (!config.skipAuth && !NO_AUTH_URLS.some(url => config.url.includes(url))) {
    const token = Taro.getStorageSync('token')
    if (token) {
      config.header = {
        ...config.header,
        'Authorization': `Bearer ${token}`
      }
    }
  }
  return config
}

/**
 * 响应拦截器
 */
export const responseInterceptor = <T,>(response: BaseResponse<T>): BaseResponse<T> => {
  // 统一处理响应数据格式
  if (response.code === STATUS_CODE.SUCCESS) {
    return response
  }

  // 处理特殊状态码
  if (response.code === STATUS_CODE.UNAUTHORIZED) {
    // 清除所有用户相关缓存
    UserCacheManager.clearUserCache()
  }

  throw new Error(response.message || ERROR_MESSAGES.default)
}

/**
 * 错误拦截器
 */
export const errorInterceptor = (error: any): RequestError => {
  let requestError: RequestError

  if (error.errMsg) {
    // Taro请求错误
    if (error.errMsg.includes('timeout')) {
      requestError = {
        code: STATUS_CODE.TIMEOUT,
        message: ERROR_MESSAGES[STATUS_CODE.TIMEOUT]
      }
    } else if (error.errMsg.includes('fail')) {
      requestError = {
        code: STATUS_CODE.NETWORK_ERROR,
        message: ERROR_MESSAGES[STATUS_CODE.NETWORK_ERROR]
      }
    } else {
      requestError = {
        code: error.statusCode || STATUS_CODE.NETWORK_ERROR,
        message: ERROR_MESSAGES[error.statusCode] || ERROR_MESSAGES.default,
        statusCode: error.statusCode
      }
    }
  } else if (error.message) {
    // 业务错误
    requestError = {
      code: -1,
      message: error.message
    }
  } else {
    // 未知错误
    requestError = {
      code: -1,
      message: ERROR_MESSAGES.default
    }
  }

  return requestError
}


