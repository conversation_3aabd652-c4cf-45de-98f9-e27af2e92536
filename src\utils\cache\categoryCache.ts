/**
 * 分类数据缓存管理器
 * 专门管理案例分类、动态分类等不经常变化的数据
 */
import { commonApi } from '@/utils/request/apis/common'
import { STATUS_CODE } from '@/utils/request/config'
import CacheManager, { CACHE_KEYS } from './index'
// 分类数据类型
export interface CategoryItem {
  id: number
  name: string
  [key: string]: any
}

class CategoryCacheManager {
  /**
   * 获取案例分类列表（带缓存）
   * @param forceRefresh 是否强制刷新
   * @returns 案例分类列表
   */
  static async getCaseCategoryList(forceRefresh = false): Promise<CategoryItem[]> {
    const cacheKey = CACHE_KEYS.CASE_CATEGORY_LIST

    // 如果不强制刷新，先尝试从缓存获取
    if (!forceRefresh) {
      const cachedData = CacheManager.getCache<CategoryItem[]>(cacheKey)
      if (cachedData) {
        return cachedData
      }
    }

    try {
      const response = await commonApi.getCaseCategoryList()

      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        // 缓存数据（永久缓存，直到小程序重启）
        CacheManager.setCache(cacheKey, response.data.list || [])
        return response.data.list || []
      } else {
        throw new Error(response.message || '获取案例分类失败')
      }
    } catch (error) {
      console.error('获取案例分类列表失败:', error)

      // 如果请求失败，尝试返回缓存数据
      const cachedData = CacheManager.getCache<CategoryItem[]>(cacheKey)
      if (cachedData) {
        console.log('使用缓存的案例分类数据')
        return cachedData
      }

      // 如果没有缓存数据，返回空数组
      return []
    }
  }

  /**
   * 获取动态分类列表（带缓存）
   * @param forceRefresh 是否强制刷新
   * @returns 动态分类列表
   */
  static async getDynamicsCategoryList(forceRefresh = false): Promise<CategoryItem[]> {
    const cacheKey = CACHE_KEYS.DYNAMICS_CATEGORY_LIST

    // 如果不强制刷新，先尝试从缓存获取
    if (!forceRefresh) {
      const cachedData = CacheManager.getCache<CategoryItem[]>(cacheKey)
      if (cachedData) {
        return cachedData
      }
    }

    try {
      const response = await commonApi.getDynamicsCategoryList()

      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        // 缓存数据（永久缓存，直到小程序重启）
        CacheManager.setCache(cacheKey, response.data.list || [])
        return response.data.list || []
      } else {
        throw new Error(response.message || '获取动态分类失败')
      }
    } catch (error) {
      console.error('获取动态分类列表失败:', error)

      // 如果请求失败，尝试返回缓存数据
      const cachedData = CacheManager.getCache<CategoryItem[]>(cacheKey)
      if (cachedData) {
        console.log('使用缓存的动态分类数据')
        return cachedData
      }

      // 如果没有缓存数据，返回空数组
      return []
    }
  }

  /**
   * 获取案件阶段列表（带缓存）
   * @param forceRefresh 是否强制刷新
   * @returns 案件阶段列表
   */
  static async getCaseStageList(forceRefresh = false): Promise<CategoryItem[]> {
    const cacheKey = CACHE_KEYS.CASE_STAGE_LIST

    // 如果不强制刷新，先尝试从缓存获取
    if (!forceRefresh) {
      const cachedData = CacheManager.getCache<CategoryItem[]>(cacheKey)
      if (cachedData) {
        return cachedData
      }
    }

    try {
      const response = await commonApi.getCaseStageList()

      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        // 转换数据格式，将 stageName 映射为 name
        const mappedData = (response.data.list || []).map(item => ({
          id: item.id,
          name: item.stageName
        }))
        // 缓存数据（永久缓存，直到小程序重启）
        CacheManager.setCache(cacheKey, mappedData)
        return mappedData
      } else {
        throw new Error(response.message || '获取案件阶段失败')
      }
    } catch (error) {
      console.error('获取案件阶段列表失败:', error)

      // 如果请求失败，尝试返回缓存数据
      const cachedData = CacheManager.getCache<CategoryItem[]>(cacheKey)
      if (cachedData) {
        console.log('使用缓存的案件阶段数据')
        return cachedData
      }

      // 如果没有缓存数据，返回空数组
      return []
    }
  }

  /**
   * 预加载常用分类数据和配置信息
   * 在小程序启动时调用，每次启动都发起真实请求更新缓存
   */
  static async preloadAllCategories(): Promise<void> {
    try {
      // 每次小程序启动都强制刷新缓存，确保数据是最新的
      await Promise.all([
        this.getCaseCategoryList(true), // 强制刷新
        this.getDynamicsCategoryList(true), // 强制刷新
        this.getCaseStageList(true), // 强制刷新
        this.getMiniConfig(true) // 强制刷新小程序配置
      ])
    } catch (error) {
      console.error('分类数据和配置信息预加载失败:', error)
    }
  }

  /**
   * 刷新常用分类数据和配置信息
   * 强制重新请求案例分类、动态分类和小程序配置并更新缓存
   */
  static async refreshAllCategories(): Promise<void> {
    try {
      await Promise.all([
        this.getCaseCategoryList(true),
        this.getDynamicsCategoryList(true),
        this.getCaseStageList(true),
        this.getMiniConfig(true)
      ])
    } catch (error) {
      console.error('刷新分类数据和配置信息失败:', error)
    }
  }

  /**
   * 获取小程序配置信息（带缓存）
   * @param forceRefresh 是否强制刷新
   * @returns 小程序配置信息
   */
  static async getMiniConfig(forceRefresh = false): Promise<CommonAPI.MiniConfig | null> {
    const cacheKey = CACHE_KEYS.MINI_CONFIG

    // 如果不强制刷新，先尝试从缓存获取
    if (!forceRefresh) {
      const cachedData = CacheManager.getCache<CommonAPI.MiniConfig>(cacheKey)
      if (cachedData) {
        return cachedData
      }
    }

    try {
      const response = await commonApi.getMiniConfig()
      if (response.code === STATUS_CODE.SUCCESS && response.data) {
        const configData = JSON.parse(response.data.configDataStr)
        // 缓存数据（永久缓存，直到小程序重启）
        CacheManager.setCache(cacheKey, configData)
        return configData
      } else {
        throw new Error(response.message || '获取小程序配置失败')
      }
    } catch (error) {
      console.error('获取小程序配置失败:', error)

      // 如果请求失败，尝试返回缓存数据
      const cachedData = CacheManager.getCache<CommonAPI.MiniConfig>(cacheKey)
      if (cachedData) {
        console.log('使用缓存的小程序配置数据')
        return cachedData
      }

      // 如果没有缓存数据，返回null
      return null
    }
  }

  /**
   * 清空常用分类缓存
   */
  static clearAllCategoryCache(): void {
    CacheManager.removeCache(CACHE_KEYS.CASE_CATEGORY_LIST)
    CacheManager.removeCache(CACHE_KEYS.DYNAMICS_CATEGORY_LIST)
    CacheManager.removeCache(CACHE_KEYS.CASE_STAGE_LIST)
    CacheManager.removeCache(CACHE_KEYS.MINI_CONFIG)
  }

  /**
   * 获取缓存状态信息
   * 用于调试和监控缓存状态
   */
  static getCacheStatus(): { [key: string]: boolean } {
    const status = {
      caseCategory: CacheManager.hasCache(CACHE_KEYS.CASE_CATEGORY_LIST),
      dynamicsCategory: CacheManager.hasCache(CACHE_KEYS.DYNAMICS_CATEGORY_LIST),
      caseStage: CacheManager.hasCache(CACHE_KEYS.CASE_STAGE_LIST),
      miniConfig: CacheManager.hasCache(CACHE_KEYS.MINI_CONFIG)
    }
    return status
  }
}

export default CategoryCacheManager
