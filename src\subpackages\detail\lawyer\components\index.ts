/**
 * 律师详情页面组件导出
 */

// 导出组件
export { default as LawyerAvatar } from './LawyerAvatar'
export { default as LawyerTabs } from './LawyerTabs'
export { default as LawyerProfile } from './LawyerProfile'
export { default as LawyerFields } from './LawyerFields'
export { default as LawyerFirm } from './LawyerFirm'
export { default as LawyerStatsCard } from './LawyerStatsCard'
export { default as LawyerArticleStats } from './LawyerArticleStats'
export { default as LawyerArticleList } from './LawyerArticleList'
export { default as LawyerArticleTab } from './LawyerArticleTab'
export { default as LawyerInfoTab } from './LawyerInfoTab'
export { default as LawyerDynamicsTab } from './LawyerDynamicsTab'
export { default as LawyerCasesTab } from './LawyerCasesTab'

// 导出组件类型
export type { LawyerAvatarProps } from './LawyerAvatar'
export type { LawyerTabsProps, TabType } from './LawyerTabs'
export type { LawyerProfileProps } from './LawyerProfile'
export type { LawyerFieldsProps } from './LawyerFields'
export type { LawyerFirmProps } from './LawyerFirm'
export type { LawyerStatsCardProps } from './LawyerStatsCard'
export type { LawyerArticleStatsProps } from './LawyerArticleStats'
export type { LawyerArticleListProps } from './LawyerArticleList'
export type { LawyerArticleTabProps } from './LawyerArticleTab'
export type { LawyerInfoTabProps } from './LawyerInfoTab'
export type { LawyerDynamicsTabProps } from './LawyerDynamicsTab'
export type { LawyerCasesTabProps } from './LawyerCasesTab'
