/**
 * 律师详情页面组件导出
 */

// 导出组件
export { default as LawyerAvatar } from './LawyerAvatar'
export { default as LawyerTabs } from './LawyerTabs'
export { default as LawyerArticleTab } from './LawyerArticleTab'
export { default as LawyerInfoTab } from './LawyerInfoTab'
export { default as LawyerDynamicsTab } from './LawyerDynamicsTab'
export { default as LawyerCasesTab } from './LawyerCasesTab'

// 导出组件类型
export type { LawyerAvatarProps } from './LawyerAvatar'
export type { LawyerTabsProps, TabType } from './LawyerTabs'
export type { LawyerArticleTabProps } from './LawyerArticleTab'
export type { LawyerInfoTabProps } from './LawyerInfoTab'
export type { LawyerDynamicsTabProps } from './LawyerDynamicsTab'
export type { LawyerCasesTabProps } from './LawyerCasesTab'