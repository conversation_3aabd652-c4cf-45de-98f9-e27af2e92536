/**
 * 律师页面样式
 */
.area-select-trigger {
  display: flex;
  align-items: center;
  gap: 10rpx;

  &__text {
    font-size: 28rpx;
    color: #333;
    white-space: nowrap;
    max-width: 140rpx; // 限制最大宽度，约5个文字（28rpx * 5 = 140rpx）
    overflow: hidden;
    text-overflow: ellipsis;

    &--placeholder {
      color: #999;
    }
  }

  &__arrow {
    width: 44rpx;
    height: 44rpx;
  }
}

.lawyer-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;

  .lawyer-list-container {
    padding: 0 30rpx;
  }
}


