.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);

  .login-content {
    padding: 0;

    // 页面标题
    .page-header {
      text-align: center;
      padding: 60rpx 32rpx 40rpx;

      .header-title {
        font-size: 48rpx;
        font-weight: 700;
        color: #333333;
        margin-bottom: 16rpx;
        letter-spacing: 2rpx;
      }

      .header-subtitle {
        font-size: 28rpx;
        color: #666666;
        font-weight: 400;
      }
    }

    // 角色选择卡片
    .role-selector {
      display: flex;
      gap: 24rpx;
      padding: 0 32rpx;
      margin-bottom: 40rpx;

      .role-card {
        flex: 1;
        background: #ffffff;
        border-radius: 20rpx;
        padding: 40rpx 20rpx;
        text-align: center;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
        border: 3rpx solid transparent;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        // 背景装饰
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(189, 138, 79, 0.02) 0%, rgba(189, 138, 79, 0.05) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .role-icon {
          margin-bottom: 20rpx;
          transition: transform 0.3s ease;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 80rpx;

          .role-icon-img {
            width: 60rpx;
            height: 60rpx;
            transition: transform 0.3s ease;
          }
        }

        .role-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333333;
          margin-bottom: 12rpx;
          transition: color 0.3s ease;
        }

        .role-desc {
          font-size: 24rpx;
          color: #999999;
          transition: color 0.3s ease;
        }

        // 悬停效果
        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
        }

        // 激活状态
        &--active {
          border-color: #BD8A4F;
          box-shadow: 0 8rpx 32rpx rgba(189, 138, 79, 0.2);
          transform: translateY(-4rpx);

          &::before {
            opacity: 1;
          }

          .role-icon {
            transform: scale(1.1);

            .role-icon-img {
              transform: scale(1.1);
            }
          }

          .role-title {
            color: #BD8A4F;
          }

          .role-desc {
            color: #BD8A4F;
          }
        }
      }
    }

    // 登录内容区域
    .login-container {
      min-height: 500rpx;
      background: #ffffff;
      margin: 0 32rpx;
      border-radius: 24rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
      animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;
    }
  }
}

// 内容切换动画
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}